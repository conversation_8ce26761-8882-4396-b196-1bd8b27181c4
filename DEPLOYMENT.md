# Deployment Instructions

This portfolio is a React application located in the `portfolio-react` directory.

## Local Development

```bash
cd portfolio-react
npm install
npm start
```

The app will run on `http://localhost:3000`

## Building for Production

```bash
cd portfolio-react
npm run build
```

This creates a `build` folder with optimized production files.

## Deployment Options

### 1. Netlify
- Connect your GitHub repository to Netlify
- Set build command: `cd portfolio-react && npm ci && npm run build`
- Set publish directory: `portfolio-react/build`
- The `netlify.toml` file is already configured

### 2. Vercel
- Connect your GitHub repository to Vercel
- The `vercel.json` file is already configured
- Vercel will automatically detect the React app

### 3. GitHub Pages
- Enable GitHub Pages in repository settings
- The GitHub Actions workflow will automatically deploy on push to main
- Set source to "GitHub Actions"

### 4. Manual Deployment
- Build the project: `cd portfolio-react && npm run build`
- Upload the contents of `portfolio-react/build` to your web server

## Important Notes

- The React app is in the `portfolio-react` subdirectory, not the root
- Make sure your deployment platform builds from the correct directory
- The app uses React Router, so you need proper redirect rules for SPA routing
- All deployment configurations are included in this repository

## Troubleshooting

If your deployed version differs from local:
1. Clear deployment cache
2. Trigger a new build
3. Check that the build command points to `portfolio-react` directory
4. Verify that the latest commit is being deployed
