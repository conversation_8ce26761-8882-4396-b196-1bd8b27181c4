[build]
  # Build command
  command = "cd portfolio-react && npm ci && npm run build"
  
  # Directory to publish (relative to root of your repo)
  publish = "portfolio-react/build"
  
  # Base directory for build
  base = "/"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"
  
  # Disable CI warnings
  CI = "false"

# Redirect rules for React Router
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for better performance
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
