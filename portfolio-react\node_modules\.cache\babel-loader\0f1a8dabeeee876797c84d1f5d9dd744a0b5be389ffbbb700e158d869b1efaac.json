{"ast": null, "code": "import { e as elementChildren, c as createElement } from './utils.mjs';\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, \".\".concat(checkProps[key]))[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\nexport { createElementIfNotDefined as c };", "map": {"version": 3, "names": ["e", "elementChildren", "c", "createElement", "createElementIfNotDefined", "swiper", "originalParams", "params", "checkProps", "createElements", "Object", "keys", "for<PERSON>ach", "key", "auto", "element", "el", "concat", "className", "append"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/shared/create-element-if-not-defined.mjs"], "sourcesContent": ["import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,QAAQ,aAAa;AAEtE,SAASC,yBAAyBA,CAACC,MAAM,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAE;EAC7E,IAAIH,MAAM,CAACE,MAAM,CAACE,cAAc,EAAE;IAChCC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;MACrC,IAAI,CAACN,MAAM,CAACM,GAAG,CAAC,IAAIN,MAAM,CAACO,IAAI,KAAK,IAAI,EAAE;QACxC,IAAIC,OAAO,GAAGd,eAAe,CAACI,MAAM,CAACW,EAAE,MAAAC,MAAA,CAAMT,UAAU,CAACK,GAAG,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,CAACE,OAAO,EAAE;UACZA,OAAO,GAAGZ,aAAa,CAAC,KAAK,EAAEK,UAAU,CAACK,GAAG,CAAC,CAAC;UAC/CE,OAAO,CAACG,SAAS,GAAGV,UAAU,CAACK,GAAG,CAAC;UACnCR,MAAM,CAACW,EAAE,CAACG,MAAM,CAACJ,OAAO,CAAC;QAC3B;QACAR,MAAM,CAACM,GAAG,CAAC,GAAGE,OAAO;QACrBT,cAAc,CAACO,GAAG,CAAC,GAAGE,OAAO;MAC/B;IACF,CAAC,CAAC;EACJ;EACA,OAAOR,MAAM;AACf;AAEA,SAASH,yBAAyB,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}