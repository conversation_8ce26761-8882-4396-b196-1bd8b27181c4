{"ast": null, "code": "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: \"\".concat(pfx, \"-bullet\"),\n      bulletActiveClass: \"\".concat(pfx, \"-bullet-active\"),\n      modifierClass: \"\".concat(pfx, \"-\"),\n      currentClass: \"\".concat(pfx, \"-current\"),\n      totalClass: \"\".concat(pfx, \"-total\"),\n      hiddenClass: \"\".concat(pfx, \"-hidden\"),\n      progressbarFillClass: \"\".concat(pfx, \"-progressbar-fill\"),\n      progressbarOppositeClass: \"\".concat(pfx, \"-progressbar-opposite\"),\n      clickableClass: \"\".concat(pfx, \"-clickable\"),\n      lockClass: \"\".concat(pfx, \"-lock\"),\n      horizontalClass: \"\".concat(pfx, \"-horizontal\"),\n      verticalClass: \"\".concat(pfx, \"-vertical\"),\n      paginationDisabledClass: \"\".concat(pfx, \"-disabled\")\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[\"\".concat(position === 'prev' ? 'previous' : 'next', \"ElementSibling\")];\n    if (bulletEl) {\n      bulletEl.classList.add(\"\".concat(bulletActiveClass, \"-\").concat(position));\n      bulletEl = bulletEl[\"\".concat(position === 'prev' ? 'previous' : 'next', \"ElementSibling\")];\n      if (bulletEl) {\n        bulletEl.classList.add(\"\".concat(bulletActiveClass, \"-\").concat(position, \"-\").concat(position));\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = \"\".concat(bulletSize * (params.dynamicMainBullets + 4), \"px\");\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => \"\".concat(params.bulletActiveClass).concat(suffix))].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...\"\".concat(params.bulletActiveClass, \"-main\").split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...\"\".concat(params.bulletActiveClass, \"-main\").split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = \"\".concat(bulletsOffset, \"px\");\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = \"translate3d(0,0,0) scaleX(\".concat(scaleX, \") scaleY(\").concat(scaleY, \")\");\n          progressEl.style.transitionDuration = \"\".concat(swiper.params.speed, \"ms\");\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += \"<\".concat(params.bulletElement, \" \").concat(swiper.isElement ? 'part=\"bullet\"' : '', \" class=\\\"\").concat(params.bulletClass, \"\\\"></\").concat(params.bulletElement, \">\");\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = \"<span class=\\\"\".concat(params.currentClass, \"\\\"></span>\") + ' / ' + \"<span class=\\\"\".concat(params.totalClass, \"\\\"></span>\");\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = \"<span class=\\\"\".concat(params.progressbarFillClass, \"\\\"></span>\");\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(\"\".concat(params.modifierClass).concat(params.type, \"-dynamic\"));\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\nexport { Pagination as default };", "map": {"version": 3, "names": ["c", "classesToSelector", "createElementIfNotDefined", "m", "makeElementsArray", "h", "elementOuterSize", "i", "elementIndex", "s", "setInnerHTML", "b", "elementParents", "Pagination", "_ref", "swiper", "extendParams", "on", "emit", "pfx", "pagination", "el", "bulletElement", "clickable", "hideOnClick", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "type", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "concat", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "hiddenClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "lockClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "bulletSize", "dynamicBulletIndex", "isPaginationDisabled", "params", "Array", "isArray", "length", "setSideBullets", "bulletEl", "position", "classList", "add", "getMoveDirection", "prevIndex", "nextIndex", "onBulletClick", "e", "target", "closest", "preventDefault", "index", "slidesPerGroup", "loop", "realIndex", "moveDirection", "slides", "slideNext", "slidePrev", "slideToLoop", "slideTo", "update", "rtl", "current", "previousIndex", "<PERSON><PERSON><PERSON><PERSON>", "virtual", "enabled", "total", "Math", "ceil", "snapGrid", "previousRealIndex", "floor", "snapIndex", "previousSnapIndex", "activeIndex", "firstIndex", "lastIndex", "midIndex", "isHorizontal", "for<PERSON>ach", "subEl", "style", "undefined", "max", "min", "classesToRemove", "map", "suffix", "includes", "split", "flat", "remove", "bullet", "bulletIndex", "isElement", "setAttribute", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "querySelectorAll", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "transform", "transitionDuration", "speed", "watchOverflow", "isLocked", "render", "grid", "rows", "paginationHTML", "numberOfBullets", "freeMode", "call", "push", "init", "originalParams", "querySelector", "document", "uniqueNavElements", "find", "Object", "assign", "addEventListener", "destroy", "removeEventListener", "disable", "_s", "targetEl", "contains", "navigation", "nextEl", "prevEl", "isHidden", "toggle", "enable", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/pagination.mjs"], "sourcesContent": ["import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,iBAAiB,QAAQ,mCAAmC;AAC1E,SAASD,CAAC,IAAIE,yBAAyB,QAAQ,6CAA6C;AAC5F,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAE9I,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,GAAG,GAAG,mBAAmB;EAC/BH,YAAY,CAAC;IACXI,UAAU,EAAE;MACVC,EAAE,EAAE,IAAI;MACRC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,IAAI,EAAE,SAAS;MACf;MACAC,cAAc,EAAE,KAAK;MACrBC,kBAAkB,EAAE,CAAC;MACrBC,qBAAqB,EAAEC,MAAM,IAAIA,MAAM;MACvCC,mBAAmB,EAAED,MAAM,IAAIA,MAAM;MACrCE,WAAW,KAAAC,MAAA,CAAKlB,GAAG,YAAS;MAC5BmB,iBAAiB,KAAAD,MAAA,CAAKlB,GAAG,mBAAgB;MACzCoB,aAAa,KAAAF,MAAA,CAAKlB,GAAG,MAAG;MACxBqB,YAAY,KAAAH,MAAA,CAAKlB,GAAG,aAAU;MAC9BsB,UAAU,KAAAJ,MAAA,CAAKlB,GAAG,WAAQ;MAC1BuB,WAAW,KAAAL,MAAA,CAAKlB,GAAG,YAAS;MAC5BwB,oBAAoB,KAAAN,MAAA,CAAKlB,GAAG,sBAAmB;MAC/CyB,wBAAwB,KAAAP,MAAA,CAAKlB,GAAG,0BAAuB;MACvD0B,cAAc,KAAAR,MAAA,CAAKlB,GAAG,eAAY;MAClC2B,SAAS,KAAAT,MAAA,CAAKlB,GAAG,UAAO;MACxB4B,eAAe,KAAAV,MAAA,CAAKlB,GAAG,gBAAa;MACpC6B,aAAa,KAAAX,MAAA,CAAKlB,GAAG,cAAW;MAChC8B,uBAAuB,KAAAZ,MAAA,CAAKlB,GAAG;IACjC;EACF,CAAC,CAAC;EACFJ,MAAM,CAACK,UAAU,GAAG;IAClBC,EAAE,EAAE,IAAI;IACR6B,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU;EACd,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,SAASC,oBAAoBA,CAAA,EAAG;IAC9B,OAAO,CAACtC,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACC,EAAE,IAAI,CAACN,MAAM,CAACK,UAAU,CAACC,EAAE,IAAIkC,KAAK,CAACC,OAAO,CAACzC,MAAM,CAACK,UAAU,CAACC,EAAE,CAAC,IAAIN,MAAM,CAACK,UAAU,CAACC,EAAE,CAACoC,MAAM,KAAK,CAAC;EAC1I;EACA,SAASC,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC1C,MAAM;MACJtB;IACF,CAAC,GAAGvB,MAAM,CAACuC,MAAM,CAAClC,UAAU;IAC5B,IAAI,CAACuC,QAAQ,EAAE;IACfA,QAAQ,GAAGA,QAAQ,IAAAtB,MAAA,CAAIuB,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM,oBAAiB;IACjF,IAAID,QAAQ,EAAE;MACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,IAAAzB,MAAA,CAAIC,iBAAiB,OAAAD,MAAA,CAAIuB,QAAQ,CAAE,CAAC;MAC1DD,QAAQ,GAAGA,QAAQ,IAAAtB,MAAA,CAAIuB,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM,oBAAiB;MACjF,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,IAAAzB,MAAA,CAAIC,iBAAiB,OAAAD,MAAA,CAAIuB,QAAQ,OAAAvB,MAAA,CAAIuB,QAAQ,CAAE,CAAC;MACxE;IACF;EACF;EACA,SAASG,gBAAgBA,CAACC,SAAS,EAAEC,SAAS,EAAER,MAAM,EAAE;IACtDO,SAAS,GAAGA,SAAS,GAAGP,MAAM;IAC9BQ,SAAS,GAAGA,SAAS,GAAGR,MAAM;IAC9B,IAAIQ,SAAS,KAAKD,SAAS,GAAG,CAAC,EAAE;MAC/B,OAAO,MAAM;IACf,CAAC,MAAM,IAAIC,SAAS,KAAKD,SAAS,GAAG,CAAC,EAAE;MACtC,OAAO,UAAU;IACnB;IACA;EACF;EACA,SAASE,aAAaA,CAACC,CAAC,EAAE;IACxB,MAAMR,QAAQ,GAAGQ,CAAC,CAACC,MAAM,CAACC,OAAO,CAACpE,iBAAiB,CAACc,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACgB,WAAW,CAAC,CAAC;IAC1F,IAAI,CAACuB,QAAQ,EAAE;MACb;IACF;IACAQ,CAAC,CAACG,cAAc,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG/D,YAAY,CAACmD,QAAQ,CAAC,GAAG5C,MAAM,CAACuC,MAAM,CAACkB,cAAc;IACnE,IAAIzD,MAAM,CAACuC,MAAM,CAACmB,IAAI,EAAE;MACtB,IAAI1D,MAAM,CAAC2D,SAAS,KAAKH,KAAK,EAAE;MAChC,MAAMI,aAAa,GAAGZ,gBAAgB,CAAChD,MAAM,CAAC2D,SAAS,EAAEH,KAAK,EAAExD,MAAM,CAAC6D,MAAM,CAACnB,MAAM,CAAC;MACrF,IAAIkB,aAAa,KAAK,MAAM,EAAE;QAC5B5D,MAAM,CAAC8D,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIF,aAAa,KAAK,UAAU,EAAE;QACvC5D,MAAM,CAAC+D,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM;QACL/D,MAAM,CAACgE,WAAW,CAACR,KAAK,CAAC;MAC3B;IACF,CAAC,MAAM;MACLxD,MAAM,CAACiE,OAAO,CAACT,KAAK,CAAC;IACvB;EACF;EACA,SAASU,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMC,GAAG,GAAGnE,MAAM,CAACmE,GAAG;IACtB,MAAM5B,MAAM,GAAGvC,MAAM,CAACuC,MAAM,CAAClC,UAAU;IACvC,IAAIiC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,IAAIhC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1B;IACA,IAAI8D,OAAO;IACX,IAAIC,aAAa;IACjB,MAAMC,YAAY,GAAGtE,MAAM,CAACuE,OAAO,IAAIvE,MAAM,CAACuC,MAAM,CAACgC,OAAO,CAACC,OAAO,GAAGxE,MAAM,CAACuE,OAAO,CAACV,MAAM,CAACnB,MAAM,GAAG1C,MAAM,CAAC6D,MAAM,CAACnB,MAAM;IAC1H,MAAM+B,KAAK,GAAGzE,MAAM,CAACuC,MAAM,CAACmB,IAAI,GAAGgB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGtE,MAAM,CAACuC,MAAM,CAACkB,cAAc,CAAC,GAAGzD,MAAM,CAAC4E,QAAQ,CAAClC,MAAM;IAClH,IAAI1C,MAAM,CAACuC,MAAM,CAACmB,IAAI,EAAE;MACtBW,aAAa,GAAGrE,MAAM,CAAC6E,iBAAiB,IAAI,CAAC;MAC7CT,OAAO,GAAGpE,MAAM,CAACuC,MAAM,CAACkB,cAAc,GAAG,CAAC,GAAGiB,IAAI,CAACI,KAAK,CAAC9E,MAAM,CAAC2D,SAAS,GAAG3D,MAAM,CAACuC,MAAM,CAACkB,cAAc,CAAC,GAAGzD,MAAM,CAAC2D,SAAS;IAC7H,CAAC,MAAM,IAAI,OAAO3D,MAAM,CAAC+E,SAAS,KAAK,WAAW,EAAE;MAClDX,OAAO,GAAGpE,MAAM,CAAC+E,SAAS;MAC1BV,aAAa,GAAGrE,MAAM,CAACgF,iBAAiB;IAC1C,CAAC,MAAM;MACLX,aAAa,GAAGrE,MAAM,CAACqE,aAAa,IAAI,CAAC;MACzCD,OAAO,GAAGpE,MAAM,CAACiF,WAAW,IAAI,CAAC;IACnC;IACA;IACA,IAAI1C,MAAM,CAACxB,IAAI,KAAK,SAAS,IAAIf,MAAM,CAACK,UAAU,CAAC8B,OAAO,IAAInC,MAAM,CAACK,UAAU,CAAC8B,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MAClG,MAAMP,OAAO,GAAGnC,MAAM,CAACK,UAAU,CAAC8B,OAAO;MACzC,IAAI+C,UAAU;MACd,IAAIC,SAAS;MACb,IAAIC,QAAQ;MACZ,IAAI7C,MAAM,CAACvB,cAAc,EAAE;QACzBoB,UAAU,GAAG7C,gBAAgB,CAAC4C,OAAO,CAAC,CAAC,CAAC,EAAEnC,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,EAAE,IAAI,CAAC;QAC3F/E,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAI;UAClBA,KAAK,CAACC,KAAK,CAACxF,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,MAAA/D,MAAA,CAAMc,UAAU,IAAIG,MAAM,CAACtB,kBAAkB,GAAG,CAAC,CAAC,OAAI;QAC/G,CAAC,CAAC;QACF,IAAIsB,MAAM,CAACtB,kBAAkB,GAAG,CAAC,IAAIoD,aAAa,KAAKoB,SAAS,EAAE;UAChEpD,kBAAkB,IAAI+B,OAAO,IAAIC,aAAa,IAAI,CAAC,CAAC;UACpD,IAAIhC,kBAAkB,GAAGE,MAAM,CAACtB,kBAAkB,GAAG,CAAC,EAAE;YACtDoB,kBAAkB,GAAGE,MAAM,CAACtB,kBAAkB,GAAG,CAAC;UACpD,CAAC,MAAM,IAAIoB,kBAAkB,GAAG,CAAC,EAAE;YACjCA,kBAAkB,GAAG,CAAC;UACxB;QACF;QACA6C,UAAU,GAAGR,IAAI,CAACgB,GAAG,CAACtB,OAAO,GAAG/B,kBAAkB,EAAE,CAAC,CAAC;QACtD8C,SAAS,GAAGD,UAAU,IAAIR,IAAI,CAACiB,GAAG,CAACxD,OAAO,CAACO,MAAM,EAAEH,MAAM,CAACtB,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAClFmE,QAAQ,GAAG,CAACD,SAAS,GAAGD,UAAU,IAAI,CAAC;MACzC;MACA/C,OAAO,CAACmD,OAAO,CAAC1C,QAAQ,IAAI;QAC1B,MAAMgD,eAAe,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAACC,GAAG,CAACC,MAAM,OAAAxE,MAAA,CAAOiB,MAAM,CAAChB,iBAAiB,EAAAD,MAAA,CAAGwE,MAAM,CAAE,CAAC,CAAC,CAACD,GAAG,CAACnG,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACqG,QAAQ,CAAC,GAAG,CAAC,GAAGrG,CAAC,CAACsG,KAAK,CAAC,GAAG,CAAC,GAAGtG,CAAC,CAAC,CAACuG,IAAI,CAAC,CAAC;QAC3NrD,QAAQ,CAACE,SAAS,CAACoD,MAAM,CAAC,GAAGN,eAAe,CAAC;MAC/C,CAAC,CAAC;MACF,IAAItF,EAAE,CAACoC,MAAM,GAAG,CAAC,EAAE;QACjBP,OAAO,CAACmD,OAAO,CAACa,MAAM,IAAI;UACxB,MAAMC,WAAW,GAAG3G,YAAY,CAAC0G,MAAM,CAAC;UACxC,IAAIC,WAAW,KAAKhC,OAAO,EAAE;YAC3B+B,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAGR,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;UAC9D,CAAC,MAAM,IAAIhG,MAAM,CAACqG,SAAS,EAAE;YAC3BF,MAAM,CAACG,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;UACvC;UACA,IAAI/D,MAAM,CAACvB,cAAc,EAAE;YACzB,IAAIoF,WAAW,IAAIlB,UAAU,IAAIkB,WAAW,IAAIjB,SAAS,EAAE;cACzDgB,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAG,GAAAzB,MAAA,CAAGiB,MAAM,CAAChB,iBAAiB,WAAQyE,KAAK,CAAC,GAAG,CAAC,CAAC;YACxE;YACA,IAAII,WAAW,KAAKlB,UAAU,EAAE;cAC9BvC,cAAc,CAACwD,MAAM,EAAE,MAAM,CAAC;YAChC;YACA,IAAIC,WAAW,KAAKjB,SAAS,EAAE;cAC7BxC,cAAc,CAACwD,MAAM,EAAE,MAAM,CAAC;YAChC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMA,MAAM,GAAGhE,OAAO,CAACiC,OAAO,CAAC;QAC/B,IAAI+B,MAAM,EAAE;UACVA,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAGR,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9D;QACA,IAAIhG,MAAM,CAACqG,SAAS,EAAE;UACpBlE,OAAO,CAACmD,OAAO,CAAC,CAAC1C,QAAQ,EAAEwD,WAAW,KAAK;YACzCxD,QAAQ,CAAC0D,YAAY,CAAC,MAAM,EAAEF,WAAW,KAAKhC,OAAO,GAAG,eAAe,GAAG,QAAQ,CAAC;UACrF,CAAC,CAAC;QACJ;QACA,IAAI7B,MAAM,CAACvB,cAAc,EAAE;UACzB,MAAMuF,oBAAoB,GAAGpE,OAAO,CAAC+C,UAAU,CAAC;UAChD,MAAMsB,mBAAmB,GAAGrE,OAAO,CAACgD,SAAS,CAAC;UAC9C,KAAK,IAAI3F,CAAC,GAAG0F,UAAU,EAAE1F,CAAC,IAAI2F,SAAS,EAAE3F,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI2C,OAAO,CAAC3C,CAAC,CAAC,EAAE;cACd2C,OAAO,CAAC3C,CAAC,CAAC,CAACsD,SAAS,CAACC,GAAG,CAAC,GAAG,GAAAzB,MAAA,CAAGiB,MAAM,CAAChB,iBAAiB,WAAQyE,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5E;UACF;UACArD,cAAc,CAAC4D,oBAAoB,EAAE,MAAM,CAAC;UAC5C5D,cAAc,CAAC6D,mBAAmB,EAAE,MAAM,CAAC;QAC7C;MACF;MACA,IAAIjE,MAAM,CAACvB,cAAc,EAAE;QACzB,MAAMyF,oBAAoB,GAAG/B,IAAI,CAACiB,GAAG,CAACxD,OAAO,CAACO,MAAM,EAAEH,MAAM,CAACtB,kBAAkB,GAAG,CAAC,CAAC;QACpF,MAAMyF,aAAa,GAAG,CAACtE,UAAU,GAAGqE,oBAAoB,GAAGrE,UAAU,IAAI,CAAC,GAAGgD,QAAQ,GAAGhD,UAAU;QAClG,MAAMuE,UAAU,GAAGxC,GAAG,GAAG,OAAO,GAAG,MAAM;QACzChC,OAAO,CAACmD,OAAO,CAACa,MAAM,IAAI;UACxBA,MAAM,CAACX,KAAK,CAACxF,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAGsB,UAAU,GAAG,KAAK,CAAC,MAAArF,MAAA,CAAMoF,aAAa,OAAI;QACjF,CAAC,CAAC;MACJ;IACF;IACApG,EAAE,CAACgF,OAAO,CAAC,CAACC,KAAK,EAAEqB,UAAU,KAAK;MAChC,IAAIrE,MAAM,CAACxB,IAAI,KAAK,UAAU,EAAE;QAC9BwE,KAAK,CAACsB,gBAAgB,CAAC3H,iBAAiB,CAACqD,MAAM,CAACd,YAAY,CAAC,CAAC,CAAC6D,OAAO,CAACwB,UAAU,IAAI;UACnFA,UAAU,CAACC,WAAW,GAAGxE,MAAM,CAACrB,qBAAqB,CAACkD,OAAO,GAAG,CAAC,CAAC;QACpE,CAAC,CAAC;QACFmB,KAAK,CAACsB,gBAAgB,CAAC3H,iBAAiB,CAACqD,MAAM,CAACb,UAAU,CAAC,CAAC,CAAC4D,OAAO,CAAC0B,OAAO,IAAI;UAC9EA,OAAO,CAACD,WAAW,GAAGxE,MAAM,CAACnB,mBAAmB,CAACqD,KAAK,CAAC;QACzD,CAAC,CAAC;MACJ;MACA,IAAIlC,MAAM,CAACxB,IAAI,KAAK,aAAa,EAAE;QACjC,IAAIkG,oBAAoB;QACxB,IAAI1E,MAAM,CAACzB,mBAAmB,EAAE;UAC9BmG,oBAAoB,GAAGjH,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG,UAAU,GAAG,YAAY;QAC1E,CAAC,MAAM;UACL4B,oBAAoB,GAAGjH,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,UAAU;QAC1E;QACA,MAAM6B,KAAK,GAAG,CAAC9C,OAAO,GAAG,CAAC,IAAIK,KAAK;QACnC,IAAI0C,MAAM,GAAG,CAAC;QACd,IAAIC,MAAM,GAAG,CAAC;QACd,IAAIH,oBAAoB,KAAK,YAAY,EAAE;UACzCE,MAAM,GAAGD,KAAK;QAChB,CAAC,MAAM;UACLE,MAAM,GAAGF,KAAK;QAChB;QACA3B,KAAK,CAACsB,gBAAgB,CAAC3H,iBAAiB,CAACqD,MAAM,CAACX,oBAAoB,CAAC,CAAC,CAAC0D,OAAO,CAAC+B,UAAU,IAAI;UAC3FA,UAAU,CAAC7B,KAAK,CAAC8B,SAAS,gCAAAhG,MAAA,CAAgC6F,MAAM,eAAA7F,MAAA,CAAY8F,MAAM,MAAG;UACrFC,UAAU,CAAC7B,KAAK,CAAC+B,kBAAkB,MAAAjG,MAAA,CAAMtB,MAAM,CAACuC,MAAM,CAACiF,KAAK,OAAI;QAClE,CAAC,CAAC;MACJ;MACA,IAAIjF,MAAM,CAACxB,IAAI,KAAK,QAAQ,IAAIwB,MAAM,CAAC1B,YAAY,EAAE;QACnDlB,YAAY,CAAC4F,KAAK,EAAEhD,MAAM,CAAC1B,YAAY,CAACb,MAAM,EAAEoE,OAAO,GAAG,CAAC,EAAEK,KAAK,CAAC,CAAC;QACpE,IAAImC,UAAU,KAAK,CAAC,EAAEzG,IAAI,CAAC,kBAAkB,EAAEoF,KAAK,CAAC;MACvD,CAAC,MAAM;QACL,IAAIqB,UAAU,KAAK,CAAC,EAAEzG,IAAI,CAAC,kBAAkB,EAAEoF,KAAK,CAAC;QACrDpF,IAAI,CAAC,kBAAkB,EAAEoF,KAAK,CAAC;MACjC;MACA,IAAIvF,MAAM,CAACuC,MAAM,CAACkF,aAAa,IAAIzH,MAAM,CAACwE,OAAO,EAAE;QACjDe,KAAK,CAACzC,SAAS,CAAC9C,MAAM,CAAC0H,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACnF,MAAM,CAACR,SAAS,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EACA,SAAS4F,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMpF,MAAM,GAAGvC,MAAM,CAACuC,MAAM,CAAClC,UAAU;IACvC,IAAIiC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,MAAMgC,YAAY,GAAGtE,MAAM,CAACuE,OAAO,IAAIvE,MAAM,CAACuC,MAAM,CAACgC,OAAO,CAACC,OAAO,GAAGxE,MAAM,CAACuE,OAAO,CAACV,MAAM,CAACnB,MAAM,GAAG1C,MAAM,CAAC4H,IAAI,IAAI5H,MAAM,CAACuC,MAAM,CAACqF,IAAI,CAACC,IAAI,GAAG,CAAC,GAAG7H,MAAM,CAAC6D,MAAM,CAACnB,MAAM,GAAGgC,IAAI,CAACC,IAAI,CAAC3E,MAAM,CAACuC,MAAM,CAACqF,IAAI,CAACC,IAAI,CAAC,GAAG7H,MAAM,CAAC6D,MAAM,CAACnB,MAAM;IACnO,IAAIpC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1B,IAAIwH,cAAc,GAAG,EAAE;IACvB,IAAIvF,MAAM,CAACxB,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAIgH,eAAe,GAAG/H,MAAM,CAACuC,MAAM,CAACmB,IAAI,GAAGgB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGtE,MAAM,CAACuC,MAAM,CAACkB,cAAc,CAAC,GAAGzD,MAAM,CAAC4E,QAAQ,CAAClC,MAAM;MAC1H,IAAI1C,MAAM,CAACuC,MAAM,CAACyF,QAAQ,IAAIhI,MAAM,CAACuC,MAAM,CAACyF,QAAQ,CAACxD,OAAO,IAAIuD,eAAe,GAAGzD,YAAY,EAAE;QAC9FyD,eAAe,GAAGzD,YAAY;MAChC;MACA,KAAK,IAAI9E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,eAAe,EAAEvI,CAAC,IAAI,CAAC,EAAE;QAC3C,IAAI+C,MAAM,CAAC7B,YAAY,EAAE;UACvBoH,cAAc,IAAIvF,MAAM,CAAC7B,YAAY,CAACuH,IAAI,CAACjI,MAAM,EAAER,CAAC,EAAE+C,MAAM,CAAClB,WAAW,CAAC;QAC3E,CAAC,MAAM;UACL;UACAyG,cAAc,QAAAxG,MAAA,CAAQiB,MAAM,CAAChC,aAAa,OAAAe,MAAA,CAAItB,MAAM,CAACqG,SAAS,GAAG,eAAe,GAAG,EAAE,eAAA/E,MAAA,CAAWiB,MAAM,CAAClB,WAAW,WAAAC,MAAA,CAAOiB,MAAM,CAAChC,aAAa,MAAG;QAClJ;MACF;IACF;IACA,IAAIgC,MAAM,CAACxB,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIwB,MAAM,CAAC3B,cAAc,EAAE;QACzBkH,cAAc,GAAGvF,MAAM,CAAC3B,cAAc,CAACqH,IAAI,CAACjI,MAAM,EAAEuC,MAAM,CAACd,YAAY,EAAEc,MAAM,CAACb,UAAU,CAAC;MAC7F,CAAC,MAAM;QACLoG,cAAc,GAAG,iBAAAxG,MAAA,CAAgBiB,MAAM,CAACd,YAAY,kBAAc,KAAK,oBAAAH,MAAA,CAAmBiB,MAAM,CAACb,UAAU,eAAW;MACxH;IACF;IACA,IAAIa,MAAM,CAACxB,IAAI,KAAK,aAAa,EAAE;MACjC,IAAIwB,MAAM,CAAC5B,iBAAiB,EAAE;QAC5BmH,cAAc,GAAGvF,MAAM,CAAC5B,iBAAiB,CAACsH,IAAI,CAACjI,MAAM,EAAEuC,MAAM,CAACX,oBAAoB,CAAC;MACrF,CAAC,MAAM;QACLkG,cAAc,oBAAAxG,MAAA,CAAmBiB,MAAM,CAACX,oBAAoB,eAAW;MACzE;IACF;IACA5B,MAAM,CAACK,UAAU,CAAC8B,OAAO,GAAG,EAAE;IAC9B7B,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAI;MAClB,IAAIhD,MAAM,CAACxB,IAAI,KAAK,QAAQ,EAAE;QAC5BpB,YAAY,CAAC4F,KAAK,EAAEuC,cAAc,IAAI,EAAE,CAAC;MAC3C;MACA,IAAIvF,MAAM,CAACxB,IAAI,KAAK,SAAS,EAAE;QAC7Bf,MAAM,CAACK,UAAU,CAAC8B,OAAO,CAAC+F,IAAI,CAAC,GAAG3C,KAAK,CAACsB,gBAAgB,CAAC3H,iBAAiB,CAACqD,MAAM,CAAClB,WAAW,CAAC,CAAC,CAAC;MAClG;IACF,CAAC,CAAC;IACF,IAAIkB,MAAM,CAACxB,IAAI,KAAK,QAAQ,EAAE;MAC5BZ,IAAI,CAAC,kBAAkB,EAAEG,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EACA,SAAS6H,IAAIA,CAAA,EAAG;IACdnI,MAAM,CAACuC,MAAM,CAAClC,UAAU,GAAGlB,yBAAyB,CAACa,MAAM,EAAEA,MAAM,CAACoI,cAAc,CAAC/H,UAAU,EAAEL,MAAM,CAACuC,MAAM,CAAClC,UAAU,EAAE;MACvHC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,MAAMiC,MAAM,GAAGvC,MAAM,CAACuC,MAAM,CAAClC,UAAU;IACvC,IAAI,CAACkC,MAAM,CAACjC,EAAE,EAAE;IAChB,IAAIA,EAAE;IACN,IAAI,OAAOiC,MAAM,CAACjC,EAAE,KAAK,QAAQ,IAAIN,MAAM,CAACqG,SAAS,EAAE;MACrD/F,EAAE,GAAGN,MAAM,CAACM,EAAE,CAAC+H,aAAa,CAAC9F,MAAM,CAACjC,EAAE,CAAC;IACzC;IACA,IAAI,CAACA,EAAE,IAAI,OAAOiC,MAAM,CAACjC,EAAE,KAAK,QAAQ,EAAE;MACxCA,EAAE,GAAG,CAAC,GAAGgI,QAAQ,CAACzB,gBAAgB,CAACtE,MAAM,CAACjC,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,CAACA,EAAE,EAAE;MACPA,EAAE,GAAGiC,MAAM,CAACjC,EAAE;IAChB;IACA,IAAI,CAACA,EAAE,IAAIA,EAAE,CAACoC,MAAM,KAAK,CAAC,EAAE;IAC5B,IAAI1C,MAAM,CAACuC,MAAM,CAACgG,iBAAiB,IAAI,OAAOhG,MAAM,CAACjC,EAAE,KAAK,QAAQ,IAAIkC,KAAK,CAACC,OAAO,CAACnC,EAAE,CAAC,IAAIA,EAAE,CAACoC,MAAM,GAAG,CAAC,EAAE;MAC1GpC,EAAE,GAAG,CAAC,GAAGN,MAAM,CAACM,EAAE,CAACuG,gBAAgB,CAACtE,MAAM,CAACjC,EAAE,CAAC,CAAC;MAC/C;MACA,IAAIA,EAAE,CAACoC,MAAM,GAAG,CAAC,EAAE;QACjBpC,EAAE,GAAGA,EAAE,CAACkI,IAAI,CAACjD,KAAK,IAAI;UACpB,IAAI1F,cAAc,CAAC0F,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAKvF,MAAM,CAACM,EAAE,EAAE,OAAO,KAAK;UACnE,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;IACF;IACA,IAAIkC,KAAK,CAACC,OAAO,CAACnC,EAAE,CAAC,IAAIA,EAAE,CAACoC,MAAM,KAAK,CAAC,EAAEpC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IACpDmI,MAAM,CAACC,MAAM,CAAC1I,MAAM,CAACK,UAAU,EAAE;MAC/BC;IACF,CAAC,CAAC;IACFA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAI;MAClB,IAAIhD,MAAM,CAACxB,IAAI,KAAK,SAAS,IAAIwB,MAAM,CAAC/B,SAAS,EAAE;QACjD+E,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC,GAAG,CAACR,MAAM,CAACT,cAAc,IAAI,EAAE,EAAEkE,KAAK,CAAC,GAAG,CAAC,CAAC;MAClE;MACAT,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACf,aAAa,GAAGe,MAAM,CAACxB,IAAI,CAAC;MACvDwE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC/C,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;MAC1F,IAAIM,MAAM,CAACxB,IAAI,KAAK,SAAS,IAAIwB,MAAM,CAACvB,cAAc,EAAE;QACtDuE,KAAK,CAACzC,SAAS,CAACC,GAAG,IAAAzB,MAAA,CAAIiB,MAAM,CAACf,aAAa,EAAAF,MAAA,CAAGiB,MAAM,CAACxB,IAAI,aAAU,CAAC;QACpEsB,kBAAkB,GAAG,CAAC;QACtB,IAAIE,MAAM,CAACtB,kBAAkB,GAAG,CAAC,EAAE;UACjCsB,MAAM,CAACtB,kBAAkB,GAAG,CAAC;QAC/B;MACF;MACA,IAAIsB,MAAM,CAACxB,IAAI,KAAK,aAAa,IAAIwB,MAAM,CAACzB,mBAAmB,EAAE;QAC/DyE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACV,wBAAwB,CAAC;MACtD;MACA,IAAIU,MAAM,CAAC/B,SAAS,EAAE;QACpB+E,KAAK,CAACoD,gBAAgB,CAAC,OAAO,EAAExF,aAAa,CAAC;MAChD;MACA,IAAI,CAACnD,MAAM,CAACwE,OAAO,EAAE;QACnBe,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACR,SAAS,CAAC;MACvC;IACF,CAAC,CAAC;EACJ;EACA,SAAS6G,OAAOA,CAAA,EAAG;IACjB,MAAMrG,MAAM,GAAGvC,MAAM,CAACuC,MAAM,CAAClC,UAAU;IACvC,IAAIiC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,IAAIhC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7B,IAAIA,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAI;QAClBA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACZ,WAAW,CAAC;QAC1C4D,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACf,aAAa,GAAGe,MAAM,CAACxB,IAAI,CAAC;QAC1DwE,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAClG,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;QAC7F,IAAIM,MAAM,CAAC/B,SAAS,EAAE;UACpB+E,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC,GAAG,CAAC3D,MAAM,CAACT,cAAc,IAAI,EAAE,EAAEkE,KAAK,CAAC,GAAG,CAAC,CAAC;UACnET,KAAK,CAACsD,mBAAmB,CAAC,OAAO,EAAE1F,aAAa,CAAC;QACnD;MACF,CAAC,CAAC;IACJ;IACA,IAAInD,MAAM,CAACK,UAAU,CAAC8B,OAAO,EAAEnC,MAAM,CAACK,UAAU,CAAC8B,OAAO,CAACmD,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC,GAAG3D,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3I;EACA9F,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAI,CAACF,MAAM,CAACK,UAAU,IAAI,CAACL,MAAM,CAACK,UAAU,CAACC,EAAE,EAAE;IACjD,MAAMiC,MAAM,GAAGvC,MAAM,CAACuC,MAAM,CAAClC,UAAU;IACvC,IAAI;MACFC;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrBC,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACP,eAAe,EAAEO,MAAM,CAACN,aAAa,CAAC;MACpEsD,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC/C,MAAM,CAACqF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EACF/B,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACmE,OAAO,KAAK,KAAK,EAAE;MAC9C;MACAsE,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLX,IAAI,CAAC,CAAC;MACNR,MAAM,CAAC,CAAC;MACRzD,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACFhE,EAAE,CAAC,mBAAmB,EAAE,MAAM;IAC5B,IAAI,OAAOF,MAAM,CAAC+E,SAAS,KAAK,WAAW,EAAE;MAC3Cb,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACFhE,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1BgE,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACFhE,EAAE,CAAC,sBAAsB,EAAE,MAAM;IAC/ByH,MAAM,CAAC,CAAC;IACRzD,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACFhE,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB0I,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACF1I,EAAE,CAAC,gBAAgB,EAAE,MAAM;IACzB,IAAI;MACFI;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAAC9C,MAAM,CAACwE,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAACxE,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAAC0B,SAAS,CAAC,CAAC;IAC7G;EACF,CAAC,CAAC;EACF7B,EAAE,CAAC,aAAa,EAAE,MAAM;IACtBgE,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACFhE,EAAE,CAAC,OAAO,EAAE,CAAC6I,EAAE,EAAE3F,CAAC,KAAK;IACrB,MAAM4F,QAAQ,GAAG5F,CAAC,CAACC,MAAM;IACzB,MAAM/C,EAAE,GAAGjB,iBAAiB,CAACW,MAAM,CAACK,UAAU,CAACC,EAAE,CAAC;IAClD,IAAIN,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACC,EAAE,IAAIN,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACI,WAAW,IAAIH,EAAE,IAAIA,EAAE,CAACoC,MAAM,GAAG,CAAC,IAAI,CAACsG,QAAQ,CAAClG,SAAS,CAACmG,QAAQ,CAACjJ,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACgB,WAAW,CAAC,EAAE;MACpK,IAAIrB,MAAM,CAACkJ,UAAU,KAAKlJ,MAAM,CAACkJ,UAAU,CAACC,MAAM,IAAIH,QAAQ,KAAKhJ,MAAM,CAACkJ,UAAU,CAACC,MAAM,IAAInJ,MAAM,CAACkJ,UAAU,CAACE,MAAM,IAAIJ,QAAQ,KAAKhJ,MAAM,CAACkJ,UAAU,CAACE,MAAM,CAAC,EAAE;MACnK,MAAMC,QAAQ,GAAG/I,EAAE,CAAC,CAAC,CAAC,CAACwC,SAAS,CAACmG,QAAQ,CAACjJ,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACsB,WAAW,CAAC;MAC/E,IAAI0H,QAAQ,KAAK,IAAI,EAAE;QACrBlJ,IAAI,CAAC,gBAAgB,CAAC;MACxB,CAAC,MAAM;QACLA,IAAI,CAAC,gBAAgB,CAAC;MACxB;MACAG,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACwG,MAAM,CAACtJ,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAACsB,WAAW,CAAC,CAAC;IACnF;EACF,CAAC,CAAC;EACF,MAAM4H,MAAM,GAAGA,CAAA,KAAM;IACnBvJ,MAAM,CAACM,EAAE,CAACwC,SAAS,CAACoD,MAAM,CAAClG,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAAC6B,uBAAuB,CAAC;IAC5E,IAAI;MACF5B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAClG,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAAC6B,uBAAuB,CAAC,CAAC;IAC/F;IACAiG,IAAI,CAAC,CAAC;IACNR,MAAM,CAAC,CAAC;IACRzD,MAAM,CAAC,CAAC;EACV,CAAC;EACD,MAAM4E,OAAO,GAAGA,CAAA,KAAM;IACpB9I,MAAM,CAACM,EAAE,CAACwC,SAAS,CAACC,GAAG,CAAC/C,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAAC6B,uBAAuB,CAAC;IACzE,IAAI;MACF5B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAACgF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC/C,MAAM,CAACuC,MAAM,CAAClC,UAAU,CAAC6B,uBAAuB,CAAC,CAAC;IAC5F;IACA0G,OAAO,CAAC,CAAC;EACX,CAAC;EACDH,MAAM,CAACC,MAAM,CAAC1I,MAAM,CAACK,UAAU,EAAE;IAC/BkJ,MAAM;IACNT,OAAO;IACPnB,MAAM;IACNzD,MAAM;IACNiE,IAAI;IACJS;EACF,CAAC,CAAC;AACJ;AAEA,SAAS9I,UAAU,IAAI0J,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}