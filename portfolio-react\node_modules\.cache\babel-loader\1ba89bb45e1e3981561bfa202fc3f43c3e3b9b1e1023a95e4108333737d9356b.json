{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jobsData}from'../data/jobsData';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Experience=()=>{const handleCompanyLinkClick=e=>{// Prevent the parent Link from being triggered when clicking the company link\ne.stopPropagation();};return/*#__PURE__*/_jsxs(\"section\",{className:\"experience\",id:\"experience\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Professional Experience\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"timeline\",children:[jobsData.map((job,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"timeline-dot\"}),/*#__PURE__*/_jsx(Link,{to:\"/job/\".concat(job.slug),className:\"timeline-content-link\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"timeline-content\",children:[/*#__PURE__*/_jsx(\"img\",{src:job.logo,alt:job.logoAlt,className:\"company-logo\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"job-title\",children:job.title}),/*#__PURE__*/_jsx(\"h4\",{className:\"company-name\",children:job.company}),job.companyLink&&/*#__PURE__*/_jsx(\"p\",{className:\"company-link\",children:/*#__PURE__*/_jsx(\"a\",{href:job.companyLink,target:\"_blank\",rel:\"noopener noreferrer\",onClick:handleCompanyLinkClick,children:job.companyLink})}),/*#__PURE__*/_jsx(\"p\",{className:\"job-duration\",children:job.duration}),/*#__PURE__*/_jsx(\"p\",{className:\"job-description\",children:job.summary}),/*#__PURE__*/_jsx(\"div\",{className:\"view-details\",children:/*#__PURE__*/_jsx(\"span\",{children:\"View Details \\u2192\"})})]})})]},job.id)),jobsData.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"timeline-item\",children:/*#__PURE__*/_jsx(\"div\",{className:\"timeline-content\",children:/*#__PURE__*/_jsx(\"p\",{children:\"No jobs found in jobsData.js\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{textAlign:'center',marginTop:'40px',color:'rgba(255,255,255,0.6)',fontSize:'14px'},children:[\"Currently showing \",jobsData.length,\" job\",jobsData.length!==1?'s':'',\" from jobsData.js\"]})]});};export default Experience;", "map": {"version": 3, "names": ["React", "Link", "jobsData", "jsx", "_jsx", "jsxs", "_jsxs", "Experience", "handleCompanyLinkClick", "e", "stopPropagation", "className", "id", "children", "map", "job", "index", "to", "concat", "slug", "src", "logo", "alt", "logoAlt", "title", "company", "companyLink", "href", "target", "rel", "onClick", "duration", "summary", "length", "style", "textAlign", "marginTop", "color", "fontSize"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\n\nconst Experience = () => {\n  const handleCompanyLinkClick = (e) => {\n    // Prevent the parent Link from being triggered when clicking the company link\n    e.stopPropagation();\n  };\n\n  return (\n    <section className=\"experience\" id=\"experience\">\n      <h2>Professional Experience</h2>\n      <div className=\"timeline\">\n        {/* Dynamic timeline content - automatically shows all jobs from jobsData.js */}\n        {jobsData.map((job, index) => (\n          <div key={job.id} className=\"timeline-item\">\n            <div className=\"timeline-dot\"></div>\n            <Link to={`/job/${job.slug}`} className=\"timeline-content-link\">\n              <div className=\"timeline-content\">\n                <img\n                  src={job.logo}\n                  alt={job.logoAlt}\n                  className=\"company-logo\"\n                />\n                <h3 className=\"job-title\">{job.title}</h3>\n                <h4 className=\"company-name\">{job.company}</h4>\n                {job.companyLink && (\n                  <p className=\"company-link\">\n                    <a\n                      href={job.companyLink}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      onClick={handleCompanyLinkClick}\n                    >\n                      {job.companyLink}\n                    </a>\n                  </p>\n                )}\n                <p className=\"job-duration\">{job.duration}</p>\n                <p className=\"job-description\">{job.summary}</p>\n                <div className=\"view-details\">\n                  <span>View Details →</span>\n                </div>\n              </div>\n            </Link>\n          </div>\n        ))}\n\n        {/* Debug info - shows how many jobs are loaded */}\n        {jobsData.length === 0 && (\n          <div className=\"timeline-item\">\n            <div className=\"timeline-content\">\n              <p>No jobs found in jobsData.js</p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Info for developers */}\n      <div style={{ textAlign: 'center', marginTop: '40px', color: 'rgba(255,255,255,0.6)', fontSize: '14px' }}>\n        Currently showing {jobsData.length} job{jobsData.length !== 1 ? 's' : ''} from jobsData.js\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,QAAQ,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,KAAM,CAAAC,sBAAsB,CAAIC,CAAC,EAAK,CACpC;AACAA,CAAC,CAACC,eAAe,CAAC,CAAC,CACrB,CAAC,CAED,mBACEJ,KAAA,YAASK,SAAS,CAAC,YAAY,CAACC,EAAE,CAAC,YAAY,CAAAC,QAAA,eAC7CT,IAAA,OAAAS,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChCP,KAAA,QAAKK,SAAS,CAAC,UAAU,CAAAE,QAAA,EAEtBX,QAAQ,CAACY,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,gBACvBV,KAAA,QAAkBK,SAAS,CAAC,eAAe,CAAAE,QAAA,eACzCT,IAAA,QAAKO,SAAS,CAAC,cAAc,CAAM,CAAC,cACpCP,IAAA,CAACH,IAAI,EAACgB,EAAE,SAAAC,MAAA,CAAUH,GAAG,CAACI,IAAI,CAAG,CAACR,SAAS,CAAC,uBAAuB,CAAAE,QAAA,cAC7DP,KAAA,QAAKK,SAAS,CAAC,kBAAkB,CAAAE,QAAA,eAC/BT,IAAA,QACEgB,GAAG,CAAEL,GAAG,CAACM,IAAK,CACdC,GAAG,CAAEP,GAAG,CAACQ,OAAQ,CACjBZ,SAAS,CAAC,cAAc,CACzB,CAAC,cACFP,IAAA,OAAIO,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAEE,GAAG,CAACS,KAAK,CAAK,CAAC,cAC1CpB,IAAA,OAAIO,SAAS,CAAC,cAAc,CAAAE,QAAA,CAAEE,GAAG,CAACU,OAAO,CAAK,CAAC,CAC9CV,GAAG,CAACW,WAAW,eACdtB,IAAA,MAAGO,SAAS,CAAC,cAAc,CAAAE,QAAA,cACzBT,IAAA,MACEuB,IAAI,CAAEZ,GAAG,CAACW,WAAY,CACtBE,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBC,OAAO,CAAEtB,sBAAuB,CAAAK,QAAA,CAE/BE,GAAG,CAACW,WAAW,CACf,CAAC,CACH,CACJ,cACDtB,IAAA,MAAGO,SAAS,CAAC,cAAc,CAAAE,QAAA,CAAEE,GAAG,CAACgB,QAAQ,CAAI,CAAC,cAC9C3B,IAAA,MAAGO,SAAS,CAAC,iBAAiB,CAAAE,QAAA,CAAEE,GAAG,CAACiB,OAAO,CAAI,CAAC,cAChD5B,IAAA,QAAKO,SAAS,CAAC,cAAc,CAAAE,QAAA,cAC3BT,IAAA,SAAAS,QAAA,CAAM,qBAAc,CAAM,CAAC,CACxB,CAAC,EACH,CAAC,CACF,CAAC,GA7BCE,GAAG,CAACH,EA8BT,CACN,CAAC,CAGDV,QAAQ,CAAC+B,MAAM,GAAK,CAAC,eACpB7B,IAAA,QAAKO,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5BT,IAAA,QAAKO,SAAS,CAAC,kBAAkB,CAAAE,QAAA,cAC/BT,IAAA,MAAAS,QAAA,CAAG,8BAA4B,CAAG,CAAC,CAChC,CAAC,CACH,CACN,EACE,CAAC,cAGNP,KAAA,QAAK4B,KAAK,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,SAAS,CAAE,MAAM,CAAEC,KAAK,CAAE,uBAAuB,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAzB,QAAA,EAAC,oBACtF,CAACX,QAAQ,CAAC+B,MAAM,CAAC,MAAI,CAAC/B,QAAQ,CAAC+B,MAAM,GAAK,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,mBAC3E,EAAK,CAAC,EACC,CAAC,CAEd,CAAC,CAED,cAAe,CAAA1B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}