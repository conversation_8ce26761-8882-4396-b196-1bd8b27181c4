{"ast": null, "code": "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return \".\".concat(classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.'));\n}\nexport { classesToSelector as c };", "map": {"version": 3, "names": ["classesToSelector", "classes", "concat", "trim", "replace", "c"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/shared/classes-to-selector.mjs"], "sourcesContent": ["function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,OAAO,EAAE;EAClC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,EAAE;EACd;EACA,WAAAC,MAAA,CAAWD,OAAO,CAACE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;EAAA,CACzDA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AACrB;AAEA,SAASJ,iBAAiB,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}