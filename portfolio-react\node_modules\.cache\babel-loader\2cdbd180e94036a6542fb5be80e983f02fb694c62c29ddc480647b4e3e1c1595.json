{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\JobDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useParams, Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport ProjectImageSwiper from './ProjectImageSwiper';\nimport NDANotification from './NDANotification';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst JobDetail = () => {\n  _s();\n  const {\n    slug\n  } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({\n    isOpen: false,\n    projectTitle: ''\n  });\n\n  // Function to check if a project is NDA protected\n  const isNDAProject = project => {\n    return project.description.toLowerCase().includes('nda') || project.title.toLowerCase().includes('nda') || project.images.some(img => img.includes('NDA'));\n  };\n  const handleProjectCardClick = (e, project) => {\n    // Don't trigger if clicking on swiper navigation elements\n    if (e.target.closest('.swiper-button-prev-custom') || e.target.closest('.swiper-button-next-custom') || e.target.closest('.swiper-pagination') || e.target.closest('.fullscreen-overlay')) {\n      return;\n    }\n\n    // Don't trigger if clicking on swiper slide content (single click opens fullscreen)\n    if (e.target.closest('.swiper-slide-content')) {\n      return;\n    }\n\n    // Check if project is NDA protected\n    if (isNDAProject(project)) {\n      setNdaNotification({\n        isOpen: true,\n        projectTitle: project.title\n      });\n      return;\n    }\n    window.open(project.liveUrl, '_blank');\n  };\n  const handleProjectCardDoubleClick = (e, project) => {\n    // Only trigger on double-click for swiper slide content\n    if (e.target.closest('.swiper-slide-content')) {\n      // Check if project is NDA protected\n      if (isNDAProject(project)) {\n        setNdaNotification({\n          isOpen: true,\n          projectTitle: project.title\n        });\n        return;\n      }\n      window.open(project.liveUrl, '_blank');\n    }\n  };\n  const closeNdaNotification = () => {\n    setNdaNotification({\n      isOpen: false,\n      projectTitle: ''\n    });\n  };\n  if (!job) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '100px 20px',\n          textAlign: 'center',\n          color: 'white'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Job Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          style: {\n            color: '#4B0082'\n          },\n          children: \"\\u2190 Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"back-navigation\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/#experience\",\n        className: \"back-button\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"back-arrow\",\n          children: \"\\u2190\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Back to Timeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-hero\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"job-hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"company-branding\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: job.logo,\n            alt: job.logoAlt,\n            className: \"hero-company-logo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"company-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"job-title-hero\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"company-name-hero\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), job.companyLink && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"company-link-hero\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: job.companyLink,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: job.companyLink\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration-hero\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"job-summary\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.summary\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"job-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Role Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: job.roleOverview\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Key Responsibilities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: job.responsibilities.map((responsibility, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: responsibility\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Technologies & Skills\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skills-grid\",\n            children: Object.entries(job.skills).map(([category, skills]) => {\n              // Generate class name based on category name\n              const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `skill-category ${categoryClass}`,\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"skill-tags\",\n                  children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"skill-tag\",\n                    children: skill\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this)]\n              }, category, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Key Accomplishments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"accomplishments-list\",\n            children: job.accomplishments.map((accomplishment, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"accomplishment-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric\",\n                children: accomplishment.metric\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-description\",\n                children: accomplishment.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"role-projects\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Projects from this Role\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"projects-grid\",\n        children: job.projects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"project-card\",\n          onClick: e => handleProjectCardClick(e, project),\n          onDoubleClick: e => handleProjectCardDoubleClick(e, project),\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-image\",\n            children: /*#__PURE__*/_jsxDEV(ProjectImageSwiper, {\n              images: project.images,\n              title: project.title,\n              isNDA: isNDAProject(project)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"project-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-tech\",\n              children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: tech\n              }, techIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), project.liveUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"project-link\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NDANotification, {\n      isOpen: ndaNotification.isOpen,\n      onClose: closeNdaNotification,\n      projectTitle: ndaNotification.projectTitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(JobDetail, \"K5F6R1SFyh6WlhXyoSJTdBBNYuE=\", false, function () {\n  return [useParams];\n});\n_c = JobDetail;\nexport default JobDetail;\nvar _c;\n$RefreshReg$(_c, \"JobDetail\");", "map": {"version": 3, "names": ["React", "useState", "useParams", "Link", "jobsData", "Header", "Footer", "ProjectImageSwiper", "NDANotification", "jsxDEV", "_jsxDEV", "JobDetail", "_s", "slug", "job", "find", "ndaNotification", "setNdaNotification", "isOpen", "projectTitle", "isNDAProject", "project", "description", "toLowerCase", "includes", "title", "images", "some", "img", "handleProjectCardClick", "e", "target", "closest", "window", "open", "liveUrl", "handleProjectCardDoubleClick", "closeNdaNotification", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "textAlign", "color", "to", "className", "src", "logo", "alt", "logoAlt", "company", "companyLink", "href", "rel", "duration", "summary", "roleOverview", "responsibilities", "map", "responsibility", "index", "Object", "entries", "skills", "category", "categoryClass", "replace", "skill", "accomplishments", "accomplishment", "metric", "projects", "onClick", "onDoubleClick", "cursor", "isNDA", "technologies", "tech", "techIndex", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/JobDetail.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport ProjectImageSwiper from './ProjectImageSwiper';\nimport NDANotification from './NDANotification';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\nconst JobDetail = () => {\n  const { slug } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });\n\n  // Function to check if a project is NDA protected\n  const isNDAProject = (project) => {\n    return project.description.toLowerCase().includes('nda') ||\n           project.title.toLowerCase().includes('nda') ||\n           project.images.some(img => img.includes('NDA'));\n  };\n\n  const handleProjectCardClick = (e, project) => {\n    // Don't trigger if clicking on swiper navigation elements\n    if (e.target.closest('.swiper-button-prev-custom') ||\n        e.target.closest('.swiper-button-next-custom') ||\n        e.target.closest('.swiper-pagination') ||\n        e.target.closest('.fullscreen-overlay')) {\n      return;\n    }\n\n    // Don't trigger if clicking on swiper slide content (single click opens fullscreen)\n    if (e.target.closest('.swiper-slide-content')) {\n      return;\n    }\n\n    // Check if project is NDA protected\n    if (isNDAProject(project)) {\n      setNdaNotification({ isOpen: true, projectTitle: project.title });\n      return;\n    }\n\n    window.open(project.liveUrl, '_blank');\n  };\n\n  const handleProjectCardDoubleClick = (e, project) => {\n    // Only trigger on double-click for swiper slide content\n    if (e.target.closest('.swiper-slide-content')) {\n      // Check if project is NDA protected\n      if (isNDAProject(project)) {\n        setNdaNotification({ isOpen: true, projectTitle: project.title });\n        return;\n      }\n      window.open(project.liveUrl, '_blank');\n    }\n  };\n\n  const closeNdaNotification = () => {\n    setNdaNotification({ isOpen: false, projectTitle: '' });\n  };\n\n  if (!job) {\n    return (\n      <div>\n        <Header />\n        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>\n          <h1>Job Not Found</h1>\n          <Link to=\"/\" style={{ color: '#4B0082' }}>← Back to Home</Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Header />\n      \n      {/* Navigation Back */}\n      <div className=\"back-navigation\">\n        <Link to=\"/#experience\" className=\"back-button\">\n          <span className=\"back-arrow\">←</span>\n          <span>Back to Timeline</span>\n        </Link>\n      </div>\n\n      {/* Job Detail Hero Section */}\n      <section className=\"job-hero\">\n        <div className=\"job-hero-content\">\n          <div className=\"company-branding\">\n            <img \n              src={job.logo} \n              alt={job.logoAlt} \n              className=\"hero-company-logo\" \n            />\n            <div className=\"company-info\">\n              <h1 className=\"job-title-hero\">{job.title}</h1>\n              <h2 className=\"company-name-hero\">{job.company}</h2>\n              {job.companyLink && (\n                <p className=\"company-link-hero\">\n                  <a href={job.companyLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                    {job.companyLink}\n                  </a>\n                </p>\n              )}\n              <p className=\"job-duration-hero\">{job.duration}</p>\n            </div>\n          </div>\n          <div className=\"job-summary\">\n            <p>{job.summary}</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Job Details Content */}\n      <section className=\"job-content\">\n        <div className=\"content-grid\">\n          {/* Full Job Description */}\n          <div className=\"content-card\">\n            <h3>Role Overview</h3>\n            <p>{job.roleOverview}</p>\n            \n            <h4>Key Responsibilities</h4>\n            <ul>\n              {job.responsibilities.map((responsibility, index) => (\n                <li key={index}>{responsibility}</li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills & Technologies */}\n          <div className=\"content-card\">\n            <h3>Technologies & Skills</h3>\n            <div className=\"skills-grid\">\n              {Object.entries(job.skills).map(([category, skills]) => {\n                // Generate class name based on category name\n                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n                return (\n                  <div key={category} className={`skill-category ${categoryClass}`}>\n                    <h4>{category}</h4>\n                    <div className=\"skill-tags\">\n                      {skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Key Accomplishments */}\n          <div className=\"content-card\">\n            <h3>Key Accomplishments</h3>\n            <div className=\"accomplishments-list\">\n              {job.accomplishments.map((accomplishment, index) => (\n                <div key={index} className=\"accomplishment-item\">\n                  <div className=\"metric\">{accomplishment.metric}</div>\n                  <div className=\"metric-description\">{accomplishment.description}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Project Portfolio from this role */}\n      <section className=\"role-projects\">\n        <h2>Projects from this Role</h2>\n        <div className=\"projects-grid\">\n          {job.projects.map((project, index) => (\n            <div\n              key={index}\n              className=\"project-card\"\n              onClick={(e) => handleProjectCardClick(e, project)}\n              onDoubleClick={(e) => handleProjectCardDoubleClick(e, project)}\n              style={{ cursor: 'pointer' }}\n            >\n              <div className=\"project-image\">\n                <ProjectImageSwiper\n                  images={project.images}\n                  title={project.title}\n                  isNDA={isNDAProject(project)}\n                />\n              </div>\n              <div className=\"project-info\">\n                <h3>{project.title}</h3>\n                <p>{project.description}</p>\n                <div className=\"project-tech\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span key={techIndex}>{tech}</span>\n                  ))}\n                </div>\n                {project.liveUrl && (\n                  <div className=\"project-link\">\n                    <span>\n                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* NDA Notification Modal */}\n      <NDANotification\n        isOpen={ndaNotification.isOpen}\n        onClose={closeNdaNotification}\n        projectTitle={ndaNotification.projectTitle}\n      />\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default JobDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,IAAI,QAAQ,kBAAkB;AAClD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,mBAAmB;AAC1B,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC5B,MAAMY,GAAG,GAAGV,QAAQ,CAACW,IAAI,CAACD,GAAG,IAAIA,GAAG,CAACD,IAAI,KAAKA,IAAI,CAAC;EACnD,MAAM,CAACG,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC;IAAEiB,MAAM,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAG,CAAC,CAAC;;EAE3F;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChC,OAAOA,OAAO,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IACjDH,OAAO,CAACI,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAC3CH,OAAO,CAACK,MAAM,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC;EACxD,CAAC;EAED,MAAMK,sBAAsB,GAAGA,CAACC,CAAC,EAAET,OAAO,KAAK;IAC7C;IACA,IAAIS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,IAC9CF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,IAC9CF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,oBAAoB,CAAC,IACtCF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MAC3C;IACF;;IAEA;IACA,IAAIF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MAC7C;IACF;;IAEA;IACA,IAAIZ,YAAY,CAACC,OAAO,CAAC,EAAE;MACzBJ,kBAAkB,CAAC;QAAEC,MAAM,EAAE,IAAI;QAAEC,YAAY,EAAEE,OAAO,CAACI;MAAM,CAAC,CAAC;MACjE;IACF;IAEAQ,MAAM,CAACC,IAAI,CAACb,OAAO,CAACc,OAAO,EAAE,QAAQ,CAAC;EACxC,CAAC;EAED,MAAMC,4BAA4B,GAAGA,CAACN,CAAC,EAAET,OAAO,KAAK;IACnD;IACA,IAAIS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;MAC7C;MACA,IAAIZ,YAAY,CAACC,OAAO,CAAC,EAAE;QACzBJ,kBAAkB,CAAC;UAAEC,MAAM,EAAE,IAAI;UAAEC,YAAY,EAAEE,OAAO,CAACI;QAAM,CAAC,CAAC;QACjE;MACF;MACAQ,MAAM,CAACC,IAAI,CAACb,OAAO,CAACc,OAAO,EAAE,QAAQ,CAAC;IACxC;EACF,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCpB,kBAAkB,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,YAAY,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;EAED,IAAI,CAACL,GAAG,EAAE;IACR,oBACEJ,OAAA;MAAA4B,QAAA,gBACE5B,OAAA,CAACL,MAAM;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVhC,OAAA;QAAKiC,KAAK,EAAE;UAAEC,OAAO,EAAE,YAAY;UAAEC,SAAS,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAQ,CAAE;QAAAR,QAAA,gBACzE5B,OAAA;UAAA4B,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBhC,OAAA,CAACP,IAAI;UAAC4C,EAAE,EAAC,GAAG;UAACJ,KAAK,EAAE;YAAEG,KAAK,EAAE;UAAU,CAAE;UAAAR,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNhC,OAAA,CAACJ,MAAM;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACEhC,OAAA;IAAA4B,QAAA,gBACE5B,OAAA,CAACL,MAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGVhC,OAAA;MAAKsC,SAAS,EAAC,iBAAiB;MAAAV,QAAA,eAC9B5B,OAAA,CAACP,IAAI;QAAC4C,EAAE,EAAC,cAAc;QAACC,SAAS,EAAC,aAAa;QAAAV,QAAA,gBAC7C5B,OAAA;UAAMsC,SAAS,EAAC,YAAY;UAAAV,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACrChC,OAAA;UAAA4B,QAAA,EAAM;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhC,OAAA;MAASsC,SAAS,EAAC,UAAU;MAAAV,QAAA,eAC3B5B,OAAA;QAAKsC,SAAS,EAAC,kBAAkB;QAAAV,QAAA,gBAC/B5B,OAAA;UAAKsC,SAAS,EAAC,kBAAkB;UAAAV,QAAA,gBAC/B5B,OAAA;YACEuC,GAAG,EAAEnC,GAAG,CAACoC,IAAK;YACdC,GAAG,EAAErC,GAAG,CAACsC,OAAQ;YACjBJ,SAAS,EAAC;UAAmB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACFhC,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAV,QAAA,gBAC3B5B,OAAA;cAAIsC,SAAS,EAAC,gBAAgB;cAAAV,QAAA,EAAExB,GAAG,CAACW;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/ChC,OAAA;cAAIsC,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAExB,GAAG,CAACuC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACnD5B,GAAG,CAACwC,WAAW,iBACd5C,OAAA;cAAGsC,SAAS,EAAC,mBAAmB;cAAAV,QAAA,eAC9B5B,OAAA;gBAAG6C,IAAI,EAAEzC,GAAG,CAACwC,WAAY;gBAACvB,MAAM,EAAC,QAAQ;gBAACyB,GAAG,EAAC,qBAAqB;gBAAAlB,QAAA,EAChExB,GAAG,CAACwC;cAAW;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACJ,eACDhC,OAAA;cAAGsC,SAAS,EAAC,mBAAmB;cAAAV,QAAA,EAAExB,GAAG,CAAC2C;YAAQ;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAV,QAAA,eAC1B5B,OAAA;YAAA4B,QAAA,EAAIxB,GAAG,CAAC4C;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhC,OAAA;MAASsC,SAAS,EAAC,aAAa;MAAAV,QAAA,eAC9B5B,OAAA;QAAKsC,SAAS,EAAC,cAAc;QAAAV,QAAA,gBAE3B5B,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3B5B,OAAA;YAAA4B,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBhC,OAAA;YAAA4B,QAAA,EAAIxB,GAAG,CAAC6C;UAAY;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzBhC,OAAA;YAAA4B,QAAA,EAAI;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BhC,OAAA;YAAA4B,QAAA,EACGxB,GAAG,CAAC8C,gBAAgB,CAACC,GAAG,CAAC,CAACC,cAAc,EAAEC,KAAK,kBAC9CrD,OAAA;cAAA4B,QAAA,EAAiBwB;YAAc,GAAtBC,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAsB,CACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGNhC,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3B5B,OAAA;YAAA4B,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BhC,OAAA;YAAKsC,SAAS,EAAC,aAAa;YAAAV,QAAA,EACzB0B,MAAM,CAACC,OAAO,CAACnD,GAAG,CAACoD,MAAM,CAAC,CAACL,GAAG,CAAC,CAAC,CAACM,QAAQ,EAAED,MAAM,CAAC,KAAK;cACtD;cACA,MAAME,aAAa,GAAGD,QAAQ,CAAC5C,WAAW,CAAC,CAAC,CAAC8C,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS;cACnF,oBACE3D,OAAA;gBAAoBsC,SAAS,EAAE,kBAAkBoB,aAAa,EAAG;gBAAA9B,QAAA,gBAC/D5B,OAAA;kBAAA4B,QAAA,EAAK6B;gBAAQ;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnBhC,OAAA;kBAAKsC,SAAS,EAAC,YAAY;kBAAAV,QAAA,EACxB4B,MAAM,CAACL,GAAG,CAAC,CAACS,KAAK,EAAEP,KAAK,kBACvBrD,OAAA;oBAAkBsC,SAAS,EAAC,WAAW;oBAAAV,QAAA,EAAEgC;kBAAK,GAAnCP,KAAK;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,GANEyB,QAAQ;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOb,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhC,OAAA;UAAKsC,SAAS,EAAC,cAAc;UAAAV,QAAA,gBAC3B5B,OAAA;YAAA4B,QAAA,EAAI;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BhC,OAAA;YAAKsC,SAAS,EAAC,sBAAsB;YAAAV,QAAA,EAClCxB,GAAG,CAACyD,eAAe,CAACV,GAAG,CAAC,CAACW,cAAc,EAAET,KAAK,kBAC7CrD,OAAA;cAAiBsC,SAAS,EAAC,qBAAqB;cAAAV,QAAA,gBAC9C5B,OAAA;gBAAKsC,SAAS,EAAC,QAAQ;gBAAAV,QAAA,EAAEkC,cAAc,CAACC;cAAM;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDhC,OAAA;gBAAKsC,SAAS,EAAC,oBAAoB;gBAAAV,QAAA,EAAEkC,cAAc,CAAClD;cAAW;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAF9DqB,KAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhC,OAAA;MAASsC,SAAS,EAAC,eAAe;MAAAV,QAAA,gBAChC5B,OAAA;QAAA4B,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChChC,OAAA;QAAKsC,SAAS,EAAC,eAAe;QAAAV,QAAA,EAC3BxB,GAAG,CAAC4D,QAAQ,CAACb,GAAG,CAAC,CAACxC,OAAO,EAAE0C,KAAK,kBAC/BrD,OAAA;UAEEsC,SAAS,EAAC,cAAc;UACxB2B,OAAO,EAAG7C,CAAC,IAAKD,sBAAsB,CAACC,CAAC,EAAET,OAAO,CAAE;UACnDuD,aAAa,EAAG9C,CAAC,IAAKM,4BAA4B,CAACN,CAAC,EAAET,OAAO,CAAE;UAC/DsB,KAAK,EAAE;YAAEkC,MAAM,EAAE;UAAU,CAAE;UAAAvC,QAAA,gBAE7B5B,OAAA;YAAKsC,SAAS,EAAC,eAAe;YAAAV,QAAA,eAC5B5B,OAAA,CAACH,kBAAkB;cACjBmB,MAAM,EAAEL,OAAO,CAACK,MAAO;cACvBD,KAAK,EAAEJ,OAAO,CAACI,KAAM;cACrBqD,KAAK,EAAE1D,YAAY,CAACC,OAAO;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhC,OAAA;YAAKsC,SAAS,EAAC,cAAc;YAAAV,QAAA,gBAC3B5B,OAAA;cAAA4B,QAAA,EAAKjB,OAAO,CAACI;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBhC,OAAA;cAAA4B,QAAA,EAAIjB,OAAO,CAACC;YAAW;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BhC,OAAA;cAAKsC,SAAS,EAAC,cAAc;cAAAV,QAAA,EAC1BjB,OAAO,CAAC0D,YAAY,CAAClB,GAAG,CAAC,CAACmB,IAAI,EAAEC,SAAS,kBACxCvE,OAAA;gBAAA4B,QAAA,EAAuB0C;cAAI,GAAhBC,SAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLrB,OAAO,CAACc,OAAO,iBACdzB,OAAA;cAAKsC,SAAS,EAAC,cAAc;cAAAV,QAAA,eAC3B5B,OAAA;gBAAA4B,QAAA,EACGlB,YAAY,CAACC,OAAO,CAAC,GAAG,0BAA0B,GAAG;cAAyB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA5BDqB,KAAK;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVhC,OAAA,CAACF,eAAe;MACdU,MAAM,EAAEF,eAAe,CAACE,MAAO;MAC/BgE,OAAO,EAAE7C,oBAAqB;MAC9BlB,YAAY,EAAEH,eAAe,CAACG;IAAa;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAEFhC,OAAA,CAACJ,MAAM;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA9MID,SAAS;EAAA,QACIT,SAAS;AAAA;AAAAiF,EAAA,GADtBxE,SAAS;AAgNf,eAAeA,SAAS;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}