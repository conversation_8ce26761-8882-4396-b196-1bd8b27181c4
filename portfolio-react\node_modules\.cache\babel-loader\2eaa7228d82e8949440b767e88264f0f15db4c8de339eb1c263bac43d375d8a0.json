{"ast": null, "code": "import { e as elementChildren } from '../shared/utils.mjs';\nfunction Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const elementsSelector = '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]';\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = \"\".concat(parseInt(x, 10) * progress * rtlFactor, \"%\");\n    } else {\n      x = \"\".concat(x * progress * rtlFactor, \"px\");\n    }\n    if (y.indexOf('%') >= 0) {\n      y = \"\".concat(parseInt(y, 10) * progress, \"%\");\n    } else {\n      y = \"\".concat(y * progress, \"px\");\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = \"translate3d(\".concat(x, \", \").concat(y, \", 0px)\");\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += \" scale(\".concat(currentScale, \")\");\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += \" rotate(\".concat(currentRotate, \"deg)\");\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid,\n      isElement\n    } = swiper;\n    const elements = elementChildren(el, elementsSelector);\n    if (swiper.isElement) {\n      elements.push(...elementChildren(swiper.hostEl, elementsSelector));\n    }\n    elements.forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll(\"\".concat(elementsSelector, \", [data-swiper-parallax-rotate]\")).forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function (duration) {\n    if (duration === void 0) {\n      duration = swiper.params.speed;\n    }\n    const {\n      el,\n      hostEl\n    } = swiper;\n    const elements = [...el.querySelectorAll(elementsSelector)];\n    if (swiper.isElement) {\n      elements.push(...hostEl.querySelectorAll(elementsSelector));\n    }\n    elements.forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = \"\".concat(parallaxDuration, \"ms\");\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\nexport { Parallax as default };", "map": {"version": 3, "names": ["e", "elementChildren", "Parallax", "_ref", "swiper", "extendParams", "on", "parallax", "enabled", "elementsSelector", "setTransform", "el", "progress", "rtl", "rtlFactor", "p", "getAttribute", "x", "y", "scale", "opacity", "rotate", "isHorizontal", "indexOf", "concat", "parseInt", "currentOpacity", "Math", "abs", "style", "transform", "currentScale", "currentRotate", "setTranslate", "slides", "snapGrid", "isElement", "elements", "push", "hostEl", "for<PERSON>ach", "subEl", "slideEl", "slideIndex", "slideProgress", "params", "slidesPerGroup", "<PERSON><PERSON><PERSON><PERSON>iew", "ceil", "length", "min", "max", "querySelectorAll", "setTransition", "duration", "speed", "parallaxEl", "parallaxDuration", "transitionDuration", "watchSlidesProgress", "originalParams", "_swiper", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/parallax.mjs"], "sourcesContent": ["import { e as elementChildren } from '../shared/utils.mjs';\n\nfunction Parallax(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    parallax: {\n      enabled: false\n    }\n  });\n  const elementsSelector = '[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]';\n  const setTransform = (el, progress) => {\n    const {\n      rtl\n    } = swiper;\n    const rtlFactor = rtl ? -1 : 1;\n    const p = el.getAttribute('data-swiper-parallax') || '0';\n    let x = el.getAttribute('data-swiper-parallax-x');\n    let y = el.getAttribute('data-swiper-parallax-y');\n    const scale = el.getAttribute('data-swiper-parallax-scale');\n    const opacity = el.getAttribute('data-swiper-parallax-opacity');\n    const rotate = el.getAttribute('data-swiper-parallax-rotate');\n    if (x || y) {\n      x = x || '0';\n      y = y || '0';\n    } else if (swiper.isHorizontal()) {\n      x = p;\n      y = '0';\n    } else {\n      y = p;\n      x = '0';\n    }\n    if (x.indexOf('%') >= 0) {\n      x = `${parseInt(x, 10) * progress * rtlFactor}%`;\n    } else {\n      x = `${x * progress * rtlFactor}px`;\n    }\n    if (y.indexOf('%') >= 0) {\n      y = `${parseInt(y, 10) * progress}%`;\n    } else {\n      y = `${y * progress}px`;\n    }\n    if (typeof opacity !== 'undefined' && opacity !== null) {\n      const currentOpacity = opacity - (opacity - 1) * (1 - Math.abs(progress));\n      el.style.opacity = currentOpacity;\n    }\n    let transform = `translate3d(${x}, ${y}, 0px)`;\n    if (typeof scale !== 'undefined' && scale !== null) {\n      const currentScale = scale - (scale - 1) * (1 - Math.abs(progress));\n      transform += ` scale(${currentScale})`;\n    }\n    if (rotate && typeof rotate !== 'undefined' && rotate !== null) {\n      const currentRotate = rotate * progress * -1;\n      transform += ` rotate(${currentRotate}deg)`;\n    }\n    el.style.transform = transform;\n  };\n  const setTranslate = () => {\n    const {\n      el,\n      slides,\n      progress,\n      snapGrid,\n      isElement\n    } = swiper;\n    const elements = elementChildren(el, elementsSelector);\n    if (swiper.isElement) {\n      elements.push(...elementChildren(swiper.hostEl, elementsSelector));\n    }\n    elements.forEach(subEl => {\n      setTransform(subEl, progress);\n    });\n    slides.forEach((slideEl, slideIndex) => {\n      let slideProgress = slideEl.progress;\n      if (swiper.params.slidesPerGroup > 1 && swiper.params.slidesPerView !== 'auto') {\n        slideProgress += Math.ceil(slideIndex / 2) - progress * (snapGrid.length - 1);\n      }\n      slideProgress = Math.min(Math.max(slideProgress, -1), 1);\n      slideEl.querySelectorAll(`${elementsSelector}, [data-swiper-parallax-rotate]`).forEach(subEl => {\n        setTransform(subEl, slideProgress);\n      });\n    });\n  };\n  const setTransition = function (duration) {\n    if (duration === void 0) {\n      duration = swiper.params.speed;\n    }\n    const {\n      el,\n      hostEl\n    } = swiper;\n    const elements = [...el.querySelectorAll(elementsSelector)];\n    if (swiper.isElement) {\n      elements.push(...hostEl.querySelectorAll(elementsSelector));\n    }\n    elements.forEach(parallaxEl => {\n      let parallaxDuration = parseInt(parallaxEl.getAttribute('data-swiper-parallax-duration'), 10) || duration;\n      if (duration === 0) parallaxDuration = 0;\n      parallaxEl.style.transitionDuration = `${parallaxDuration}ms`;\n    });\n  };\n  on('beforeInit', () => {\n    if (!swiper.params.parallax.enabled) return;\n    swiper.params.watchSlidesProgress = true;\n    swiper.originalParams.watchSlidesProgress = true;\n  });\n  on('init', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTranslate', () => {\n    if (!swiper.params.parallax.enabled) return;\n    setTranslate();\n  });\n  on('setTransition', (_swiper, duration) => {\n    if (!swiper.params.parallax.enabled) return;\n    setTransition(duration);\n  });\n}\n\nexport { Parallax as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAE1D,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,QAAQ,EAAE;MACRC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAG,0IAA0I;EACnK,MAAMC,YAAY,GAAGA,CAACC,EAAE,EAAEC,QAAQ,KAAK;IACrC,MAAM;MACJC;IACF,CAAC,GAAGT,MAAM;IACV,MAAMU,SAAS,GAAGD,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9B,MAAME,CAAC,GAAGJ,EAAE,CAACK,YAAY,CAAC,sBAAsB,CAAC,IAAI,GAAG;IACxD,IAAIC,CAAC,GAAGN,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,IAAIE,CAAC,GAAGP,EAAE,CAACK,YAAY,CAAC,wBAAwB,CAAC;IACjD,MAAMG,KAAK,GAAGR,EAAE,CAACK,YAAY,CAAC,4BAA4B,CAAC;IAC3D,MAAMI,OAAO,GAAGT,EAAE,CAACK,YAAY,CAAC,8BAA8B,CAAC;IAC/D,MAAMK,MAAM,GAAGV,EAAE,CAACK,YAAY,CAAC,6BAA6B,CAAC;IAC7D,IAAIC,CAAC,IAAIC,CAAC,EAAE;MACVD,CAAC,GAAGA,CAAC,IAAI,GAAG;MACZC,CAAC,GAAGA,CAAC,IAAI,GAAG;IACd,CAAC,MAAM,IAAId,MAAM,CAACkB,YAAY,CAAC,CAAC,EAAE;MAChCL,CAAC,GAAGF,CAAC;MACLG,CAAC,GAAG,GAAG;IACT,CAAC,MAAM;MACLA,CAAC,GAAGH,CAAC;MACLE,CAAC,GAAG,GAAG;IACT;IACA,IAAIA,CAAC,CAACM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBN,CAAC,MAAAO,MAAA,CAAMC,QAAQ,CAACR,CAAC,EAAE,EAAE,CAAC,GAAGL,QAAQ,GAAGE,SAAS,MAAG;IAClD,CAAC,MAAM;MACLG,CAAC,MAAAO,MAAA,CAAMP,CAAC,GAAGL,QAAQ,GAAGE,SAAS,OAAI;IACrC;IACA,IAAII,CAAC,CAACK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACvBL,CAAC,MAAAM,MAAA,CAAMC,QAAQ,CAACP,CAAC,EAAE,EAAE,CAAC,GAAGN,QAAQ,MAAG;IACtC,CAAC,MAAM;MACLM,CAAC,MAAAM,MAAA,CAAMN,CAAC,GAAGN,QAAQ,OAAI;IACzB;IACA,IAAI,OAAOQ,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,EAAE;MACtD,MAAMM,cAAc,GAAGN,OAAO,GAAG,CAACA,OAAO,GAAG,CAAC,KAAK,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAChB,QAAQ,CAAC,CAAC;MACzED,EAAE,CAACkB,KAAK,CAACT,OAAO,GAAGM,cAAc;IACnC;IACA,IAAII,SAAS,kBAAAN,MAAA,CAAkBP,CAAC,QAAAO,MAAA,CAAKN,CAAC,WAAQ;IAC9C,IAAI,OAAOC,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClD,MAAMY,YAAY,GAAGZ,KAAK,GAAG,CAACA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAGQ,IAAI,CAACC,GAAG,CAAChB,QAAQ,CAAC,CAAC;MACnEkB,SAAS,cAAAN,MAAA,CAAcO,YAAY,MAAG;IACxC;IACA,IAAIV,MAAM,IAAI,OAAOA,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI,EAAE;MAC9D,MAAMW,aAAa,GAAGX,MAAM,GAAGT,QAAQ,GAAG,CAAC,CAAC;MAC5CkB,SAAS,eAAAN,MAAA,CAAeQ,aAAa,SAAM;IAC7C;IACArB,EAAE,CAACkB,KAAK,CAACC,SAAS,GAAGA,SAAS;EAChC,CAAC;EACD,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJtB,EAAE;MACFuB,MAAM;MACNtB,QAAQ;MACRuB,QAAQ;MACRC;IACF,CAAC,GAAGhC,MAAM;IACV,MAAMiC,QAAQ,GAAGpC,eAAe,CAACU,EAAE,EAAEF,gBAAgB,CAAC;IACtD,IAAIL,MAAM,CAACgC,SAAS,EAAE;MACpBC,QAAQ,CAACC,IAAI,CAAC,GAAGrC,eAAe,CAACG,MAAM,CAACmC,MAAM,EAAE9B,gBAAgB,CAAC,CAAC;IACpE;IACA4B,QAAQ,CAACG,OAAO,CAACC,KAAK,IAAI;MACxB/B,YAAY,CAAC+B,KAAK,EAAE7B,QAAQ,CAAC;IAC/B,CAAC,CAAC;IACFsB,MAAM,CAACM,OAAO,CAAC,CAACE,OAAO,EAAEC,UAAU,KAAK;MACtC,IAAIC,aAAa,GAAGF,OAAO,CAAC9B,QAAQ;MACpC,IAAIR,MAAM,CAACyC,MAAM,CAACC,cAAc,GAAG,CAAC,IAAI1C,MAAM,CAACyC,MAAM,CAACE,aAAa,KAAK,MAAM,EAAE;QAC9EH,aAAa,IAAIjB,IAAI,CAACqB,IAAI,CAACL,UAAU,GAAG,CAAC,CAAC,GAAG/B,QAAQ,IAAIuB,QAAQ,CAACc,MAAM,GAAG,CAAC,CAAC;MAC/E;MACAL,aAAa,GAAGjB,IAAI,CAACuB,GAAG,CAACvB,IAAI,CAACwB,GAAG,CAACP,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxDF,OAAO,CAACU,gBAAgB,IAAA5B,MAAA,CAAIf,gBAAgB,oCAAiC,CAAC,CAAC+B,OAAO,CAACC,KAAK,IAAI;QAC9F/B,YAAY,CAAC+B,KAAK,EAAEG,aAAa,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMS,aAAa,GAAG,SAAAA,CAAUC,QAAQ,EAAE;IACxC,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAGlD,MAAM,CAACyC,MAAM,CAACU,KAAK;IAChC;IACA,MAAM;MACJ5C,EAAE;MACF4B;IACF,CAAC,GAAGnC,MAAM;IACV,MAAMiC,QAAQ,GAAG,CAAC,GAAG1B,EAAE,CAACyC,gBAAgB,CAAC3C,gBAAgB,CAAC,CAAC;IAC3D,IAAIL,MAAM,CAACgC,SAAS,EAAE;MACpBC,QAAQ,CAACC,IAAI,CAAC,GAAGC,MAAM,CAACa,gBAAgB,CAAC3C,gBAAgB,CAAC,CAAC;IAC7D;IACA4B,QAAQ,CAACG,OAAO,CAACgB,UAAU,IAAI;MAC7B,IAAIC,gBAAgB,GAAGhC,QAAQ,CAAC+B,UAAU,CAACxC,YAAY,CAAC,+BAA+B,CAAC,EAAE,EAAE,CAAC,IAAIsC,QAAQ;MACzG,IAAIA,QAAQ,KAAK,CAAC,EAAEG,gBAAgB,GAAG,CAAC;MACxCD,UAAU,CAAC3B,KAAK,CAAC6B,kBAAkB,MAAAlC,MAAA,CAAMiC,gBAAgB,OAAI;IAC/D,CAAC,CAAC;EACJ,CAAC;EACDnD,EAAE,CAAC,YAAY,EAAE,MAAM;IACrB,IAAI,CAACF,MAAM,CAACyC,MAAM,CAACtC,QAAQ,CAACC,OAAO,EAAE;IACrCJ,MAAM,CAACyC,MAAM,CAACc,mBAAmB,GAAG,IAAI;IACxCvD,MAAM,CAACwD,cAAc,CAACD,mBAAmB,GAAG,IAAI;EAClD,CAAC,CAAC;EACFrD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAI,CAACF,MAAM,CAACyC,MAAM,CAACtC,QAAQ,CAACC,OAAO,EAAE;IACrCyB,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACF3B,EAAE,CAAC,cAAc,EAAE,MAAM;IACvB,IAAI,CAACF,MAAM,CAACyC,MAAM,CAACtC,QAAQ,CAACC,OAAO,EAAE;IACrCyB,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACF3B,EAAE,CAAC,eAAe,EAAE,CAACuD,OAAO,EAAEP,QAAQ,KAAK;IACzC,IAAI,CAAClD,MAAM,CAACyC,MAAM,CAACtC,QAAQ,CAACC,OAAO,EAAE;IACrC6C,aAAa,CAACC,QAAQ,CAAC;EACzB,CAAC,CAAC;AACJ;AAEA,SAASpD,QAAQ,IAAI4D,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}