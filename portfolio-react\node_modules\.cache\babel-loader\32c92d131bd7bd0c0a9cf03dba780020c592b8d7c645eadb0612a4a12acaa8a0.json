{"ast": null, "code": "import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\nfunction EffectCoverflow(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    const r = getRotateFix(swiper);\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${r(rotateX)}deg) rotateY(${r(rotateY)}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow('coverflow', slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow('coverflow', slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}\nexport { EffectCoverflow as default };", "map": {"version": 3, "names": ["c", "createShadow", "e", "effectInit", "effect<PERSON>arget", "g", "getSlideTransformEl", "p", "getRotateFix", "EffectCoverflow", "_ref", "swiper", "extendParams", "on", "coverflowEffect", "rotate", "stretch", "depth", "scale", "modifier", "slideShadows", "setTranslate", "width", "swiper<PERSON><PERSON><PERSON>", "height", "swiperHeight", "slides", "slidesSizesGrid", "params", "isHorizontal", "transform", "translate", "center", "r", "i", "length", "slideEl", "slideSize", "slideOffset", "swiperSlideOffset", "centerOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "Math", "abs", "indexOf", "parseFloat", "translateY", "translateX", "slideTransform", "targetEl", "style", "zIndex", "round", "shadowBeforeEl", "querySelector", "shadowAfterEl", "opacity", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "querySelectorAll", "shadowEl", "effect", "perspective", "overwriteParams", "watchSlidesProgress", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/effect-coverflow.mjs"], "sourcesContent": ["import { c as createShadow } from '../shared/create-shadow.mjs';\nimport { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { g as getSlideTransformEl, p as getRotateFix } from '../shared/utils.mjs';\n\nfunction EffectCoverflow(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true\n    }\n  });\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth;\n    const r = getRotateFix(swiper);\n    // Each slide offset from center\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const slideEl = slides[i];\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = slideEl.swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier;\n      // var rotateZ = 0\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch;\n      // Allow percentage to make a relative stretch for responsive sliders\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier);\n\n      // Fix for ultra small values\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${r(rotateX)}deg) rotateY(${r(rotateY)}deg) scale(${scale})`;\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.transform = slideTransform;\n      slideEl.style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n      if (params.slideShadows) {\n        // Set shadows\n        let shadowBeforeEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-left') : slideEl.querySelector('.swiper-slide-shadow-top');\n        let shadowAfterEl = isHorizontal ? slideEl.querySelector('.swiper-slide-shadow-right') : slideEl.querySelector('.swiper-slide-shadow-bottom');\n        if (!shadowBeforeEl) {\n          shadowBeforeEl = createShadow('coverflow', slideEl, isHorizontal ? 'left' : 'top');\n        }\n        if (!shadowAfterEl) {\n          shadowAfterEl = createShadow('coverflow', slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n        if (shadowBeforeEl) shadowBeforeEl.style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if (shadowAfterEl) shadowAfterEl.style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n      el.querySelectorAll('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').forEach(shadowEl => {\n        shadowEl.style.transitionDuration = `${duration}ms`;\n      });\n    });\n  };\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}\n\nexport { EffectCoverflow as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,UAAU,QAAQ,2BAA2B;AAC3D,SAASD,CAAC,IAAIE,YAAY,QAAQ,6BAA6B;AAC/D,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAEjF,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,eAAe,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE;IAChB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC,KAAK,EAAEC,WAAW;MAClBC,MAAM,EAAEC,YAAY;MACpBC,MAAM;MACNC;IACF,CAAC,GAAGhB,MAAM;IACV,MAAMiB,MAAM,GAAGjB,MAAM,CAACiB,MAAM,CAACd,eAAe;IAC5C,MAAMe,YAAY,GAAGlB,MAAM,CAACkB,YAAY,CAAC,CAAC;IAC1C,MAAMC,SAAS,GAAGnB,MAAM,CAACoB,SAAS;IAClC,MAAMC,MAAM,GAAGH,YAAY,GAAG,CAACC,SAAS,GAAGP,WAAW,GAAG,CAAC,GAAG,CAACO,SAAS,GAAGL,YAAY,GAAG,CAAC;IAC1F,MAAMV,MAAM,GAAGc,YAAY,GAAGD,MAAM,CAACb,MAAM,GAAG,CAACa,MAAM,CAACb,MAAM;IAC5D,MAAMgB,SAAS,GAAGH,MAAM,CAACX,KAAK;IAC9B,MAAMgB,CAAC,GAAGzB,YAAY,CAACG,MAAM,CAAC;IAC9B;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEC,MAAM,GAAGT,MAAM,CAACS,MAAM,EAAED,CAAC,GAAGC,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC1D,MAAME,OAAO,GAAGV,MAAM,CAACQ,CAAC,CAAC;MACzB,MAAMG,SAAS,GAAGV,eAAe,CAACO,CAAC,CAAC;MACpC,MAAMI,WAAW,GAAGF,OAAO,CAACG,iBAAiB;MAC7C,MAAMC,YAAY,GAAG,CAACR,MAAM,GAAGM,WAAW,GAAGD,SAAS,GAAG,CAAC,IAAIA,SAAS;MACvE,MAAMI,gBAAgB,GAAG,OAAOb,MAAM,CAACT,QAAQ,KAAK,UAAU,GAAGS,MAAM,CAACT,QAAQ,CAACqB,YAAY,CAAC,GAAGA,YAAY,GAAGZ,MAAM,CAACT,QAAQ;MAC/H,IAAIuB,OAAO,GAAGb,YAAY,GAAGd,MAAM,GAAG0B,gBAAgB,GAAG,CAAC;MAC1D,IAAIE,OAAO,GAAGd,YAAY,GAAG,CAAC,GAAGd,MAAM,GAAG0B,gBAAgB;MAC1D;MACA,IAAIG,UAAU,GAAG,CAACb,SAAS,GAAGc,IAAI,CAACC,GAAG,CAACL,gBAAgB,CAAC;MACxD,IAAIzB,OAAO,GAAGY,MAAM,CAACZ,OAAO;MAC5B;MACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAAC+B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9D/B,OAAO,GAAGgC,UAAU,CAACpB,MAAM,CAACZ,OAAO,CAAC,GAAG,GAAG,GAAGqB,SAAS;MACxD;MACA,IAAIY,UAAU,GAAGpB,YAAY,GAAG,CAAC,GAAGb,OAAO,GAAGyB,gBAAgB;MAC9D,IAAIS,UAAU,GAAGrB,YAAY,GAAGb,OAAO,GAAGyB,gBAAgB,GAAG,CAAC;MAC9D,IAAIvB,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGU,MAAM,CAACV,KAAK,IAAI2B,IAAI,CAACC,GAAG,CAACL,gBAAgB,CAAC;;MAE/D;MACA,IAAII,IAAI,CAACC,GAAG,CAACI,UAAU,CAAC,GAAG,KAAK,EAAEA,UAAU,GAAG,CAAC;MAChD,IAAIL,IAAI,CAACC,GAAG,CAACG,UAAU,CAAC,GAAG,KAAK,EAAEA,UAAU,GAAG,CAAC;MAChD,IAAIJ,IAAI,CAACC,GAAG,CAACF,UAAU,CAAC,GAAG,KAAK,EAAEA,UAAU,GAAG,CAAC;MAChD,IAAIC,IAAI,CAACC,GAAG,CAACJ,OAAO,CAAC,GAAG,KAAK,EAAEA,OAAO,GAAG,CAAC;MAC1C,IAAIG,IAAI,CAACC,GAAG,CAACH,OAAO,CAAC,GAAG,KAAK,EAAEA,OAAO,GAAG,CAAC;MAC1C,IAAIE,IAAI,CAACC,GAAG,CAAC5B,KAAK,CAAC,GAAG,KAAK,EAAEA,KAAK,GAAG,CAAC;MACtC,MAAMiC,cAAc,GAAG,eAAeD,UAAU,MAAMD,UAAU,MAAML,UAAU,gBAAgBX,CAAC,CAACU,OAAO,CAAC,gBAAgBV,CAAC,CAACS,OAAO,CAAC,cAAcxB,KAAK,GAAG;MAC1J,MAAMkC,QAAQ,GAAGhD,YAAY,CAACwB,MAAM,EAAEQ,OAAO,CAAC;MAC9CgB,QAAQ,CAACC,KAAK,CAACvB,SAAS,GAAGqB,cAAc;MACzCf,OAAO,CAACiB,KAAK,CAACC,MAAM,GAAG,CAACT,IAAI,CAACC,GAAG,CAACD,IAAI,CAACU,KAAK,CAACd,gBAAgB,CAAC,CAAC,GAAG,CAAC;MAClE,IAAIb,MAAM,CAACR,YAAY,EAAE;QACvB;QACA,IAAIoC,cAAc,GAAG3B,YAAY,GAAGO,OAAO,CAACqB,aAAa,CAAC,2BAA2B,CAAC,GAAGrB,OAAO,CAACqB,aAAa,CAAC,0BAA0B,CAAC;QAC1I,IAAIC,aAAa,GAAG7B,YAAY,GAAGO,OAAO,CAACqB,aAAa,CAAC,4BAA4B,CAAC,GAAGrB,OAAO,CAACqB,aAAa,CAAC,6BAA6B,CAAC;QAC7I,IAAI,CAACD,cAAc,EAAE;UACnBA,cAAc,GAAGvD,YAAY,CAAC,WAAW,EAAEmC,OAAO,EAAEP,YAAY,GAAG,MAAM,GAAG,KAAK,CAAC;QACpF;QACA,IAAI,CAAC6B,aAAa,EAAE;UAClBA,aAAa,GAAGzD,YAAY,CAAC,WAAW,EAAEmC,OAAO,EAAEP,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC;QACvF;QACA,IAAI2B,cAAc,EAAEA,cAAc,CAACH,KAAK,CAACM,OAAO,GAAGlB,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAG,CAAC;QAC9F,IAAIiB,aAAa,EAAEA,aAAa,CAACL,KAAK,CAACM,OAAO,GAAG,CAAClB,gBAAgB,GAAG,CAAC,GAAG,CAACA,gBAAgB,GAAG,CAAC;MAChG;IACF;EACF,CAAC;EACD,MAAMmB,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAGnD,MAAM,CAACe,MAAM,CAACqC,GAAG,CAAC3B,OAAO,IAAI9B,mBAAmB,CAAC8B,OAAO,CAAC,CAAC;IACpF0B,iBAAiB,CAACE,OAAO,CAACC,EAAE,IAAI;MAC9BA,EAAE,CAACZ,KAAK,CAACa,kBAAkB,GAAG,GAAGL,QAAQ,IAAI;MAC7CI,EAAE,CAACE,gBAAgB,CAAC,8GAA8G,CAAC,CAACH,OAAO,CAACI,QAAQ,IAAI;QACtJA,QAAQ,CAACf,KAAK,CAACa,kBAAkB,GAAG,GAAGL,QAAQ,IAAI;MACrD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD1D,UAAU,CAAC;IACTkE,MAAM,EAAE,WAAW;IACnB1D,MAAM;IACNE,EAAE;IACFQ,YAAY;IACZuC,aAAa;IACbU,WAAW,EAAEA,CAAA,KAAM,IAAI;IACvBC,eAAe,EAAEA,CAAA,MAAO;MACtBC,mBAAmB,EAAE;IACvB,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAAS/D,eAAe,IAAIgE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}