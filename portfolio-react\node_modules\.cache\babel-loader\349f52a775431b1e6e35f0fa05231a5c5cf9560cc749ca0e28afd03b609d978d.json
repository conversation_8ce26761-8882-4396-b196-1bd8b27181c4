{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Experience.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Experience = () => {\n  const handleCompanyLinkClick = e => {\n    // Prevent the parent Link from being triggered when clicking the company link\n    e.stopPropagation();\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"experience\",\n    id: \"experience\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Professional Experience\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"timeline\",\n      children: [jobsData.map((job, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeline-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timeline-dot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: `/job/${job.slug}`,\n          className: \"timeline-content-link\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"timeline-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: job.logo,\n              alt: job.logoAlt,\n              className: \"company-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"job-title\",\n              children: job.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"company-name\",\n              children: job.company\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 17\n            }, this), job.companyLink && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"company-link\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: job.companyLink,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                onClick: handleCompanyLinkClick,\n                children: job.companyLink\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-duration\",\n              children: job.duration\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"job-description\",\n              children: job.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"view-details\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"View Details \\u2192\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 13\n        }, this)]\n      }, job.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 11\n      }, this)), jobsData.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"timeline-item\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"timeline-content\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No jobs found in jobsData.js\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        marginTop: '40px',\n        color: 'rgba(255,255,255,0.6)',\n        fontSize: '14px'\n      },\n      children: [\"Currently showing \", jobsData.length, \" job\", jobsData.length !== 1 ? 's' : '', \" from jobsData.js\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Experience;\nexport default Experience;\nvar _c;\n$RefreshReg$(_c, \"Experience\");", "map": {"version": 3, "names": ["React", "Link", "jobsData", "jsxDEV", "_jsxDEV", "Experience", "handleCompanyLinkClick", "e", "stopPropagation", "className", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "job", "index", "to", "slug", "src", "logo", "alt", "logoAlt", "title", "company", "companyLink", "href", "target", "rel", "onClick", "duration", "summary", "length", "style", "textAlign", "marginTop", "color", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Experience.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\n\nconst Experience = () => {\n  const handleCompanyLinkClick = (e) => {\n    // Prevent the parent Link from being triggered when clicking the company link\n    e.stopPropagation();\n  };\n\n  return (\n    <section className=\"experience\" id=\"experience\">\n      <h2>Professional Experience</h2>\n      <div className=\"timeline\">\n        {/* Dynamic timeline content - automatically shows all jobs from jobsData.js */}\n        {jobsData.map((job, index) => (\n          <div key={job.id} className=\"timeline-item\">\n            <div className=\"timeline-dot\"></div>\n            <Link to={`/job/${job.slug}`} className=\"timeline-content-link\">\n              <div className=\"timeline-content\">\n                <img\n                  src={job.logo}\n                  alt={job.logoAlt}\n                  className=\"company-logo\"\n                />\n                <h3 className=\"job-title\">{job.title}</h3>\n                <h4 className=\"company-name\">{job.company}</h4>\n                {job.companyLink && (\n                  <p className=\"company-link\">\n                    <a\n                      href={job.companyLink}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      onClick={handleCompanyLinkClick}\n                    >\n                      {job.companyLink}\n                    </a>\n                  </p>\n                )}\n                <p className=\"job-duration\">{job.duration}</p>\n                <p className=\"job-description\">{job.summary}</p>\n                <div className=\"view-details\">\n                  <span>View Details →</span>\n                </div>\n              </div>\n            </Link>\n          </div>\n        ))}\n\n        {/* Debug info - shows how many jobs are loaded */}\n        {jobsData.length === 0 && (\n          <div className=\"timeline-item\">\n            <div className=\"timeline-content\">\n              <p>No jobs found in jobsData.js</p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Info for developers */}\n      <div style={{ textAlign: 'center', marginTop: '40px', color: 'rgba(255,255,255,0.6)', fontSize: '14px' }}>\n        Currently showing {jobsData.length} job{jobsData.length !== 1 ? 's' : ''} from jobsData.js\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,MAAMC,sBAAsB,GAAIC,CAAC,IAAK;IACpC;IACAA,CAAC,CAACC,eAAe,CAAC,CAAC;EACrB,CAAC;EAED,oBACEJ,OAAA;IAASK,SAAS,EAAC,YAAY;IAACC,EAAE,EAAC,YAAY;IAAAC,QAAA,gBAC7CP,OAAA;MAAAO,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChCX,OAAA;MAAKK,SAAS,EAAC,UAAU;MAAAE,QAAA,GAEtBT,QAAQ,CAACc,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvBd,OAAA;QAAkBK,SAAS,EAAC,eAAe;QAAAE,QAAA,gBACzCP,OAAA;UAAKK,SAAS,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpCX,OAAA,CAACH,IAAI;UAACkB,EAAE,EAAE,QAAQF,GAAG,CAACG,IAAI,EAAG;UAACX,SAAS,EAAC,uBAAuB;UAAAE,QAAA,eAC7DP,OAAA;YAAKK,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/BP,OAAA;cACEiB,GAAG,EAAEJ,GAAG,CAACK,IAAK;cACdC,GAAG,EAAEN,GAAG,CAACO,OAAQ;cACjBf,SAAS,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFX,OAAA;cAAIK,SAAS,EAAC,WAAW;cAAAE,QAAA,EAAEM,GAAG,CAACQ;YAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1CX,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAEM,GAAG,CAACS;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC9CE,GAAG,CAACU,WAAW,iBACdvB,OAAA;cAAGK,SAAS,EAAC,cAAc;cAAAE,QAAA,eACzBP,OAAA;gBACEwB,IAAI,EAAEX,GAAG,CAACU,WAAY;gBACtBE,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzBC,OAAO,EAAEzB,sBAAuB;gBAAAK,QAAA,EAE/BM,GAAG,CAACU;cAAW;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACJ,eACDX,OAAA;cAAGK,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAEM,GAAG,CAACe;YAAQ;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9CX,OAAA;cAAGK,SAAS,EAAC,iBAAiB;cAAAE,QAAA,EAAEM,GAAG,CAACgB;YAAO;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChDX,OAAA;cAAKK,SAAS,EAAC,cAAc;cAAAE,QAAA,eAC3BP,OAAA;gBAAAO,QAAA,EAAM;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA,GA7BCE,GAAG,CAACP,EAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BX,CACN,CAAC,EAGDb,QAAQ,CAACgC,MAAM,KAAK,CAAC,iBACpB9B,OAAA;QAAKK,SAAS,EAAC,eAAe;QAAAE,QAAA,eAC5BP,OAAA;UAAKK,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eAC/BP,OAAA;YAAAO,QAAA,EAAG;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNX,OAAA;MAAK+B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,SAAS,EAAE,MAAM;QAAEC,KAAK,EAAE,uBAAuB;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAA5B,QAAA,GAAC,oBACtF,EAACT,QAAQ,CAACgC,MAAM,EAAC,MAAI,EAAChC,QAAQ,CAACgC,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,mBAC3E;IAAA;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACyB,EAAA,GA7DInC,UAAU;AA+DhB,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}