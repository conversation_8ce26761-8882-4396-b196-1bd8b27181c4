{"ast": null, "code": "import React,{useState}from'react';import{use<PERSON><PERSON><PERSON>,<PERSON>}from'react-router-dom';import{jobsData}from'../data/jobsData';import Header from'./Header';import Footer from'./Footer';import ProjectImageSwiper from'./ProjectImageSwiper';import NDANotification from'./NDANotification';import'../job-detail.css';import'./ProjectImageSwiper.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const JobDetail=()=>{const{slug}=useParams();const job=jobsData.find(job=>job.slug===slug);const[ndaNotification,setNdaNotification]=useState({isOpen:false,projectTitle:''});// Function to check if a project is NDA protected\nconst isNDAProject=project=>{return project.description.toLowerCase().includes('nda')||project.title.toLowerCase().includes('nda')||project.images.some(img=>img.includes('NDA'));};const handleProjectCardClick=(e,project)=>{// Don't trigger if clicking on swiper navigation elements or image area\nif(e.target.closest('.swiper-button-prev-custom')||e.target.closest('.swiper-button-next-custom')||e.target.closest('.swiper-pagination')||e.target.closest('.fullscreen-overlay')||e.target.closest('.fullscreen-icon-overlay')||e.target.closest('.project-image')||e.target.closest('.swiper-slide-content')||e.target.closest('.swiper-image')){return;}// Check if project is NDA protected\nif(isNDAProject(project)){setNdaNotification({isOpen:true,projectTitle:project.title});return;}window.open(project.liveUrl,'_blank');};const closeNdaNotification=()=>{setNdaNotification({isOpen:false,projectTitle:''});};if(!job){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'100px 20px',textAlign:'center',color:'white'},children:[/*#__PURE__*/_jsx(\"h1\",{children:\"Job Not Found\"}),/*#__PURE__*/_jsx(Link,{to:\"/\",style:{color:'#4B0082'},children:\"\\u2190 Back to Home\"})]}),/*#__PURE__*/_jsx(Footer,{})]});}return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"div\",{className:\"back-navigation\",children:/*#__PURE__*/_jsxs(Link,{to:\"/#experience\",className:\"back-button\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"back-arrow\",children:\"\\u2190\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Back to Timeline\"})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"job-hero\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"job-hero-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"company-branding\",children:[/*#__PURE__*/_jsx(\"img\",{src:job.logo,alt:job.logoAlt,className:\"hero-company-logo\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"company-info\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"job-title-hero\",children:job.title}),/*#__PURE__*/_jsx(\"h2\",{className:\"company-name-hero\",children:job.company}),job.companyLink&&/*#__PURE__*/_jsx(\"p\",{className:\"company-link-hero\",children:/*#__PURE__*/_jsx(\"a\",{href:job.companyLink,target:\"_blank\",rel:\"noopener noreferrer\",children:job.companyLink})}),/*#__PURE__*/_jsx(\"p\",{className:\"job-duration-hero\",children:job.duration})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"job-summary\",children:/*#__PURE__*/_jsx(\"p\",{children:job.summary})})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"job-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"content-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Role Overview\"}),/*#__PURE__*/_jsx(\"p\",{children:job.roleOverview}),/*#__PURE__*/_jsx(\"h4\",{children:\"Key Responsibilities\"}),/*#__PURE__*/_jsx(\"ul\",{children:job.responsibilities.map((responsibility,index)=>/*#__PURE__*/_jsx(\"li\",{children:responsibility},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Technologies & Skills\"}),/*#__PURE__*/_jsx(\"div\",{className:\"skills-grid\",children:Object.entries(job.skills).map(_ref=>{let[category,skills]=_ref;// Generate class name based on category name\nconst categoryClass=category.toLowerCase().replace(/[^a-z0-9]/g,'_')+'_skills';return/*#__PURE__*/_jsxs(\"div\",{className:\"skill-category \".concat(categoryClass),children:[/*#__PURE__*/_jsx(\"h4\",{children:category}),/*#__PURE__*/_jsx(\"div\",{className:\"skill-tags\",children:skills.map((skill,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"skill-tag\",children:skill},index))})]},category);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-card\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"Key Accomplishments\"}),/*#__PURE__*/_jsx(\"div\",{className:\"accomplishments-list\",children:job.accomplishments.map((accomplishment,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"accomplishment-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"metric\",children:accomplishment.metric}),/*#__PURE__*/_jsx(\"div\",{className:\"metric-description\",children:accomplishment.description})]},index))})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:\"role-projects\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"Projects from this Role\"}),/*#__PURE__*/_jsx(\"div\",{className:\"projects-grid\",children:job.projects.map((project,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"project-card\",onClick:e=>handleProjectCardClick(e,project),style:{cursor:'pointer'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"project-image\",children:/*#__PURE__*/_jsx(ProjectImageSwiper,{images:project.images,title:project.title,isNDA:isNDAProject(project)})}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-info\",children:[/*#__PURE__*/_jsx(\"h3\",{children:project.title}),/*#__PURE__*/_jsx(\"p\",{children:project.description}),/*#__PURE__*/_jsx(\"div\",{className:\"project-tech\",children:project.technologies.map((tech,techIndex)=>/*#__PURE__*/_jsx(\"span\",{children:tech},techIndex))}),project.liveUrl&&/*#__PURE__*/_jsx(\"div\",{className:\"project-link\",children:/*#__PURE__*/_jsx(\"span\",{children:isNDAProject(project)?'Click to view NDA info →':'Click to view project →'})})]})]},index))})]}),/*#__PURE__*/_jsx(NDANotification,{isOpen:ndaNotification.isOpen,onClose:closeNdaNotification,projectTitle:ndaNotification.projectTitle}),/*#__PURE__*/_jsx(Footer,{})]});};export default JobDetail;", "map": {"version": 3, "names": ["React", "useState", "useParams", "Link", "jobsData", "Header", "Footer", "ProjectImageSwiper", "NDANotification", "jsx", "_jsx", "jsxs", "_jsxs", "JobDetail", "slug", "job", "find", "ndaNotification", "setNdaNotification", "isOpen", "projectTitle", "isNDAProject", "project", "description", "toLowerCase", "includes", "title", "images", "some", "img", "handleProjectCardClick", "e", "target", "closest", "window", "open", "liveUrl", "closeNdaNotification", "children", "style", "padding", "textAlign", "color", "to", "className", "src", "logo", "alt", "logoAlt", "company", "companyLink", "href", "rel", "duration", "summary", "roleOverview", "responsibilities", "map", "responsibility", "index", "Object", "entries", "skills", "_ref", "category", "categoryClass", "replace", "concat", "skill", "accomplishments", "accomplishment", "metric", "projects", "onClick", "cursor", "isNDA", "technologies", "tech", "techIndex", "onClose"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/JobDetail.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';\nimport { jobsData } from '../data/jobsData';\nimport Header from './Header';\nimport Footer from './Footer';\nimport ProjectImageSwiper from './ProjectImageSwiper';\nimport NDANotification from './NDANotification';\nimport '../job-detail.css';\nimport './ProjectImageSwiper.css';\n\nconst JobDetail = () => {\n  const { slug } = useParams();\n  const job = jobsData.find(job => job.slug === slug);\n  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });\n\n  // Function to check if a project is NDA protected\n  const isNDAProject = (project) => {\n    return project.description.toLowerCase().includes('nda') ||\n           project.title.toLowerCase().includes('nda') ||\n           project.images.some(img => img.includes('NDA'));\n  };\n\n  const handleProjectCardClick = (e, project) => {\n    // Don't trigger if clicking on swiper navigation elements or image area\n    if (e.target.closest('.swiper-button-prev-custom') ||\n        e.target.closest('.swiper-button-next-custom') ||\n        e.target.closest('.swiper-pagination') ||\n        e.target.closest('.fullscreen-overlay') ||\n        e.target.closest('.fullscreen-icon-overlay') ||\n        e.target.closest('.project-image') ||\n        e.target.closest('.swiper-slide-content') ||\n        e.target.closest('.swiper-image')) {\n      return;\n    }\n\n    // Check if project is NDA protected\n    if (isNDAProject(project)) {\n      setNdaNotification({ isOpen: true, projectTitle: project.title });\n      return;\n    }\n\n    window.open(project.liveUrl, '_blank');\n  };\n\n  const closeNdaNotification = () => {\n    setNdaNotification({ isOpen: false, projectTitle: '' });\n  };\n\n  if (!job) {\n    return (\n      <div>\n        <Header />\n        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>\n          <h1>Job Not Found</h1>\n          <Link to=\"/\" style={{ color: '#4B0082' }}>← Back to Home</Link>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <Header />\n      \n      {/* Navigation Back */}\n      <div className=\"back-navigation\">\n        <Link to=\"/#experience\" className=\"back-button\">\n          <span className=\"back-arrow\">←</span>\n          <span>Back to Timeline</span>\n        </Link>\n      </div>\n\n      {/* Job Detail Hero Section */}\n      <section className=\"job-hero\">\n        <div className=\"job-hero-content\">\n          <div className=\"company-branding\">\n            <img \n              src={job.logo} \n              alt={job.logoAlt} \n              className=\"hero-company-logo\" \n            />\n            <div className=\"company-info\">\n              <h1 className=\"job-title-hero\">{job.title}</h1>\n              <h2 className=\"company-name-hero\">{job.company}</h2>\n              {job.companyLink && (\n                <p className=\"company-link-hero\">\n                  <a href={job.companyLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                    {job.companyLink}\n                  </a>\n                </p>\n              )}\n              <p className=\"job-duration-hero\">{job.duration}</p>\n            </div>\n          </div>\n          <div className=\"job-summary\">\n            <p>{job.summary}</p>\n          </div>\n        </div>\n      </section>\n\n      {/* Job Details Content */}\n      <section className=\"job-content\">\n        <div className=\"content-grid\">\n          {/* Full Job Description */}\n          <div className=\"content-card\">\n            <h3>Role Overview</h3>\n            <p>{job.roleOverview}</p>\n            \n            <h4>Key Responsibilities</h4>\n            <ul>\n              {job.responsibilities.map((responsibility, index) => (\n                <li key={index}>{responsibility}</li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Skills & Technologies */}\n          <div className=\"content-card\">\n            <h3>Technologies & Skills</h3>\n            <div className=\"skills-grid\">\n              {Object.entries(job.skills).map(([category, skills]) => {\n                // Generate class name based on category name\n                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';\n                return (\n                  <div key={category} className={`skill-category ${categoryClass}`}>\n                    <h4>{category}</h4>\n                    <div className=\"skill-tags\">\n                      {skills.map((skill, index) => (\n                        <span key={index} className=\"skill-tag\">{skill}</span>\n                      ))}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Key Accomplishments */}\n          <div className=\"content-card\">\n            <h3>Key Accomplishments</h3>\n            <div className=\"accomplishments-list\">\n              {job.accomplishments.map((accomplishment, index) => (\n                <div key={index} className=\"accomplishment-item\">\n                  <div className=\"metric\">{accomplishment.metric}</div>\n                  <div className=\"metric-description\">{accomplishment.description}</div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Project Portfolio from this role */}\n      <section className=\"role-projects\">\n        <h2>Projects from this Role</h2>\n        <div className=\"projects-grid\">\n          {job.projects.map((project, index) => (\n            <div\n              key={index}\n              className=\"project-card\"\n              onClick={(e) => handleProjectCardClick(e, project)}\n              style={{ cursor: 'pointer' }}\n            >\n              <div className=\"project-image\">\n                <ProjectImageSwiper\n                  images={project.images}\n                  title={project.title}\n                  isNDA={isNDAProject(project)}\n                />\n              </div>\n              <div className=\"project-info\">\n                <h3>{project.title}</h3>\n                <p>{project.description}</p>\n                <div className=\"project-tech\">\n                  {project.technologies.map((tech, techIndex) => (\n                    <span key={techIndex}>{tech}</span>\n                  ))}\n                </div>\n                {project.liveUrl && (\n                  <div className=\"project-link\">\n                    <span>\n                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}\n                    </span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </section>\n\n      {/* NDA Notification Modal */}\n      <NDANotification\n        isOpen={ndaNotification.isOpen}\n        onClose={closeNdaNotification}\n        projectTitle={ndaNotification.projectTitle}\n      />\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default JobDetail;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,SAAS,CAAEC,IAAI,KAAQ,kBAAkB,CAClD,OAASC,QAAQ,KAAQ,kBAAkB,CAC3C,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,mBAAmB,CAC1B,MAAO,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElC,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAEC,IAAK,CAAC,CAAGZ,SAAS,CAAC,CAAC,CAC5B,KAAM,CAAAa,GAAG,CAAGX,QAAQ,CAACY,IAAI,CAACD,GAAG,EAAIA,GAAG,CAACD,IAAI,GAAKA,IAAI,CAAC,CACnD,KAAM,CAACG,eAAe,CAAEC,kBAAkB,CAAC,CAAGjB,QAAQ,CAAC,CAAEkB,MAAM,CAAE,KAAK,CAAEC,YAAY,CAAE,EAAG,CAAC,CAAC,CAE3F;AACA,KAAM,CAAAC,YAAY,CAAIC,OAAO,EAAK,CAChC,MAAO,CAAAA,OAAO,CAACC,WAAW,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EACjDH,OAAO,CAACI,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,EAC3CH,OAAO,CAACK,MAAM,CAACC,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACJ,QAAQ,CAAC,KAAK,CAAC,CAAC,CACxD,CAAC,CAED,KAAM,CAAAK,sBAAsB,CAAGA,CAACC,CAAC,CAAET,OAAO,GAAK,CAC7C;AACA,GAAIS,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAC9CF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,4BAA4B,CAAC,EAC9CF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,oBAAoB,CAAC,EACtCF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EACvCF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,0BAA0B,CAAC,EAC5CF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAClCF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC,EACzCF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,eAAe,CAAC,CAAE,CACrC,OACF,CAEA;AACA,GAAIZ,YAAY,CAACC,OAAO,CAAC,CAAE,CACzBJ,kBAAkB,CAAC,CAAEC,MAAM,CAAE,IAAI,CAAEC,YAAY,CAAEE,OAAO,CAACI,KAAM,CAAC,CAAC,CACjE,OACF,CAEAQ,MAAM,CAACC,IAAI,CAACb,OAAO,CAACc,OAAO,CAAE,QAAQ,CAAC,CACxC,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjCnB,kBAAkB,CAAC,CAAEC,MAAM,CAAE,KAAK,CAAEC,YAAY,CAAE,EAAG,CAAC,CAAC,CACzD,CAAC,CAED,GAAI,CAACL,GAAG,CAAE,CACR,mBACEH,KAAA,QAAA0B,QAAA,eACE5B,IAAA,CAACL,MAAM,GAAE,CAAC,cACVO,KAAA,QAAK2B,KAAK,CAAE,CAAEC,OAAO,CAAE,YAAY,CAAEC,SAAS,CAAE,QAAQ,CAAEC,KAAK,CAAE,OAAQ,CAAE,CAAAJ,QAAA,eACzE5B,IAAA,OAAA4B,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5B,IAAA,CAACP,IAAI,EAACwC,EAAE,CAAC,GAAG,CAACJ,KAAK,CAAE,CAAEG,KAAK,CAAE,SAAU,CAAE,CAAAJ,QAAA,CAAC,qBAAc,CAAM,CAAC,EAC5D,CAAC,cACN5B,IAAA,CAACJ,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAEA,mBACEM,KAAA,QAAA0B,QAAA,eACE5B,IAAA,CAACL,MAAM,GAAE,CAAC,cAGVK,IAAA,QAAKkC,SAAS,CAAC,iBAAiB,CAAAN,QAAA,cAC9B1B,KAAA,CAACT,IAAI,EAACwC,EAAE,CAAC,cAAc,CAACC,SAAS,CAAC,aAAa,CAAAN,QAAA,eAC7C5B,IAAA,SAAMkC,SAAS,CAAC,YAAY,CAAAN,QAAA,CAAC,QAAC,CAAM,CAAC,cACrC5B,IAAA,SAAA4B,QAAA,CAAM,kBAAgB,CAAM,CAAC,EACzB,CAAC,CACJ,CAAC,cAGN5B,IAAA,YAASkC,SAAS,CAAC,UAAU,CAAAN,QAAA,cAC3B1B,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/B1B,KAAA,QAAKgC,SAAS,CAAC,kBAAkB,CAAAN,QAAA,eAC/B5B,IAAA,QACEmC,GAAG,CAAE9B,GAAG,CAAC+B,IAAK,CACdC,GAAG,CAAEhC,GAAG,CAACiC,OAAQ,CACjBJ,SAAS,CAAC,mBAAmB,CAC9B,CAAC,cACFhC,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3B5B,IAAA,OAAIkC,SAAS,CAAC,gBAAgB,CAAAN,QAAA,CAAEvB,GAAG,CAACW,KAAK,CAAK,CAAC,cAC/ChB,IAAA,OAAIkC,SAAS,CAAC,mBAAmB,CAAAN,QAAA,CAAEvB,GAAG,CAACkC,OAAO,CAAK,CAAC,CACnDlC,GAAG,CAACmC,WAAW,eACdxC,IAAA,MAAGkC,SAAS,CAAC,mBAAmB,CAAAN,QAAA,cAC9B5B,IAAA,MAAGyC,IAAI,CAAEpC,GAAG,CAACmC,WAAY,CAAClB,MAAM,CAAC,QAAQ,CAACoB,GAAG,CAAC,qBAAqB,CAAAd,QAAA,CAChEvB,GAAG,CAACmC,WAAW,CACf,CAAC,CACH,CACJ,cACDxC,IAAA,MAAGkC,SAAS,CAAC,mBAAmB,CAAAN,QAAA,CAAEvB,GAAG,CAACsC,QAAQ,CAAI,CAAC,EAChD,CAAC,EACH,CAAC,cACN3C,IAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAN,QAAA,cAC1B5B,IAAA,MAAA4B,QAAA,CAAIvB,GAAG,CAACuC,OAAO,CAAI,CAAC,CACjB,CAAC,EACH,CAAC,CACC,CAAC,cAGV5C,IAAA,YAASkC,SAAS,CAAC,aAAa,CAAAN,QAAA,cAC9B1B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAE3B1B,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3B5B,IAAA,OAAA4B,QAAA,CAAI,eAAa,CAAI,CAAC,cACtB5B,IAAA,MAAA4B,QAAA,CAAIvB,GAAG,CAACwC,YAAY,CAAI,CAAC,cAEzB7C,IAAA,OAAA4B,QAAA,CAAI,sBAAoB,CAAI,CAAC,cAC7B5B,IAAA,OAAA4B,QAAA,CACGvB,GAAG,CAACyC,gBAAgB,CAACC,GAAG,CAAC,CAACC,cAAc,CAAEC,KAAK,gBAC9CjD,IAAA,OAAA4B,QAAA,CAAiBoB,cAAc,EAAtBC,KAA2B,CACrC,CAAC,CACA,CAAC,EACF,CAAC,cAGN/C,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3B5B,IAAA,OAAA4B,QAAA,CAAI,uBAAqB,CAAI,CAAC,cAC9B5B,IAAA,QAAKkC,SAAS,CAAC,aAAa,CAAAN,QAAA,CACzBsB,MAAM,CAACC,OAAO,CAAC9C,GAAG,CAAC+C,MAAM,CAAC,CAACL,GAAG,CAACM,IAAA,EAAwB,IAAvB,CAACC,QAAQ,CAAEF,MAAM,CAAC,CAAAC,IAAA,CACjD;AACA,KAAM,CAAAE,aAAa,CAAGD,QAAQ,CAACxC,WAAW,CAAC,CAAC,CAAC0C,OAAO,CAAC,YAAY,CAAE,GAAG,CAAC,CAAG,SAAS,CACnF,mBACEtD,KAAA,QAAoBgC,SAAS,mBAAAuB,MAAA,CAAoBF,aAAa,CAAG,CAAA3B,QAAA,eAC/D5B,IAAA,OAAA4B,QAAA,CAAK0B,QAAQ,CAAK,CAAC,cACnBtD,IAAA,QAAKkC,SAAS,CAAC,YAAY,CAAAN,QAAA,CACxBwB,MAAM,CAACL,GAAG,CAAC,CAACW,KAAK,CAAET,KAAK,gBACvBjD,IAAA,SAAkBkC,SAAS,CAAC,WAAW,CAAAN,QAAA,CAAE8B,KAAK,EAAnCT,KAA0C,CACtD,CAAC,CACC,CAAC,GANEK,QAOL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAGNpD,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3B5B,IAAA,OAAA4B,QAAA,CAAI,qBAAmB,CAAI,CAAC,cAC5B5B,IAAA,QAAKkC,SAAS,CAAC,sBAAsB,CAAAN,QAAA,CAClCvB,GAAG,CAACsD,eAAe,CAACZ,GAAG,CAAC,CAACa,cAAc,CAAEX,KAAK,gBAC7C/C,KAAA,QAAiBgC,SAAS,CAAC,qBAAqB,CAAAN,QAAA,eAC9C5B,IAAA,QAAKkC,SAAS,CAAC,QAAQ,CAAAN,QAAA,CAAEgC,cAAc,CAACC,MAAM,CAAM,CAAC,cACrD7D,IAAA,QAAKkC,SAAS,CAAC,oBAAoB,CAAAN,QAAA,CAAEgC,cAAc,CAAC/C,WAAW,CAAM,CAAC,GAF9DoC,KAGL,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGV/C,KAAA,YAASgC,SAAS,CAAC,eAAe,CAAAN,QAAA,eAChC5B,IAAA,OAAA4B,QAAA,CAAI,yBAAuB,CAAI,CAAC,cAChC5B,IAAA,QAAKkC,SAAS,CAAC,eAAe,CAAAN,QAAA,CAC3BvB,GAAG,CAACyD,QAAQ,CAACf,GAAG,CAAC,CAACnC,OAAO,CAAEqC,KAAK,gBAC/B/C,KAAA,QAEEgC,SAAS,CAAC,cAAc,CACxB6B,OAAO,CAAG1C,CAAC,EAAKD,sBAAsB,CAACC,CAAC,CAAET,OAAO,CAAE,CACnDiB,KAAK,CAAE,CAAEmC,MAAM,CAAE,SAAU,CAAE,CAAApC,QAAA,eAE7B5B,IAAA,QAAKkC,SAAS,CAAC,eAAe,CAAAN,QAAA,cAC5B5B,IAAA,CAACH,kBAAkB,EACjBoB,MAAM,CAAEL,OAAO,CAACK,MAAO,CACvBD,KAAK,CAAEJ,OAAO,CAACI,KAAM,CACrBiD,KAAK,CAAEtD,YAAY,CAACC,OAAO,CAAE,CAC9B,CAAC,CACC,CAAC,cACNV,KAAA,QAAKgC,SAAS,CAAC,cAAc,CAAAN,QAAA,eAC3B5B,IAAA,OAAA4B,QAAA,CAAKhB,OAAO,CAACI,KAAK,CAAK,CAAC,cACxBhB,IAAA,MAAA4B,QAAA,CAAIhB,OAAO,CAACC,WAAW,CAAI,CAAC,cAC5Bb,IAAA,QAAKkC,SAAS,CAAC,cAAc,CAAAN,QAAA,CAC1BhB,OAAO,CAACsD,YAAY,CAACnB,GAAG,CAAC,CAACoB,IAAI,CAAEC,SAAS,gBACxCpE,IAAA,SAAA4B,QAAA,CAAuBuC,IAAI,EAAhBC,SAAuB,CACnC,CAAC,CACC,CAAC,CACLxD,OAAO,CAACc,OAAO,eACd1B,IAAA,QAAKkC,SAAS,CAAC,cAAc,CAAAN,QAAA,cAC3B5B,IAAA,SAAA4B,QAAA,CACGjB,YAAY,CAACC,OAAO,CAAC,CAAG,0BAA0B,CAAG,yBAAyB,CAC3E,CAAC,CACJ,CACN,EACE,CAAC,GA3BDqC,KA4BF,CACN,CAAC,CACC,CAAC,EACC,CAAC,cAGVjD,IAAA,CAACF,eAAe,EACdW,MAAM,CAAEF,eAAe,CAACE,MAAO,CAC/B4D,OAAO,CAAE1C,oBAAqB,CAC9BjB,YAAY,CAAEH,eAAe,CAACG,YAAa,CAC5C,CAAC,cAEFV,IAAA,CAACJ,MAAM,GAAE,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}