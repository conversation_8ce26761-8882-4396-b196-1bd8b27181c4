{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"social-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://github.com/aminos555\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-github-64.png\",\n            alt: \"GitHub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://linkedin.com/in/medamine-chouchane\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-linkedin-circled-64.png\",\n            alt: \"LinkedIn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://instagram.com/medamine.chouchane\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/icons8-instagram-64.png\",\n            alt: \"Instagram\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-info\",\n        id: \"contact\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Get In Touch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Email: <EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Location: Tunisia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"copyright\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2024 Med Amine Chouchane. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "href", "target", "rel", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\n\nconst Footer = () => {\n  return (\n    <footer className=\"footer\">\n      <div className=\"footer-content\">\n        <div className=\"social-links\">\n          <a href=\"https://github.com/aminos555\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img src=\"/icons8-github-64.png\" alt=\"GitHub\" />\n          </a>\n          <a href=\"https://linkedin.com/in/medamine-chouchane\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img src=\"/icons8-linkedin-circled-64.png\" alt=\"LinkedIn\" />\n          </a>\n          <a href=\"https://instagram.com/medamine.chouchane\" target=\"_blank\" rel=\"noopener noreferrer\">\n            <img src=\"/icons8-instagram-64.png\" alt=\"Instagram\" />\n          </a>\n        </div>\n        \n        <div className=\"contact-info\" id=\"contact\">\n          <h3>Get In Touch</h3>\n          <p>Email: <EMAIL></p>\n          <p>Location: Tunisia</p>\n        </div>\n        \n        <div className=\"copyright\">\n          <p>&copy; 2024 Med Amine Chouchane. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACED,OAAA;IAAQE,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBH,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAGI,IAAI,EAAC,8BAA8B;UAACC,MAAM,EAAC,QAAQ;UAACC,GAAG,EAAC,qBAAqB;UAAAH,QAAA,eAC9EH,OAAA;YAAKO,GAAG,EAAC,uBAAuB;YAACC,GAAG,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACJZ,OAAA;UAAGI,IAAI,EAAC,4CAA4C;UAACC,MAAM,EAAC,QAAQ;UAACC,GAAG,EAAC,qBAAqB;UAAAH,QAAA,eAC5FH,OAAA;YAAKO,GAAG,EAAC,iCAAiC;YAACC,GAAG,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACJZ,OAAA;UAAGI,IAAI,EAAC,0CAA0C;UAACC,MAAM,EAAC,QAAQ;UAACC,GAAG,EAAC,qBAAqB;UAAAH,QAAA,eAC1FH,OAAA;YAAKO,GAAG,EAAC,0BAA0B;YAACC,GAAG,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENZ,OAAA;QAAKE,SAAS,EAAC,cAAc;QAACW,EAAE,EAAC,SAAS;QAAAV,QAAA,gBACxCH,OAAA;UAAAG,QAAA,EAAI;QAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBZ,OAAA;UAAAG,QAAA,EAAG;QAAkC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzCZ,OAAA;UAAAG,QAAA,EAAG;QAAiB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eAENZ,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBH,OAAA;UAAAG,QAAA,EAAG;QAAqD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACE,EAAA,GA5BIb,MAAM;AA8BZ,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}