{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElement, i as elementIndex, m as makeElementsArray, s as setInnerHTML } from '../shared/utils.mjs';\nfunction A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      containerRole: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n      scrollOnFocus: true\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  let preventFocusHandler;\n  let focusTargetSlideEl;\n  let visibilityChangedTimestamp = new Date().getTime();\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    setInnerHTML(notification, message);\n  }\n  function getRandomNumber(size) {\n    if (size === void 0) {\n      size = 16;\n    }\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {\n      const prevEls = makeElementsArray(swiper.navigation.prevEl);\n      const nextEls = makeElementsArray(swiper.navigation.nextEl);\n      if (nextEls.includes(targetEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          notify(params.lastSlideMessage);\n        } else {\n          notify(params.nextSlideMessage);\n        }\n      }\n      if (prevEls.includes(targetEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          notify(params.firstSlideMessage);\n        } else {\n          notify(params.prevSlideMessage);\n        }\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = e => {\n    if (focusTargetSlideEl && focusTargetSlideEl !== e.target && !focusTargetSlideEl.contains(e.target)) {\n      preventFocusHandler = true;\n    }\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    preventFocusHandler = false;\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const onVisibilityChange = e => {\n    visibilityChangedTimestamp = new Date().getTime();\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked || !swiper.params.a11y.scrollOnFocus) return;\n    if (new Date().getTime() - visibilityChangedTimestamp < 100) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    focusTargetSlideEl = slideEl;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    requestAnimationFrame(() => {\n      if (preventFocusHandler) return;\n      if (swiper.params.loop) {\n        swiper.slideToLoop(parseInt(slideEl.getAttribute('data-swiper-slide-index')), 0);\n      } else {\n        swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n      }\n      preventFocusHandler = false;\n    });\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n    if (params.containerRole) {\n      addElRole(containerEl, params.containerRole);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n    // Tab focus\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('focus', handleFocus, true);\n      swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n      swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n    }\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\nexport { A11y as default };", "map": {"version": 3, "names": ["g", "getDocument", "c", "classesToSelector", "createElement", "i", "elementIndex", "m", "makeElementsArray", "s", "setInnerHTML", "A11y", "_ref", "swiper", "extendParams", "on", "a11y", "enabled", "notificationClass", "prevSlideMessage", "nextSlideMessage", "firstSlideMessage", "lastSlideMessage", "paginationBulletMessage", "slideLabelMessage", "containerMessage", "containerRoleDescriptionMessage", "containerRole", "itemRoleDescriptionMessage", "slideRole", "id", "scrollOnFocus", "clicked", "liveRegion", "preventFocus<PERSON><PERSON>ler", "focusTargetSlideEl", "visibilityChangedTimestamp", "Date", "getTime", "notify", "message", "notification", "length", "getRandomNumber", "size", "randomChar", "Math", "round", "random", "toString", "repeat", "replace", "makeElFocusable", "el", "for<PERSON>ach", "subEl", "setAttribute", "makeElNotFocusable", "addElRole", "role", "addElRoleDescription", "description", "addElControls", "controls", "addElLabel", "label", "addElId", "addElLive", "live", "disableEl", "enableEl", "onEnterOrSpaceKey", "e", "keyCode", "params", "targetEl", "target", "pagination", "contains", "matches", "bulletClass", "navigation", "prevEl", "nextEl", "prevEls", "nextEls", "includes", "isEnd", "loop", "slideNext", "isBeginning", "slidePrev", "click", "updateNavigation", "rewind", "hasPagination", "bullets", "hasClickablePagination", "clickable", "updatePagination", "bulletEl", "renderBullet", "bulletActiveClass", "removeAttribute", "initNavEl", "wrapperId", "tagName", "addEventListener", "handlePointerDown", "handlePointerUp", "requestAnimationFrame", "destroyed", "onVisibilityChange", "handleFocus", "slideEl", "closest", "slideClass", "slides", "isActive", "indexOf", "activeIndex", "isVisible", "watchSlidesProgress", "visibleSlides", "sourceCapabilities", "firesTouchEvents", "isHorizontal", "scrollLeft", "scrollTop", "slideToLoop", "parseInt", "getAttribute", "slideTo", "initSlides", "<PERSON><PERSON><PERSON><PERSON>", "index", "slideIndex", "ariaLabelMessage", "init", "append", "containerEl", "wrapperEl", "autoplay", "paginationEl", "document", "destroy", "remove", "removeEventListener", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/a11y.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElement, i as elementIndex, m as makeElementsArray, s as setInnerHTML } from '../shared/utils.mjs';\n\nfunction A11y(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    a11y: {\n      enabled: true,\n      notificationClass: 'swiper-notification',\n      prevSlideMessage: 'Previous slide',\n      nextSlideMessage: 'Next slide',\n      firstSlideMessage: 'This is the first slide',\n      lastSlideMessage: 'This is the last slide',\n      paginationBulletMessage: 'Go to slide {{index}}',\n      slideLabelMessage: '{{index}} / {{slidesLength}}',\n      containerMessage: null,\n      containerRoleDescriptionMessage: null,\n      containerRole: null,\n      itemRoleDescriptionMessage: null,\n      slideRole: 'group',\n      id: null,\n      scrollOnFocus: true\n    }\n  });\n  swiper.a11y = {\n    clicked: false\n  };\n  let liveRegion = null;\n  let preventFocusHandler;\n  let focusTargetSlideEl;\n  let visibilityChangedTimestamp = new Date().getTime();\n  function notify(message) {\n    const notification = liveRegion;\n    if (notification.length === 0) return;\n    setInnerHTML(notification, message);\n  }\n  function getRandomNumber(size) {\n    if (size === void 0) {\n      size = 16;\n    }\n    const randomChar = () => Math.round(16 * Math.random()).toString(16);\n    return 'x'.repeat(size).replace(/x/g, randomChar);\n  }\n  function makeElFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '0');\n    });\n  }\n  function makeElNotFocusable(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('tabIndex', '-1');\n    });\n  }\n  function addElRole(el, role) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('role', role);\n    });\n  }\n  function addElRoleDescription(el, description) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-roledescription', description);\n    });\n  }\n  function addElControls(el, controls) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-controls', controls);\n    });\n  }\n  function addElLabel(el, label) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-label', label);\n    });\n  }\n  function addElId(el, id) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('id', id);\n    });\n  }\n  function addElLive(el, live) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-live', live);\n    });\n  }\n  function disableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', true);\n    });\n  }\n  function enableEl(el) {\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.setAttribute('aria-disabled', false);\n    });\n  }\n  function onEnterOrSpaceKey(e) {\n    if (e.keyCode !== 13 && e.keyCode !== 32) return;\n    const params = swiper.params.a11y;\n    const targetEl = e.target;\n    if (swiper.pagination && swiper.pagination.el && (targetEl === swiper.pagination.el || swiper.pagination.el.contains(e.target))) {\n      if (!e.target.matches(classesToSelector(swiper.params.pagination.bulletClass))) return;\n    }\n    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {\n      const prevEls = makeElementsArray(swiper.navigation.prevEl);\n      const nextEls = makeElementsArray(swiper.navigation.nextEl);\n      if (nextEls.includes(targetEl)) {\n        if (!(swiper.isEnd && !swiper.params.loop)) {\n          swiper.slideNext();\n        }\n        if (swiper.isEnd) {\n          notify(params.lastSlideMessage);\n        } else {\n          notify(params.nextSlideMessage);\n        }\n      }\n      if (prevEls.includes(targetEl)) {\n        if (!(swiper.isBeginning && !swiper.params.loop)) {\n          swiper.slidePrev();\n        }\n        if (swiper.isBeginning) {\n          notify(params.firstSlideMessage);\n        } else {\n          notify(params.prevSlideMessage);\n        }\n      }\n    }\n    if (swiper.pagination && targetEl.matches(classesToSelector(swiper.params.pagination.bulletClass))) {\n      targetEl.click();\n    }\n  }\n  function updateNavigation() {\n    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (prevEl) {\n      if (swiper.isBeginning) {\n        disableEl(prevEl);\n        makeElNotFocusable(prevEl);\n      } else {\n        enableEl(prevEl);\n        makeElFocusable(prevEl);\n      }\n    }\n    if (nextEl) {\n      if (swiper.isEnd) {\n        disableEl(nextEl);\n        makeElNotFocusable(nextEl);\n      } else {\n        enableEl(nextEl);\n        makeElFocusable(nextEl);\n      }\n    }\n  }\n  function hasPagination() {\n    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length;\n  }\n  function hasClickablePagination() {\n    return hasPagination() && swiper.params.pagination.clickable;\n  }\n  function updatePagination() {\n    const params = swiper.params.a11y;\n    if (!hasPagination()) return;\n    swiper.pagination.bullets.forEach(bulletEl => {\n      if (swiper.params.pagination.clickable) {\n        makeElFocusable(bulletEl);\n        if (!swiper.params.pagination.renderBullet) {\n          addElRole(bulletEl, 'button');\n          addElLabel(bulletEl, params.paginationBulletMessage.replace(/\\{\\{index\\}\\}/, elementIndex(bulletEl) + 1));\n        }\n      }\n      if (bulletEl.matches(classesToSelector(swiper.params.pagination.bulletActiveClass))) {\n        bulletEl.setAttribute('aria-current', 'true');\n      } else {\n        bulletEl.removeAttribute('aria-current');\n      }\n    });\n  }\n  const initNavEl = (el, wrapperId, message) => {\n    makeElFocusable(el);\n    if (el.tagName !== 'BUTTON') {\n      addElRole(el, 'button');\n      el.addEventListener('keydown', onEnterOrSpaceKey);\n    }\n    addElLabel(el, message);\n    addElControls(el, wrapperId);\n  };\n  const handlePointerDown = e => {\n    if (focusTargetSlideEl && focusTargetSlideEl !== e.target && !focusTargetSlideEl.contains(e.target)) {\n      preventFocusHandler = true;\n    }\n    swiper.a11y.clicked = true;\n  };\n  const handlePointerUp = () => {\n    preventFocusHandler = false;\n    requestAnimationFrame(() => {\n      requestAnimationFrame(() => {\n        if (!swiper.destroyed) {\n          swiper.a11y.clicked = false;\n        }\n      });\n    });\n  };\n  const onVisibilityChange = e => {\n    visibilityChangedTimestamp = new Date().getTime();\n  };\n  const handleFocus = e => {\n    if (swiper.a11y.clicked || !swiper.params.a11y.scrollOnFocus) return;\n    if (new Date().getTime() - visibilityChangedTimestamp < 100) return;\n    const slideEl = e.target.closest(`.${swiper.params.slideClass}, swiper-slide`);\n    if (!slideEl || !swiper.slides.includes(slideEl)) return;\n    focusTargetSlideEl = slideEl;\n    const isActive = swiper.slides.indexOf(slideEl) === swiper.activeIndex;\n    const isVisible = swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideEl);\n    if (isActive || isVisible) return;\n    if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;\n    if (swiper.isHorizontal()) {\n      swiper.el.scrollLeft = 0;\n    } else {\n      swiper.el.scrollTop = 0;\n    }\n    requestAnimationFrame(() => {\n      if (preventFocusHandler) return;\n      if (swiper.params.loop) {\n        swiper.slideToLoop(parseInt(slideEl.getAttribute('data-swiper-slide-index')), 0);\n      } else {\n        swiper.slideTo(swiper.slides.indexOf(slideEl), 0);\n      }\n      preventFocusHandler = false;\n    });\n  };\n  const initSlides = () => {\n    const params = swiper.params.a11y;\n    if (params.itemRoleDescriptionMessage) {\n      addElRoleDescription(swiper.slides, params.itemRoleDescriptionMessage);\n    }\n    if (params.slideRole) {\n      addElRole(swiper.slides, params.slideRole);\n    }\n    const slidesLength = swiper.slides.length;\n    if (params.slideLabelMessage) {\n      swiper.slides.forEach((slideEl, index) => {\n        const slideIndex = swiper.params.loop ? parseInt(slideEl.getAttribute('data-swiper-slide-index'), 10) : index;\n        const ariaLabelMessage = params.slideLabelMessage.replace(/\\{\\{index\\}\\}/, slideIndex + 1).replace(/\\{\\{slidesLength\\}\\}/, slidesLength);\n        addElLabel(slideEl, ariaLabelMessage);\n      });\n    }\n  };\n  const init = () => {\n    const params = swiper.params.a11y;\n    swiper.el.append(liveRegion);\n\n    // Container\n    const containerEl = swiper.el;\n    if (params.containerRoleDescriptionMessage) {\n      addElRoleDescription(containerEl, params.containerRoleDescriptionMessage);\n    }\n    if (params.containerMessage) {\n      addElLabel(containerEl, params.containerMessage);\n    }\n    if (params.containerRole) {\n      addElRole(containerEl, params.containerRole);\n    }\n\n    // Wrapper\n    const wrapperEl = swiper.wrapperEl;\n    const wrapperId = params.id || wrapperEl.getAttribute('id') || `swiper-wrapper-${getRandomNumber(16)}`;\n    const live = swiper.params.autoplay && swiper.params.autoplay.enabled ? 'off' : 'polite';\n    addElId(wrapperEl, wrapperId);\n    addElLive(wrapperEl, live);\n\n    // Slide\n    initSlides();\n\n    // Navigation\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => initNavEl(el, wrapperId, params.nextSlideMessage));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => initNavEl(el, wrapperId, params.prevSlideMessage));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.addEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n\n    // Tab focus\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('focus', handleFocus, true);\n    swiper.el.addEventListener('pointerdown', handlePointerDown, true);\n    swiper.el.addEventListener('pointerup', handlePointerUp, true);\n  };\n  function destroy() {\n    if (liveRegion) liveRegion.remove();\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation ? swiper.navigation : {};\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (nextEl) {\n      nextEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n    if (prevEl) {\n      prevEl.forEach(el => el.removeEventListener('keydown', onEnterOrSpaceKey));\n    }\n\n    // Pagination\n    if (hasClickablePagination()) {\n      const paginationEl = makeElementsArray(swiper.pagination.el);\n      paginationEl.forEach(el => {\n        el.removeEventListener('keydown', onEnterOrSpaceKey);\n      });\n    }\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n    // Tab focus\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('focus', handleFocus, true);\n      swiper.el.removeEventListener('pointerdown', handlePointerDown, true);\n      swiper.el.removeEventListener('pointerup', handlePointerUp, true);\n    }\n  }\n  on('beforeInit', () => {\n    liveRegion = createElement('span', swiper.params.a11y.notificationClass);\n    liveRegion.setAttribute('aria-live', 'assertive');\n    liveRegion.setAttribute('aria-atomic', 'true');\n  });\n  on('afterInit', () => {\n    if (!swiper.params.a11y.enabled) return;\n    init();\n  });\n  on('slidesLengthChange snapGridLengthChange slidesGridLengthChange', () => {\n    if (!swiper.params.a11y.enabled) return;\n    initSlides();\n  });\n  on('fromEdge toEdge afterInit lock unlock', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updateNavigation();\n  });\n  on('paginationUpdate', () => {\n    if (!swiper.params.a11y.enabled) return;\n    updatePagination();\n  });\n  on('destroy', () => {\n    if (!swiper.params.a11y.enabled) return;\n    destroy();\n  });\n}\n\nexport { A11y as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;AAC/D,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,mCAAmC;AAC1E,SAASD,CAAC,IAAIE,aAAa,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAEtH,SAASC,IAAIA,CAACC,IAAI,EAAE;EAClB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,IAAI,EAAE;MACJC,OAAO,EAAE,IAAI;MACbC,iBAAiB,EAAE,qBAAqB;MACxCC,gBAAgB,EAAE,gBAAgB;MAClCC,gBAAgB,EAAE,YAAY;MAC9BC,iBAAiB,EAAE,yBAAyB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,uBAAuB,EAAE,uBAAuB;MAChDC,iBAAiB,EAAE,8BAA8B;MACjDC,gBAAgB,EAAE,IAAI;MACtBC,+BAA+B,EAAE,IAAI;MACrCC,aAAa,EAAE,IAAI;MACnBC,0BAA0B,EAAE,IAAI;MAChCC,SAAS,EAAE,OAAO;MAClBC,EAAE,EAAE,IAAI;MACRC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACFlB,MAAM,CAACG,IAAI,GAAG;IACZgB,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,mBAAmB;EACvB,IAAIC,kBAAkB;EACtB,IAAIC,0BAA0B,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACrD,SAASC,MAAMA,CAACC,OAAO,EAAE;IACvB,MAAMC,YAAY,GAAGR,UAAU;IAC/B,IAAIQ,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;IAC/BhC,YAAY,CAAC+B,YAAY,EAAED,OAAO,CAAC;EACrC;EACA,SAASG,eAAeA,CAACC,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,EAAE;IACX;IACA,MAAMC,UAAU,GAAGA,CAAA,KAAMC,IAAI,CAACC,KAAK,CAAC,EAAE,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACpE,OAAO,GAAG,CAACC,MAAM,CAACN,IAAI,CAAC,CAACO,OAAO,CAAC,IAAI,EAAEN,UAAU,CAAC;EACnD;EACA,SAASO,eAAeA,CAACC,EAAE,EAAE;IAC3BA,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,CAAC,CAAC;EACJ;EACA,SAASC,kBAAkBA,CAACJ,EAAE,EAAE;IAC9BA,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACJ;EACA,SAASE,SAASA,CAACL,EAAE,EAAEM,IAAI,EAAE;IAC3BN,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,MAAM,EAAEG,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ;EACA,SAASC,oBAAoBA,CAACP,EAAE,EAAEQ,WAAW,EAAE;IAC7CR,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,sBAAsB,EAAEK,WAAW,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,SAASC,aAAaA,CAACT,EAAE,EAAEU,QAAQ,EAAE;IACnCV,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAEO,QAAQ,CAAC;IAC/C,CAAC,CAAC;EACJ;EACA,SAASC,UAAUA,CAACX,EAAE,EAAEY,KAAK,EAAE;IAC7BZ,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,YAAY,EAAES,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;EACA,SAASC,OAAOA,CAACb,EAAE,EAAEvB,EAAE,EAAE;IACvBuB,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,IAAI,EAAE1B,EAAE,CAAC;IAC9B,CAAC,CAAC;EACJ;EACA,SAASqC,SAASA,CAACd,EAAE,EAAEe,IAAI,EAAE;IAC3Bf,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,WAAW,EAAEY,IAAI,CAAC;IACvC,CAAC,CAAC;EACJ;EACA,SAASC,SAASA,CAAChB,EAAE,EAAE;IACrBA,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;IAC3C,CAAC,CAAC;EACJ;EACA,SAASc,QAAQA,CAACjB,EAAE,EAAE;IACpBA,EAAE,GAAG7C,iBAAiB,CAAC6C,EAAE,CAAC;IAC1BA,EAAE,CAACC,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACC,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC;IAC5C,CAAC,CAAC;EACJ;EACA,SAASe,iBAAiBA,CAACC,CAAC,EAAE;IAC5B,IAAIA,CAAC,CAACC,OAAO,KAAK,EAAE,IAAID,CAAC,CAACC,OAAO,KAAK,EAAE,EAAE;IAC1C,MAAMC,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,MAAM2D,QAAQ,GAAGH,CAAC,CAACI,MAAM;IACzB,IAAI/D,MAAM,CAACgE,UAAU,IAAIhE,MAAM,CAACgE,UAAU,CAACxB,EAAE,KAAKsB,QAAQ,KAAK9D,MAAM,CAACgE,UAAU,CAACxB,EAAE,IAAIxC,MAAM,CAACgE,UAAU,CAACxB,EAAE,CAACyB,QAAQ,CAACN,CAAC,CAACI,MAAM,CAAC,CAAC,EAAE;MAC/H,IAAI,CAACJ,CAAC,CAACI,MAAM,CAACG,OAAO,CAAC5E,iBAAiB,CAACU,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;IAClF;IACA,IAAInE,MAAM,CAACoE,UAAU,IAAIpE,MAAM,CAACoE,UAAU,CAACC,MAAM,IAAIrE,MAAM,CAACoE,UAAU,CAACE,MAAM,EAAE;MAC7E,MAAMC,OAAO,GAAG5E,iBAAiB,CAACK,MAAM,CAACoE,UAAU,CAACC,MAAM,CAAC;MAC3D,MAAMG,OAAO,GAAG7E,iBAAiB,CAACK,MAAM,CAACoE,UAAU,CAACE,MAAM,CAAC;MAC3D,IAAIE,OAAO,CAACC,QAAQ,CAACX,QAAQ,CAAC,EAAE;QAC9B,IAAI,EAAE9D,MAAM,CAAC0E,KAAK,IAAI,CAAC1E,MAAM,CAAC6D,MAAM,CAACc,IAAI,CAAC,EAAE;UAC1C3E,MAAM,CAAC4E,SAAS,CAAC,CAAC;QACpB;QACA,IAAI5E,MAAM,CAAC0E,KAAK,EAAE;UAChBhD,MAAM,CAACmC,MAAM,CAACpD,gBAAgB,CAAC;QACjC,CAAC,MAAM;UACLiB,MAAM,CAACmC,MAAM,CAACtD,gBAAgB,CAAC;QACjC;MACF;MACA,IAAIgE,OAAO,CAACE,QAAQ,CAACX,QAAQ,CAAC,EAAE;QAC9B,IAAI,EAAE9D,MAAM,CAAC6E,WAAW,IAAI,CAAC7E,MAAM,CAAC6D,MAAM,CAACc,IAAI,CAAC,EAAE;UAChD3E,MAAM,CAAC8E,SAAS,CAAC,CAAC;QACpB;QACA,IAAI9E,MAAM,CAAC6E,WAAW,EAAE;UACtBnD,MAAM,CAACmC,MAAM,CAACrD,iBAAiB,CAAC;QAClC,CAAC,MAAM;UACLkB,MAAM,CAACmC,MAAM,CAACvD,gBAAgB,CAAC;QACjC;MACF;IACF;IACA,IAAIN,MAAM,CAACgE,UAAU,IAAIF,QAAQ,CAACI,OAAO,CAAC5E,iBAAiB,CAACU,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACG,WAAW,CAAC,CAAC,EAAE;MAClGL,QAAQ,CAACiB,KAAK,CAAC,CAAC;IAClB;EACF;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAIhF,MAAM,CAAC6D,MAAM,CAACc,IAAI,IAAI3E,MAAM,CAAC6D,MAAM,CAACoB,MAAM,IAAI,CAACjF,MAAM,CAACoE,UAAU,EAAE;IACtE,MAAM;MACJE,MAAM;MACND;IACF,CAAC,GAAGrE,MAAM,CAACoE,UAAU;IACrB,IAAIC,MAAM,EAAE;MACV,IAAIrE,MAAM,CAAC6E,WAAW,EAAE;QACtBrB,SAAS,CAACa,MAAM,CAAC;QACjBzB,kBAAkB,CAACyB,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLZ,QAAQ,CAACY,MAAM,CAAC;QAChB9B,eAAe,CAAC8B,MAAM,CAAC;MACzB;IACF;IACA,IAAIC,MAAM,EAAE;MACV,IAAItE,MAAM,CAAC0E,KAAK,EAAE;QAChBlB,SAAS,CAACc,MAAM,CAAC;QACjB1B,kBAAkB,CAAC0B,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLb,QAAQ,CAACa,MAAM,CAAC;QAChB/B,eAAe,CAAC+B,MAAM,CAAC;MACzB;IACF;EACF;EACA,SAASY,aAAaA,CAAA,EAAG;IACvB,OAAOlF,MAAM,CAACgE,UAAU,IAAIhE,MAAM,CAACgE,UAAU,CAACmB,OAAO,IAAInF,MAAM,CAACgE,UAAU,CAACmB,OAAO,CAACtD,MAAM;EAC3F;EACA,SAASuD,sBAAsBA,CAAA,EAAG;IAChC,OAAOF,aAAa,CAAC,CAAC,IAAIlF,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACqB,SAAS;EAC9D;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,MAAMzB,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,IAAI,CAAC+E,aAAa,CAAC,CAAC,EAAE;IACtBlF,MAAM,CAACgE,UAAU,CAACmB,OAAO,CAAC1C,OAAO,CAAC8C,QAAQ,IAAI;MAC5C,IAAIvF,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACqB,SAAS,EAAE;QACtC9C,eAAe,CAACgD,QAAQ,CAAC;QACzB,IAAI,CAACvF,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACwB,YAAY,EAAE;UAC1C3C,SAAS,CAAC0C,QAAQ,EAAE,QAAQ,CAAC;UAC7BpC,UAAU,CAACoC,QAAQ,EAAE1B,MAAM,CAACnD,uBAAuB,CAAC4B,OAAO,CAAC,eAAe,EAAE7C,YAAY,CAAC8F,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC3G;MACF;MACA,IAAIA,QAAQ,CAACrB,OAAO,CAAC5E,iBAAiB,CAACU,MAAM,CAAC6D,MAAM,CAACG,UAAU,CAACyB,iBAAiB,CAAC,CAAC,EAAE;QACnFF,QAAQ,CAAC5C,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC;MAC/C,CAAC,MAAM;QACL4C,QAAQ,CAACG,eAAe,CAAC,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ;EACA,MAAMC,SAAS,GAAGA,CAACnD,EAAE,EAAEoD,SAAS,EAAEjE,OAAO,KAAK;IAC5CY,eAAe,CAACC,EAAE,CAAC;IACnB,IAAIA,EAAE,CAACqD,OAAO,KAAK,QAAQ,EAAE;MAC3BhD,SAAS,CAACL,EAAE,EAAE,QAAQ,CAAC;MACvBA,EAAE,CAACsD,gBAAgB,CAAC,SAAS,EAAEpC,iBAAiB,CAAC;IACnD;IACAP,UAAU,CAACX,EAAE,EAAEb,OAAO,CAAC;IACvBsB,aAAa,CAACT,EAAE,EAAEoD,SAAS,CAAC;EAC9B,CAAC;EACD,MAAMG,iBAAiB,GAAGpC,CAAC,IAAI;IAC7B,IAAIrC,kBAAkB,IAAIA,kBAAkB,KAAKqC,CAAC,CAACI,MAAM,IAAI,CAACzC,kBAAkB,CAAC2C,QAAQ,CAACN,CAAC,CAACI,MAAM,CAAC,EAAE;MACnG1C,mBAAmB,GAAG,IAAI;IAC5B;IACArB,MAAM,CAACG,IAAI,CAACgB,OAAO,GAAG,IAAI;EAC5B,CAAC;EACD,MAAM6E,eAAe,GAAGA,CAAA,KAAM;IAC5B3E,mBAAmB,GAAG,KAAK;IAC3B4E,qBAAqB,CAAC,MAAM;MAC1BA,qBAAqB,CAAC,MAAM;QAC1B,IAAI,CAACjG,MAAM,CAACkG,SAAS,EAAE;UACrBlG,MAAM,CAACG,IAAI,CAACgB,OAAO,GAAG,KAAK;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,MAAMgF,kBAAkB,GAAGxC,CAAC,IAAI;IAC9BpC,0BAA0B,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACnD,CAAC;EACD,MAAM2E,WAAW,GAAGzC,CAAC,IAAI;IACvB,IAAI3D,MAAM,CAACG,IAAI,CAACgB,OAAO,IAAI,CAACnB,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACe,aAAa,EAAE;IAC9D,IAAI,IAAIM,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGF,0BAA0B,GAAG,GAAG,EAAE;IAC7D,MAAM8E,OAAO,GAAG1C,CAAC,CAACI,MAAM,CAACuC,OAAO,CAAC,IAAItG,MAAM,CAAC6D,MAAM,CAAC0C,UAAU,gBAAgB,CAAC;IAC9E,IAAI,CAACF,OAAO,IAAI,CAACrG,MAAM,CAACwG,MAAM,CAAC/B,QAAQ,CAAC4B,OAAO,CAAC,EAAE;IAClD/E,kBAAkB,GAAG+E,OAAO;IAC5B,MAAMI,QAAQ,GAAGzG,MAAM,CAACwG,MAAM,CAACE,OAAO,CAACL,OAAO,CAAC,KAAKrG,MAAM,CAAC2G,WAAW;IACtE,MAAMC,SAAS,GAAG5G,MAAM,CAAC6D,MAAM,CAACgD,mBAAmB,IAAI7G,MAAM,CAAC8G,aAAa,IAAI9G,MAAM,CAAC8G,aAAa,CAACrC,QAAQ,CAAC4B,OAAO,CAAC;IACrH,IAAII,QAAQ,IAAIG,SAAS,EAAE;IAC3B,IAAIjD,CAAC,CAACoD,kBAAkB,IAAIpD,CAAC,CAACoD,kBAAkB,CAACC,gBAAgB,EAAE;IACnE,IAAIhH,MAAM,CAACiH,YAAY,CAAC,CAAC,EAAE;MACzBjH,MAAM,CAACwC,EAAE,CAAC0E,UAAU,GAAG,CAAC;IAC1B,CAAC,MAAM;MACLlH,MAAM,CAACwC,EAAE,CAAC2E,SAAS,GAAG,CAAC;IACzB;IACAlB,qBAAqB,CAAC,MAAM;MAC1B,IAAI5E,mBAAmB,EAAE;MACzB,IAAIrB,MAAM,CAAC6D,MAAM,CAACc,IAAI,EAAE;QACtB3E,MAAM,CAACoH,WAAW,CAACC,QAAQ,CAAChB,OAAO,CAACiB,YAAY,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;MAClF,CAAC,MAAM;QACLtH,MAAM,CAACuH,OAAO,CAACvH,MAAM,CAACwG,MAAM,CAACE,OAAO,CAACL,OAAO,CAAC,EAAE,CAAC,CAAC;MACnD;MACAhF,mBAAmB,GAAG,KAAK;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD,MAAMmG,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAM3D,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjC,IAAI0D,MAAM,CAAC9C,0BAA0B,EAAE;MACrCgC,oBAAoB,CAAC/C,MAAM,CAACwG,MAAM,EAAE3C,MAAM,CAAC9C,0BAA0B,CAAC;IACxE;IACA,IAAI8C,MAAM,CAAC7C,SAAS,EAAE;MACpB6B,SAAS,CAAC7C,MAAM,CAACwG,MAAM,EAAE3C,MAAM,CAAC7C,SAAS,CAAC;IAC5C;IACA,MAAMyG,YAAY,GAAGzH,MAAM,CAACwG,MAAM,CAAC3E,MAAM;IACzC,IAAIgC,MAAM,CAAClD,iBAAiB,EAAE;MAC5BX,MAAM,CAACwG,MAAM,CAAC/D,OAAO,CAAC,CAAC4D,OAAO,EAAEqB,KAAK,KAAK;QACxC,MAAMC,UAAU,GAAG3H,MAAM,CAAC6D,MAAM,CAACc,IAAI,GAAG0C,QAAQ,CAAChB,OAAO,CAACiB,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC,GAAGI,KAAK;QAC7G,MAAME,gBAAgB,GAAG/D,MAAM,CAAClD,iBAAiB,CAAC2B,OAAO,CAAC,eAAe,EAAEqF,UAAU,GAAG,CAAC,CAAC,CAACrF,OAAO,CAAC,sBAAsB,EAAEmF,YAAY,CAAC;QACxItE,UAAU,CAACkD,OAAO,EAAEuB,gBAAgB,CAAC;MACvC,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAMC,IAAI,GAAGA,CAAA,KAAM;IACjB,MAAMhE,MAAM,GAAG7D,MAAM,CAAC6D,MAAM,CAAC1D,IAAI;IACjCH,MAAM,CAACwC,EAAE,CAACsF,MAAM,CAAC1G,UAAU,CAAC;;IAE5B;IACA,MAAM2G,WAAW,GAAG/H,MAAM,CAACwC,EAAE;IAC7B,IAAIqB,MAAM,CAAChD,+BAA+B,EAAE;MAC1CkC,oBAAoB,CAACgF,WAAW,EAAElE,MAAM,CAAChD,+BAA+B,CAAC;IAC3E;IACA,IAAIgD,MAAM,CAACjD,gBAAgB,EAAE;MAC3BuC,UAAU,CAAC4E,WAAW,EAAElE,MAAM,CAACjD,gBAAgB,CAAC;IAClD;IACA,IAAIiD,MAAM,CAAC/C,aAAa,EAAE;MACxB+B,SAAS,CAACkF,WAAW,EAAElE,MAAM,CAAC/C,aAAa,CAAC;IAC9C;;IAEA;IACA,MAAMkH,SAAS,GAAGhI,MAAM,CAACgI,SAAS;IAClC,MAAMpC,SAAS,GAAG/B,MAAM,CAAC5C,EAAE,IAAI+G,SAAS,CAACV,YAAY,CAAC,IAAI,CAAC,IAAI,kBAAkBxF,eAAe,CAAC,EAAE,CAAC,EAAE;IACtG,MAAMyB,IAAI,GAAGvD,MAAM,CAAC6D,MAAM,CAACoE,QAAQ,IAAIjI,MAAM,CAAC6D,MAAM,CAACoE,QAAQ,CAAC7H,OAAO,GAAG,KAAK,GAAG,QAAQ;IACxFiD,OAAO,CAAC2E,SAAS,EAAEpC,SAAS,CAAC;IAC7BtC,SAAS,CAAC0E,SAAS,EAAEzE,IAAI,CAAC;;IAE1B;IACAiE,UAAU,CAAC,CAAC;;IAEZ;IACA,IAAI;MACFlD,MAAM;MACND;IACF,CAAC,GAAGrE,MAAM,CAACoE,UAAU,GAAGpE,MAAM,CAACoE,UAAU,GAAG,CAAC,CAAC;IAC9CE,MAAM,GAAG3E,iBAAiB,CAAC2E,MAAM,CAAC;IAClCD,MAAM,GAAG1E,iBAAiB,CAAC0E,MAAM,CAAC;IAClC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAC7B,OAAO,CAACD,EAAE,IAAImD,SAAS,CAACnD,EAAE,EAAEoD,SAAS,EAAE/B,MAAM,CAACtD,gBAAgB,CAAC,CAAC;IACzE;IACA,IAAI8D,MAAM,EAAE;MACVA,MAAM,CAAC5B,OAAO,CAACD,EAAE,IAAImD,SAAS,CAACnD,EAAE,EAAEoD,SAAS,EAAE/B,MAAM,CAACvD,gBAAgB,CAAC,CAAC;IACzE;;IAEA;IACA,IAAI8E,sBAAsB,CAAC,CAAC,EAAE;MAC5B,MAAM8C,YAAY,GAAGvI,iBAAiB,CAACK,MAAM,CAACgE,UAAU,CAACxB,EAAE,CAAC;MAC5D0F,YAAY,CAACzF,OAAO,CAACD,EAAE,IAAI;QACzBA,EAAE,CAACsD,gBAAgB,CAAC,SAAS,EAAEpC,iBAAiB,CAAC;MACnD,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMyE,QAAQ,GAAG/I,WAAW,CAAC,CAAC;IAC9B+I,QAAQ,CAACrC,gBAAgB,CAAC,kBAAkB,EAAEK,kBAAkB,CAAC;IACjEnG,MAAM,CAACwC,EAAE,CAACsD,gBAAgB,CAAC,OAAO,EAAEM,WAAW,EAAE,IAAI,CAAC;IACtDpG,MAAM,CAACwC,EAAE,CAACsD,gBAAgB,CAAC,OAAO,EAAEM,WAAW,EAAE,IAAI,CAAC;IACtDpG,MAAM,CAACwC,EAAE,CAACsD,gBAAgB,CAAC,aAAa,EAAEC,iBAAiB,EAAE,IAAI,CAAC;IAClE/F,MAAM,CAACwC,EAAE,CAACsD,gBAAgB,CAAC,WAAW,EAAEE,eAAe,EAAE,IAAI,CAAC;EAChE,CAAC;EACD,SAASoC,OAAOA,CAAA,EAAG;IACjB,IAAIhH,UAAU,EAAEA,UAAU,CAACiH,MAAM,CAAC,CAAC;IACnC,IAAI;MACF/D,MAAM;MACND;IACF,CAAC,GAAGrE,MAAM,CAACoE,UAAU,GAAGpE,MAAM,CAACoE,UAAU,GAAG,CAAC,CAAC;IAC9CE,MAAM,GAAG3E,iBAAiB,CAAC2E,MAAM,CAAC;IAClCD,MAAM,GAAG1E,iBAAiB,CAAC0E,MAAM,CAAC;IAClC,IAAIC,MAAM,EAAE;MACVA,MAAM,CAAC7B,OAAO,CAACD,EAAE,IAAIA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC,CAAC;IAC5E;IACA,IAAIW,MAAM,EAAE;MACVA,MAAM,CAAC5B,OAAO,CAACD,EAAE,IAAIA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC,CAAC;IAC5E;;IAEA;IACA,IAAI0B,sBAAsB,CAAC,CAAC,EAAE;MAC5B,MAAM8C,YAAY,GAAGvI,iBAAiB,CAACK,MAAM,CAACgE,UAAU,CAACxB,EAAE,CAAC;MAC5D0F,YAAY,CAACzF,OAAO,CAACD,EAAE,IAAI;QACzBA,EAAE,CAAC8F,mBAAmB,CAAC,SAAS,EAAE5E,iBAAiB,CAAC;MACtD,CAAC,CAAC;IACJ;IACA,MAAMyE,QAAQ,GAAG/I,WAAW,CAAC,CAAC;IAC9B+I,QAAQ,CAACG,mBAAmB,CAAC,kBAAkB,EAAEnC,kBAAkB,CAAC;IACpE;IACA,IAAInG,MAAM,CAACwC,EAAE,IAAI,OAAOxC,MAAM,CAACwC,EAAE,KAAK,QAAQ,EAAE;MAC9CxC,MAAM,CAACwC,EAAE,CAAC8F,mBAAmB,CAAC,OAAO,EAAElC,WAAW,EAAE,IAAI,CAAC;MACzDpG,MAAM,CAACwC,EAAE,CAAC8F,mBAAmB,CAAC,aAAa,EAAEvC,iBAAiB,EAAE,IAAI,CAAC;MACrE/F,MAAM,CAACwC,EAAE,CAAC8F,mBAAmB,CAAC,WAAW,EAAEtC,eAAe,EAAE,IAAI,CAAC;IACnE;EACF;EACA9F,EAAE,CAAC,YAAY,EAAE,MAAM;IACrBkB,UAAU,GAAG7B,aAAa,CAAC,MAAM,EAAES,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACE,iBAAiB,CAAC;IACxEe,UAAU,CAACuB,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC;IACjDvB,UAAU,CAACuB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAChD,CAAC,CAAC;EACFzC,EAAE,CAAC,WAAW,EAAE,MAAM;IACpB,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCyH,IAAI,CAAC,CAAC;EACR,CAAC,CAAC;EACF3H,EAAE,CAAC,gEAAgE,EAAE,MAAM;IACzE,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCoH,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACFtH,EAAE,CAAC,uCAAuC,EAAE,MAAM;IAChD,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjC4E,gBAAgB,CAAC,CAAC;EACpB,CAAC,CAAC;EACF9E,EAAE,CAAC,kBAAkB,EAAE,MAAM;IAC3B,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCkF,gBAAgB,CAAC,CAAC;EACpB,CAAC,CAAC;EACFpF,EAAE,CAAC,SAAS,EAAE,MAAM;IAClB,IAAI,CAACF,MAAM,CAAC6D,MAAM,CAAC1D,IAAI,CAACC,OAAO,EAAE;IACjCgI,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;AACJ;AAEA,SAAStI,IAAI,IAAIyI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}