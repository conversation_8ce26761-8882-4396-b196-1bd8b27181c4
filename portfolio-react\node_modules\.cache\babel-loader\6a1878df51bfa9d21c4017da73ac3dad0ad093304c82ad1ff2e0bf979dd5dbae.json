{"ast": null, "code": "import React,{useEffect}from'react';import'./NDANotification.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NDANotification=_ref=>{let{isOpen,onClose,projectTitle}=_ref;useEffect(()=>{const handleEscape=e=>{if(e.key==='Escape'){onClose();}};if(isOpen){document.addEventListener('keydown',handleEscape);document.body.style.overflow='hidden';}return()=>{document.removeEventListener('keydown',handleEscape);document.body.style.overflow='unset';};},[isOpen,onClose]);if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"nda-overlay\",onClick:onClose,children:/*#__PURE__*/_jsxs(\"div\",{className:\"nda-content\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(\"div\",{className:\"nda-icon\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDD12\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"nda-title\",children:\"Project Under NDA\"}),/*#__PURE__*/_jsx(\"p\",{className:\"nda-message\",children:\"This project is protected under a Non-Disclosure Agreement (NDA). Due to confidentiality requirements, detailed information and live demos cannot be shared publicly.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"nda-details\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"What I can share:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"General technical scope and technologies used\"}),/*#__PURE__*/_jsx(\"li\",{children:\"My role and contributions to the project\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Skills and methodologies applied\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Overall project outcomes and achievements\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"nda-contact\",children:/*#__PURE__*/_jsx(\"p\",{children:\"For more information about this project, please contact me directly.\"})}),/*#__PURE__*/_jsx(\"button\",{className:\"nda-close-btn\",onClick:onClose,children:/*#__PURE__*/_jsx(\"span\",{children:\"Understood\"})})]})});};export default NDANotification;", "map": {"version": 3, "names": ["React", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "NDANotification", "_ref", "isOpen", "onClose", "projectTitle", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "className", "onClick", "children", "stopPropagation"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/NDANotification.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './NDANotification.css';\n\nconst NDANotification = ({ isOpen, onClose, projectTitle }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"nda-overlay\" onClick={onClose}>\n      <div className=\"nda-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"nda-icon\">\n          <span>🔒</span>\n        </div>\n        <h2 className=\"nda-title\">Project Under NDA</h2>\n        <p className=\"nda-message\">\n          This project is protected under a Non-Disclosure Agreement (NDA). \n          Due to confidentiality requirements, detailed information and live demos cannot be shared publicly.\n        </p>\n        <div className=\"nda-details\">\n          <h3>What I can share:</h3>\n          <ul>\n            <li>General technical scope and technologies used</li>\n            <li>My role and contributions to the project</li>\n            <li>Skills and methodologies applied</li>\n            <li>Overall project outcomes and achievements</li>\n          </ul>\n        </div>\n        <div className=\"nda-contact\">\n          <p>For more information about this project, please contact me directly.</p>\n        </div>\n        <button className=\"nda-close-btn\" onClick={onClose}>\n          <span>Understood</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default NDANotification;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,MAAO,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/B,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAuC,IAAtC,CAAEC,MAAM,CAAEC,OAAO,CAAEC,YAAa,CAAC,CAAAH,IAAA,CACxDN,SAAS,CAAC,IAAM,CACd,KAAM,CAAAU,YAAY,CAAIC,CAAC,EAAK,CAC1B,GAAIA,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CACtBJ,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,GAAID,MAAM,CAAE,CACVM,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEJ,YAAY,CAAC,CAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACzC,CAEA,MAAO,IAAM,CACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,CAAER,YAAY,CAAC,CACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,OAAO,CACxC,CAAC,CACH,CAAC,CAAE,CAACV,MAAM,CAAEC,OAAO,CAAC,CAAC,CAErB,GAAI,CAACD,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEL,IAAA,QAAKiB,SAAS,CAAC,aAAa,CAACC,OAAO,CAAEZ,OAAQ,CAAAa,QAAA,cAC5CjB,KAAA,QAAKe,SAAS,CAAC,aAAa,CAACC,OAAO,CAAGT,CAAC,EAAKA,CAAC,CAACW,eAAe,CAAC,CAAE,CAAAD,QAAA,eAC/DnB,IAAA,QAAKiB,SAAS,CAAC,UAAU,CAAAE,QAAA,cACvBnB,IAAA,SAAAmB,QAAA,CAAM,cAAE,CAAM,CAAC,CACZ,CAAC,cACNnB,IAAA,OAAIiB,SAAS,CAAC,WAAW,CAAAE,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAChDnB,IAAA,MAAGiB,SAAS,CAAC,aAAa,CAAAE,QAAA,CAAC,uKAG3B,CAAG,CAAC,cACJjB,KAAA,QAAKe,SAAS,CAAC,aAAa,CAAAE,QAAA,eAC1BnB,IAAA,OAAAmB,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BjB,KAAA,OAAAiB,QAAA,eACEnB,IAAA,OAAAmB,QAAA,CAAI,+CAA6C,CAAI,CAAC,cACtDnB,IAAA,OAAAmB,QAAA,CAAI,0CAAwC,CAAI,CAAC,cACjDnB,IAAA,OAAAmB,QAAA,CAAI,kCAAgC,CAAI,CAAC,cACzCnB,IAAA,OAAAmB,QAAA,CAAI,2CAAyC,CAAI,CAAC,EAChD,CAAC,EACF,CAAC,cACNnB,IAAA,QAAKiB,SAAS,CAAC,aAAa,CAAAE,QAAA,cAC1BnB,IAAA,MAAAmB,QAAA,CAAG,sEAAoE,CAAG,CAAC,CACxE,CAAC,cACNnB,IAAA,WAAQiB,SAAS,CAAC,eAAe,CAACC,OAAO,CAAEZ,OAAQ,CAAAa,QAAA,cACjDnB,IAAA,SAAAmB,QAAA,CAAM,YAAU,CAAM,CAAC,CACjB,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}