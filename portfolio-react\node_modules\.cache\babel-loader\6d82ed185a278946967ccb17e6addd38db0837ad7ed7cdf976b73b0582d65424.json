{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const location = useLocation();\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"nav\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        className: \"logo\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"/logo.PNG\",\n          alt: \"Med Amine Cho<PERSON>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"nav-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: location.pathname === '/' ? 'active' : '',\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/experience\",\n            className: location.pathname === '/experience' ? 'active' : '',\n            children: \"Experience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#contact\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Header", "_s", "location", "className", "children", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pathname", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Header = () => {\n  const location = useLocation();\n\n  return (\n    <header className=\"header\">\n      <nav className=\"nav\">\n        <Link to=\"/\" className=\"logo\">\n          <img src=\"/logo.PNG\" alt=\"Med Amine Chouchane\" />\n        </Link>\n        \n        <ul className=\"nav-links\">\n          <li>\n            <Link \n              to=\"/\" \n              className={location.pathname === '/' ? 'active' : ''}\n            >\n              Home\n            </Link>\n          </li>\n          <li>\n            <Link \n              to=\"/experience\" \n              className={location.pathname === '/experience' ? 'active' : ''}\n            >\n              Experience\n            </Link>\n          </li>\n          <li>\n            <a href=\"#contact\">Contact</a>\n          </li>\n        </ul>\n      </nav>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,oBACEE,OAAA;IAAQI,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBL,OAAA;MAAKI,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBL,OAAA,CAACH,IAAI;QAACS,EAAE,EAAC,GAAG;QAACF,SAAS,EAAC,MAAM;QAAAC,QAAA,eAC3BL,OAAA;UAAKO,GAAG,EAAC,WAAW;UAACC,GAAG,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAEPZ,OAAA;QAAII,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACvBL,OAAA;UAAAK,QAAA,eACEL,OAAA,CAACH,IAAI;YACHS,EAAE,EAAC,GAAG;YACNF,SAAS,EAAED,QAAQ,CAACU,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,EAAG;YAAAR,QAAA,EACtD;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLZ,OAAA;UAAAK,QAAA,eACEL,OAAA,CAACH,IAAI;YACHS,EAAE,EAAC,aAAa;YAChBF,SAAS,EAAED,QAAQ,CAACU,QAAQ,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAG;YAAAR,QAAA,EAChE;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACLZ,OAAA;UAAAK,QAAA,eACEL,OAAA;YAAGc,IAAI,EAAC,UAAU;YAAAT,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACV,EAAA,CAlCID,MAAM;EAAA,QACOH,WAAW;AAAA;AAAAiB,EAAA,GADxBd,MAAM;AAoCZ,eAAeA,MAAM;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}