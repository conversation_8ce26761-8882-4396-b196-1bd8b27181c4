{"ast": null, "code": "import { s as setInnerHTML } from '../shared/utils.mjs';\nfunction appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.append(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\nfunction prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.prepend(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\nfunction addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\nfunction removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\nfunction removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\nfunction Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}\nexport { Manipulation as default };", "map": {"version": 3, "names": ["s", "setInnerHTML", "appendSlide", "slides", "swiper", "params", "slidesEl", "loop", "loop<PERSON><PERSON><PERSON>", "appendElement", "slideEl", "tempDOM", "document", "createElement", "append", "children", "i", "length", "recalcSlides", "loopCreate", "observer", "isElement", "update", "prependSlide", "activeIndex", "newActiveIndex", "prependElement", "prepend", "slideTo", "addSlide", "index", "activeIndexBuffer", "loopedSlides", "baseLength", "slidesBuffer", "currentSlide", "remove", "unshift", "removeSlide", "slidesIndexes", "indexToRemove", "Math", "max", "removeAllSlides", "push", "Manipulation", "_ref", "Object", "assign", "bind", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/manipulation.mjs"], "sourcesContent": ["import { s as setInnerHTML } from '../shared/utils.mjs';\n\nfunction appendSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  const appendElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.append(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.append(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) appendElement(slides[i]);\n    }\n  } else {\n    appendElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n}\n\nfunction prependSlide(slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  if (params.loop) {\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndex + 1;\n  const prependElement = slideEl => {\n    if (typeof slideEl === 'string') {\n      const tempDOM = document.createElement('div');\n      setInnerHTML(tempDOM, slideEl);\n      slidesEl.prepend(tempDOM.children[0]);\n      setInnerHTML(tempDOM, '');\n    } else {\n      slidesEl.prepend(slideEl);\n    }\n  };\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) prependElement(slides[i]);\n    }\n    newActiveIndex = activeIndex + slides.length;\n  } else {\n    prependElement(slides);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  swiper.slideTo(newActiveIndex, 0, false);\n}\n\nfunction addSlide(index, slides) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex,\n    slidesEl\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n    swiper.recalcSlides();\n  }\n  const baseLength = swiper.slides.length;\n  if (index <= 0) {\n    swiper.prependSlide(slides);\n    return;\n  }\n  if (index >= baseLength) {\n    swiper.appendSlide(slides);\n    return;\n  }\n  let newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + 1 : activeIndexBuffer;\n  const slidesBuffer = [];\n  for (let i = baseLength - 1; i >= index; i -= 1) {\n    const currentSlide = swiper.slides[i];\n    currentSlide.remove();\n    slidesBuffer.unshift(currentSlide);\n  }\n  if (typeof slides === 'object' && 'length' in slides) {\n    for (let i = 0; i < slides.length; i += 1) {\n      if (slides[i]) slidesEl.append(slides[i]);\n    }\n    newActiveIndex = activeIndexBuffer > index ? activeIndexBuffer + slides.length : activeIndexBuffer;\n  } else {\n    slidesEl.append(slides);\n  }\n  for (let i = 0; i < slidesBuffer.length; i += 1) {\n    slidesEl.append(slidesBuffer[i]);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeSlide(slidesIndexes) {\n  const swiper = this;\n  const {\n    params,\n    activeIndex\n  } = swiper;\n  let activeIndexBuffer = activeIndex;\n  if (params.loop) {\n    activeIndexBuffer -= swiper.loopedSlides;\n    swiper.loopDestroy();\n  }\n  let newActiveIndex = activeIndexBuffer;\n  let indexToRemove;\n  if (typeof slidesIndexes === 'object' && 'length' in slidesIndexes) {\n    for (let i = 0; i < slidesIndexes.length; i += 1) {\n      indexToRemove = slidesIndexes[i];\n      if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n      if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    }\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  } else {\n    indexToRemove = slidesIndexes;\n    if (swiper.slides[indexToRemove]) swiper.slides[indexToRemove].remove();\n    if (indexToRemove < newActiveIndex) newActiveIndex -= 1;\n    newActiveIndex = Math.max(newActiveIndex, 0);\n  }\n  swiper.recalcSlides();\n  if (params.loop) {\n    swiper.loopCreate();\n  }\n  if (!params.observer || swiper.isElement) {\n    swiper.update();\n  }\n  if (params.loop) {\n    swiper.slideTo(newActiveIndex + swiper.loopedSlides, 0, false);\n  } else {\n    swiper.slideTo(newActiveIndex, 0, false);\n  }\n}\n\nfunction removeAllSlides() {\n  const swiper = this;\n  const slidesIndexes = [];\n  for (let i = 0; i < swiper.slides.length; i += 1) {\n    slidesIndexes.push(i);\n  }\n  swiper.removeSlide(slidesIndexes);\n}\n\nfunction Manipulation(_ref) {\n  let {\n    swiper\n  } = _ref;\n  Object.assign(swiper, {\n    appendSlide: appendSlide.bind(swiper),\n    prependSlide: prependSlide.bind(swiper),\n    addSlide: addSlide.bind(swiper),\n    removeSlide: removeSlide.bind(swiper),\n    removeAllSlides: removeAllSlides.bind(swiper)\n  });\n}\n\nexport { Manipulation as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,YAAY,QAAQ,qBAAqB;AAEvD,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNC;EACF,CAAC,GAAGF,MAAM;EACV,IAAIC,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,MAAMC,aAAa,GAAGC,OAAO,IAAI;IAC/B,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CZ,YAAY,CAACU,OAAO,EAAED,OAAO,CAAC;MAC9BJ,QAAQ,CAACQ,MAAM,CAACH,OAAO,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpCd,YAAY,CAACU,OAAO,EAAE,EAAE,CAAC;IAC3B,CAAC,MAAM;MACLL,QAAQ,CAACQ,MAAM,CAACJ,OAAO,CAAC;IAC1B;EACF,CAAC;EACD,IAAI,OAAOP,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACc,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIb,MAAM,CAACa,CAAC,CAAC,EAAEP,aAAa,CAACN,MAAM,CAACa,CAAC,CAAC,CAAC;IACzC;EACF,CAAC,MAAM;IACLP,aAAa,CAACN,MAAM,CAAC;EACvB;EACAC,MAAM,CAACc,YAAY,CAAC,CAAC;EACrB,IAAIb,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACe,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACd,MAAM,CAACe,QAAQ,IAAIhB,MAAM,CAACiB,SAAS,EAAE;IACxCjB,MAAM,CAACkB,MAAM,CAAC,CAAC;EACjB;AACF;AAEA,SAASC,YAAYA,CAACpB,MAAM,EAAE;EAC5B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNmB,WAAW;IACXlB;EACF,CAAC,GAAGF,MAAM;EACV,IAAIC,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,IAAIiB,cAAc,GAAGD,WAAW,GAAG,CAAC;EACpC,MAAME,cAAc,GAAGhB,OAAO,IAAI;IAChC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MAC/B,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CZ,YAAY,CAACU,OAAO,EAAED,OAAO,CAAC;MAC9BJ,QAAQ,CAACqB,OAAO,CAAChB,OAAO,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;MACrCd,YAAY,CAACU,OAAO,EAAE,EAAE,CAAC;IAC3B,CAAC,MAAM;MACLL,QAAQ,CAACqB,OAAO,CAACjB,OAAO,CAAC;IAC3B;EACF,CAAC;EACD,IAAI,OAAOP,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACc,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIb,MAAM,CAACa,CAAC,CAAC,EAAEU,cAAc,CAACvB,MAAM,CAACa,CAAC,CAAC,CAAC;IAC1C;IACAS,cAAc,GAAGD,WAAW,GAAGrB,MAAM,CAACc,MAAM;EAC9C,CAAC,MAAM;IACLS,cAAc,CAACvB,MAAM,CAAC;EACxB;EACAC,MAAM,CAACc,YAAY,CAAC,CAAC;EACrB,IAAIb,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACe,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACd,MAAM,CAACe,QAAQ,IAAIhB,MAAM,CAACiB,SAAS,EAAE;IACxCjB,MAAM,CAACkB,MAAM,CAAC,CAAC;EACjB;EACAlB,MAAM,CAACwB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;AAC1C;AAEA,SAASI,QAAQA,CAACC,KAAK,EAAE3B,MAAM,EAAE;EAC/B,MAAMC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNmB,WAAW;IACXlB;EACF,CAAC,GAAGF,MAAM;EACV,IAAI2B,iBAAiB,GAAGP,WAAW;EACnC,IAAInB,MAAM,CAACE,IAAI,EAAE;IACfwB,iBAAiB,IAAI3B,MAAM,CAAC4B,YAAY;IACxC5B,MAAM,CAACI,WAAW,CAAC,CAAC;IACpBJ,MAAM,CAACc,YAAY,CAAC,CAAC;EACvB;EACA,MAAMe,UAAU,GAAG7B,MAAM,CAACD,MAAM,CAACc,MAAM;EACvC,IAAIa,KAAK,IAAI,CAAC,EAAE;IACd1B,MAAM,CAACmB,YAAY,CAACpB,MAAM,CAAC;IAC3B;EACF;EACA,IAAI2B,KAAK,IAAIG,UAAU,EAAE;IACvB7B,MAAM,CAACF,WAAW,CAACC,MAAM,CAAC;IAC1B;EACF;EACA,IAAIsB,cAAc,GAAGM,iBAAiB,GAAGD,KAAK,GAAGC,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;EAC1F,MAAMG,YAAY,GAAG,EAAE;EACvB,KAAK,IAAIlB,CAAC,GAAGiB,UAAU,GAAG,CAAC,EAAEjB,CAAC,IAAIc,KAAK,EAAEd,CAAC,IAAI,CAAC,EAAE;IAC/C,MAAMmB,YAAY,GAAG/B,MAAM,CAACD,MAAM,CAACa,CAAC,CAAC;IACrCmB,YAAY,CAACC,MAAM,CAAC,CAAC;IACrBF,YAAY,CAACG,OAAO,CAACF,YAAY,CAAC;EACpC;EACA,IAAI,OAAOhC,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,EAAE;IACpD,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,MAAM,CAACc,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,IAAIb,MAAM,CAACa,CAAC,CAAC,EAAEV,QAAQ,CAACQ,MAAM,CAACX,MAAM,CAACa,CAAC,CAAC,CAAC;IAC3C;IACAS,cAAc,GAAGM,iBAAiB,GAAGD,KAAK,GAAGC,iBAAiB,GAAG5B,MAAM,CAACc,MAAM,GAAGc,iBAAiB;EACpG,CAAC,MAAM;IACLzB,QAAQ,CAACQ,MAAM,CAACX,MAAM,CAAC;EACzB;EACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,YAAY,CAACjB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAC/CV,QAAQ,CAACQ,MAAM,CAACoB,YAAY,CAAClB,CAAC,CAAC,CAAC;EAClC;EACAZ,MAAM,CAACc,YAAY,CAAC,CAAC;EACrB,IAAIb,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACe,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACd,MAAM,CAACe,QAAQ,IAAIhB,MAAM,CAACiB,SAAS,EAAE;IACxCjB,MAAM,CAACkB,MAAM,CAAC,CAAC;EACjB;EACA,IAAIjB,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACwB,OAAO,CAACH,cAAc,GAAGrB,MAAM,CAAC4B,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACL5B,MAAM,CAACwB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF;AAEA,SAASa,WAAWA,CAACC,aAAa,EAAE;EAClC,MAAMnC,MAAM,GAAG,IAAI;EACnB,MAAM;IACJC,MAAM;IACNmB;EACF,CAAC,GAAGpB,MAAM;EACV,IAAI2B,iBAAiB,GAAGP,WAAW;EACnC,IAAInB,MAAM,CAACE,IAAI,EAAE;IACfwB,iBAAiB,IAAI3B,MAAM,CAAC4B,YAAY;IACxC5B,MAAM,CAACI,WAAW,CAAC,CAAC;EACtB;EACA,IAAIiB,cAAc,GAAGM,iBAAiB;EACtC,IAAIS,aAAa;EACjB,IAAI,OAAOD,aAAa,KAAK,QAAQ,IAAI,QAAQ,IAAIA,aAAa,EAAE;IAClE,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,aAAa,CAACtB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAChDwB,aAAa,GAAGD,aAAa,CAACvB,CAAC,CAAC;MAChC,IAAIZ,MAAM,CAACD,MAAM,CAACqC,aAAa,CAAC,EAAEpC,MAAM,CAACD,MAAM,CAACqC,aAAa,CAAC,CAACJ,MAAM,CAAC,CAAC;MACvE,IAAII,aAAa,GAAGf,cAAc,EAAEA,cAAc,IAAI,CAAC;IACzD;IACAA,cAAc,GAAGgB,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLe,aAAa,GAAGD,aAAa;IAC7B,IAAInC,MAAM,CAACD,MAAM,CAACqC,aAAa,CAAC,EAAEpC,MAAM,CAACD,MAAM,CAACqC,aAAa,CAAC,CAACJ,MAAM,CAAC,CAAC;IACvE,IAAII,aAAa,GAAGf,cAAc,EAAEA,cAAc,IAAI,CAAC;IACvDA,cAAc,GAAGgB,IAAI,CAACC,GAAG,CAACjB,cAAc,EAAE,CAAC,CAAC;EAC9C;EACArB,MAAM,CAACc,YAAY,CAAC,CAAC;EACrB,IAAIb,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACe,UAAU,CAAC,CAAC;EACrB;EACA,IAAI,CAACd,MAAM,CAACe,QAAQ,IAAIhB,MAAM,CAACiB,SAAS,EAAE;IACxCjB,MAAM,CAACkB,MAAM,CAAC,CAAC;EACjB;EACA,IAAIjB,MAAM,CAACE,IAAI,EAAE;IACfH,MAAM,CAACwB,OAAO,CAACH,cAAc,GAAGrB,MAAM,CAAC4B,YAAY,EAAE,CAAC,EAAE,KAAK,CAAC;EAChE,CAAC,MAAM;IACL5B,MAAM,CAACwB,OAAO,CAACH,cAAc,EAAE,CAAC,EAAE,KAAK,CAAC;EAC1C;AACF;AAEA,SAASkB,eAAeA,CAAA,EAAG;EACzB,MAAMvC,MAAM,GAAG,IAAI;EACnB,MAAMmC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACD,MAAM,CAACc,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAChDuB,aAAa,CAACK,IAAI,CAAC5B,CAAC,CAAC;EACvB;EACAZ,MAAM,CAACkC,WAAW,CAACC,aAAa,CAAC;AACnC;AAEA,SAASM,YAAYA,CAACC,IAAI,EAAE;EAC1B,IAAI;IACF1C;EACF,CAAC,GAAG0C,IAAI;EACRC,MAAM,CAACC,MAAM,CAAC5C,MAAM,EAAE;IACpBF,WAAW,EAAEA,WAAW,CAAC+C,IAAI,CAAC7C,MAAM,CAAC;IACrCmB,YAAY,EAAEA,YAAY,CAAC0B,IAAI,CAAC7C,MAAM,CAAC;IACvCyB,QAAQ,EAAEA,QAAQ,CAACoB,IAAI,CAAC7C,MAAM,CAAC;IAC/BkC,WAAW,EAAEA,WAAW,CAACW,IAAI,CAAC7C,MAAM,CAAC;IACrCuC,eAAe,EAAEA,eAAe,CAACM,IAAI,CAAC7C,MAAM;EAC9C,CAAC,CAAC;AACJ;AAEA,SAASyC,YAAY,IAAIK,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}