{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\FullscreenImageViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport './FullscreenImageViewer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FullscreenImageViewer = ({\n  isOpen,\n  imageUrl,\n  imageAlt,\n  onClose\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fullscreen-overlay\",\n    onClick: onClose,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-background\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-content\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fullscreen-close\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fullscreen-image-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageUrl,\n          alt: imageAlt,\n          className: \"fullscreen-image\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fullscreen-instructions\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Press ESC or click outside to close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(FullscreenImageViewer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = FullscreenImageViewer;\nexport default FullscreenImageViewer;\nvar _c;\n$RefreshReg$(_c, \"FullscreenImageViewer\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "FullscreenImageViewer", "isOpen", "imageUrl", "imageAlt", "onClose", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "className", "onClick", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stopPropagation", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/FullscreenImageViewer.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './FullscreenImageViewer.css';\n\nconst FullscreenImageViewer = ({ isOpen, imageUrl, imageAlt, onClose }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fullscreen-overlay\" onClick={onClose}>\n      <div className=\"fullscreen-background\"></div>\n      <div className=\"fullscreen-content\" onClick={(e) => e.stopPropagation()}>\n        <button className=\"fullscreen-close\" onClick={onClose}>\n          <span>×</span>\n        </button>\n        <div className=\"fullscreen-image-container\">\n          <img\n            src={imageUrl}\n            alt={imageAlt}\n            className=\"fullscreen-image\"\n          />\n        </div>\n        <div className=\"fullscreen-instructions\">\n          <span>Press ESC or click outside to close</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FullscreenImageViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACzER,SAAS,CAAC,MAAM;IACd,MAAMS,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAIH,MAAM,EAAE;MACVQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACZ,MAAM,EAAEG,OAAO,CAAC,CAAC;EAErB,IAAI,CAACH,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKgB,SAAS,EAAC,oBAAoB;IAACC,OAAO,EAAEZ,OAAQ;IAAAa,QAAA,gBACnDlB,OAAA;MAAKgB,SAAS,EAAC;IAAuB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC7CtB,OAAA;MAAKgB,SAAS,EAAC,oBAAoB;MAACC,OAAO,EAAGT,CAAC,IAAKA,CAAC,CAACe,eAAe,CAAC,CAAE;MAAAL,QAAA,gBACtElB,OAAA;QAAQgB,SAAS,EAAC,kBAAkB;QAACC,OAAO,EAAEZ,OAAQ;QAAAa,QAAA,eACpDlB,OAAA;UAAAkB,QAAA,EAAM;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACTtB,OAAA;QAAKgB,SAAS,EAAC,4BAA4B;QAAAE,QAAA,eACzClB,OAAA;UACEwB,GAAG,EAAErB,QAAS;UACdsB,GAAG,EAAErB,QAAS;UACdY,SAAS,EAAC;QAAkB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNtB,OAAA;QAAKgB,SAAS,EAAC,yBAAyB;QAAAE,QAAA,eACtClB,OAAA;UAAAkB,QAAA,EAAM;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAzCIL,qBAAqB;AAAAyB,EAAA,GAArBzB,qBAAqB;AA2C3B,eAAeA,qBAAqB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}