{"ast": null, "code": "import React,{useEffect}from'react';import'./FullscreenImageViewer.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FullscreenImageViewer=_ref=>{let{isOpen,imageUrl,imageAlt,onClose}=_ref;useEffect(()=>{const handleEscape=e=>{if(e.key==='Escape'){onClose();}};if(isOpen){document.addEventListener('keydown',handleEscape);document.body.style.overflow='hidden';}return()=>{document.removeEventListener('keydown',handleEscape);document.body.style.overflow='unset';};},[isOpen,onClose]);if(!isOpen)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"fullscreen-overlay\",onClick:onClose,children:[/*#__PURE__*/_jsx(\"div\",{className:\"fullscreen-background\",style:{backgroundImage:\"url(\".concat(imageUrl,\")\"),filter:'blur(20px) brightness(0.3)',backgroundSize:'cover',backgroundPosition:'center',backgroundRepeat:'no-repeat'}}),/*#__PURE__*/_jsx(\"div\",{className:\"fullscreen-dark-overlay\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"fullscreen-image-wrapper\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsx(\"button\",{className:\"fullscreen-close\",onClick:onClose,children:/*#__PURE__*/_jsx(\"span\",{children:\"\\xD7\"})}),/*#__PURE__*/_jsx(\"img\",{src:imageUrl,alt:imageAlt,className:\"fullscreen-image\"})]})]});};export default FullscreenImageViewer;", "map": {"version": 3, "names": ["React", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "FullscreenImageViewer", "_ref", "isOpen", "imageUrl", "imageAlt", "onClose", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "className", "onClick", "children", "backgroundImage", "concat", "filter", "backgroundSize", "backgroundPosition", "backgroundRepeat", "stopPropagation", "src", "alt"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/FullscreenImageViewer.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './FullscreenImageViewer.css';\n\nconst FullscreenImageViewer = ({ isOpen, imageUrl, imageAlt, onClose }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fullscreen-overlay\" onClick={onClose}>\n      {/* Blurred Background */}\n      <div\n        className=\"fullscreen-background\"\n        style={{\n          backgroundImage: `url(${imageUrl})`,\n          filter: 'blur(20px) brightness(0.3)',\n          backgroundSize: 'cover',\n          backgroundPosition: 'center',\n          backgroundRepeat: 'no-repeat'\n        }}\n      ></div>\n\n      {/* Dark Overlay */}\n      <div className=\"fullscreen-dark-overlay\"></div>\n\n      {/* Image Content */}\n      <div className=\"fullscreen-image-wrapper\" onClick={(e) => e.stopPropagation()}>\n        <button className=\"fullscreen-close\" onClick={onClose}>\n          <span>×</span>\n        </button>\n        <img\n          src={imageUrl}\n          alt={imageAlt}\n          className=\"fullscreen-image\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default FullscreenImageViewer;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,MAAO,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErC,KAAM,CAAAC,qBAAqB,CAAGC,IAAA,EAA6C,IAA5C,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,CACpEN,SAAS,CAAC,IAAM,CACd,KAAM,CAAAW,YAAY,CAAIC,CAAC,EAAK,CAC1B,GAAIA,CAAC,CAACC,GAAG,GAAK,QAAQ,CAAE,CACtBH,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,GAAIH,MAAM,CAAE,CACVO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEJ,YAAY,CAAC,CAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACzC,CAEA,MAAO,IAAM,CACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,CAAER,YAAY,CAAC,CACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,OAAO,CACxC,CAAC,CACH,CAAC,CAAE,CAACX,MAAM,CAAEG,OAAO,CAAC,CAAC,CAErB,GAAI,CAACH,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEH,KAAA,QAAKgB,SAAS,CAAC,oBAAoB,CAACC,OAAO,CAAEX,OAAQ,CAAAY,QAAA,eAEnDpB,IAAA,QACEkB,SAAS,CAAC,uBAAuB,CACjCH,KAAK,CAAE,CACLM,eAAe,QAAAC,MAAA,CAAShB,QAAQ,KAAG,CACnCiB,MAAM,CAAE,4BAA4B,CACpCC,cAAc,CAAE,OAAO,CACvBC,kBAAkB,CAAE,QAAQ,CAC5BC,gBAAgB,CAAE,WACpB,CAAE,CACE,CAAC,cAGP1B,IAAA,QAAKkB,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAG/ChB,KAAA,QAAKgB,SAAS,CAAC,0BAA0B,CAACC,OAAO,CAAGT,CAAC,EAAKA,CAAC,CAACiB,eAAe,CAAC,CAAE,CAAAP,QAAA,eAC5EpB,IAAA,WAAQkB,SAAS,CAAC,kBAAkB,CAACC,OAAO,CAAEX,OAAQ,CAAAY,QAAA,cACpDpB,IAAA,SAAAoB,QAAA,CAAM,MAAC,CAAM,CAAC,CACR,CAAC,cACTpB,IAAA,QACE4B,GAAG,CAAEtB,QAAS,CACduB,GAAG,CAAEtB,QAAS,CACdW,SAAS,CAAC,kBAAkB,CAC7B,CAAC,EACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAf,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}