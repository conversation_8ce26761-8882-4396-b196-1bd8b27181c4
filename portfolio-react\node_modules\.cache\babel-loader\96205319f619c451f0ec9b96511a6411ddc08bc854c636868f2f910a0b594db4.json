{"ast": null, "code": "import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = \"swiper-slide-shadow\".concat(side ? \"-\".concat(side) : '').concat(suffix ? \" swiper-slide-shadow-\".concat(suffix) : '');\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(\".\".concat(shadowClass.split(' ').join('.')));\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\nexport { createShadow as c };", "map": {"version": 3, "names": ["g", "getSlideTransformEl", "c", "createElement", "createShadow", "suffix", "slideEl", "side", "shadowClass", "concat", "shadow<PERSON><PERSON><PERSON>", "shadowEl", "querySelector", "split", "join", "append"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/shared/create-shadow.mjs"], "sourcesContent": ["import { g as getSlideTransformEl, c as createElement } from './utils.mjs';\n\nfunction createShadow(suffix, slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}${suffix ? ` swiper-slide-shadow-${suffix}` : ''}`;\n  const shadowContainer = getSlideTransformEl(slideEl);\n  let shadowEl = shadowContainer.querySelector(`.${shadowClass.split(' ').join('.')}`);\n  if (!shadowEl) {\n    shadowEl = createElement('div', shadowClass.split(' '));\n    shadowContainer.append(shadowEl);\n  }\n  return shadowEl;\n}\n\nexport { createShadow as c };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,aAAa,QAAQ,aAAa;AAE1E,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE;EAC3C,MAAMC,WAAW,yBAAAC,MAAA,CAAyBF,IAAI,OAAAE,MAAA,CAAOF,IAAI,IAAK,EAAE,EAAAE,MAAA,CAAGJ,MAAM,2BAAAI,MAAA,CAA2BJ,MAAM,IAAK,EAAE,CAAE;EACnH,MAAMK,eAAe,GAAGT,mBAAmB,CAACK,OAAO,CAAC;EACpD,IAAIK,QAAQ,GAAGD,eAAe,CAACE,aAAa,KAAAH,MAAA,CAAKD,WAAW,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAE,CAAC;EACpF,IAAI,CAACH,QAAQ,EAAE;IACbA,QAAQ,GAAGR,aAAa,CAAC,KAAK,EAAEK,WAAW,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC;IACvDH,eAAe,CAACK,MAAM,CAACJ,QAAQ,CAAC;EAClC;EACA,OAAOA,QAAQ;AACjB;AAEA,SAASP,YAAY,IAAIF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}