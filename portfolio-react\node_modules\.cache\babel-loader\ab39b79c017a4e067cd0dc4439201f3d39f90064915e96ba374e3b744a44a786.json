{"ast": null, "code": "export const jobsData = [{\n  id: 1,\n  slug: \"frontend-receeto\",\n  title: \"Frontend Developer Angular\",\n  company: \"Company : Receeto\",\n  companyLink: \"https://receeto.com\",\n  duration: \"2/2025 - 6/2025\",\n  logo: \"/Receeto_logo.jpg\",\n  logoAlt: \"Receeto Logo\",\n  summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n  roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n  responsibilities: [\"Develop responsive web applications using Angular and modern frontend technologies\", \"Implement financial data visualizations and interactive charts\", \"Optimize application performance through lazy loading and build optimization\", \"Create expense tracking and budgeting tools with real-time data processing\", \"Build responsive interfaces for both mobile and desktop platforms\", \"Implement Angular reactive state management using signals\"],\n  skills: {\n    \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n    \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n    \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n  },\n  accomplishments: [{\n    metric: \"40%\",\n    description: \"Improved application performance through lazy loading and build optimization\"\n  }, {\n    metric: \"100%\",\n    description: \"Responsive design compatibility across mobile and desktop platforms\"\n  }, {\n    metric: \"NDA\",\n    description: \"Confidential project delivered successfully while maintaining client privacy\"\n  }],\n  projects: [{\n    title: \"Img 1\",\n    description: \"NDA - details confidential\",\n    images: [\"../NDA.jpg\", \"../NDA.jpg\", \"../NDA.jpg\"],\n    technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"],\n    liveUrl: \"https://receeto.com\"\n  }, {\n    title: \"Img 2\",\n    description: \"NDA - details confidential\",\n    images: [\"../NDA.jpg\", \"../NDA.jpg\"],\n    technologies: [\"Angular\", \"RxJS\", \"Angular Material\"],\n    liveUrl: \"https://receeto.com\"\n  }]\n}, {\n  id: 2,\n  slug: \"3d-ecommerce-platform\",\n  title: \"UI/UX Designer & Frontend Developer\",\n  company: \"DigitalStudio Creative\",\n  companyLink: \"https://threed-e-commerce.onrender.com\",\n  duration: \"2022 - 2023\",\n  logo: \"/3D E Logo.png\",\n  logoAlt: \"DigitalStudio Creative Logo\",\n  summary: \"Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.\",\n  roleOverview: \"As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\\n\\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\\n\\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.\",\n  responsibilities: [\"Design user interfaces and experiences for the 3D e-commerce platform\", \"Develop responsive frontend using React.js and modern web technologies\", \"Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions\", \"Create comprehensive design systems for consistent user experience across devices\", \"Conduct usability testing to refine the shopping experience and 3D interactions\", \"Optimize application performance across various devices and network conditions\", \"Collaborate with clients to understand business requirements and technical constraints\", \"Develop fallback mechanisms and error boundaries for graceful failure handling\", \"Implement progressive enhancement and mobile-first responsive design principles\"],\n  skills: {\n    \"Frontend\": [\"React.js\", \"Three.js/WebGL\", \"JavaScript\", \"CSS3\", \"React Router\", \"Axios\"],\n    \"3D Technologies\": [\"Model Viewer\", \"GLTF/GLB\", \"OrbitControls\", \"Real-time Rendering\"],\n    \"Backend\": [\"Node.js\", \"Express.js\", \"MongoDB\", \"RESTful APIs\"],\n    \"Design & UX\": [\"Figma\", \"Responsive Design\", \"Mobile-First Design\", \"User Testing\", \"Performance Optimization\"],\n    \"Tools & Deployment\": [\"Git\", \"Render\", \"Webpack\", \"Font Awesome\", \"Chrome DevTools\"]\n  },\n  accomplishments: [{\n    metric: \"40%\",\n    description: \"Increased user engagement through improved UX design and 3D interactions\"\n  }, {\n    metric: \"95%\",\n    description: \"Client satisfaction rate based on project feedback\"\n  }],\n  projects: [{\n    title: \"3D Product Visualization Engine\",\n    description: \"Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.\",\n    images: [\"../3D E Commerce Home.PNG\", \"../3D E Commerce Product.PNG\"],\n    technologies: [\"Three.js\", \"WebGL\", \"GLTF/GLB\", \"OrbitControls\"],\n    liveUrl: \"https://threed-e-commerce-front.onrender.com\"\n  }, {\n    title: \"Mobile-Responsive Shopping Interface\",\n    description: \"Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.\",\n    images: [\"../All product pc.PNG\", \"../3D E Commerce Mobile.PNG\", \"../3D E Commerce Cart.PNG\"],\n    technologies: [\"React.js\", \"CSS3\", \"Responsive Design\", \"Mobile UX\"],\n    liveUrl: \"https://threed-e-commerce-front.onrender.com\"\n  }, {\n    title: \"Performance-Optimized Architecture\",\n    description: \"Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.\",\n    images: [\"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Architecture+System\", \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=API+Design\", \"https://via.placeholder.com/400x250/48D1CC/FFFFFF?text=Database+Schema\"],\n    technologies: [\"Node.js\", \"Express.js\", \"MongoDB\", \"Render\", \"API Design\"],\n    liveUrl: \"https://threed-e-commerce-backend.onrender.com\"\n  }]\n}, {\n  id: 3,\n  slug: \"junior-web-developer\",\n  title: \"Junior Web Developer\",\n  company: \"WebDev Agency\",\n  duration: \"2021 - 2022\",\n  logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n  logoAlt: \"WebDev Agency Logo\",\n  summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n  roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n  responsibilities: [\"Develop custom WordPress themes and plugins\", \"Build responsive websites using HTML, CSS, and JavaScript\", \"Create e-commerce solutions using WooCommerce and Shopify\", \"Collaborate with designers to implement pixel-perfect designs\", \"Optimize websites for performance and SEO\", \"Provide technical support and maintenance for client websites\"],\n  skills: {\n    \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n    \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n    \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n  },\n  accomplishments: [{\n    metric: \"30+\",\n    description: \"Websites successfully developed and launched\"\n  }, {\n    metric: \"50%\",\n    description: \"Improvement in page load speeds through optimization\"\n  }, {\n    metric: \"100%\",\n    description: \"Client satisfaction rate for delivered projects\"\n  }],\n  projects: [{\n    title: \"Restaurant Chain Website\",\n    description: \"Built a multi-location restaurant website with online ordering system\",\n    images: [\"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\", \"https://via.placeholder.com/400x250/008B8B/FFFFFF?text=Menu+System\", \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=Order+Management\"],\n    technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"],\n    liveUrl: \"https://example-restaurant.com\"\n  }, {\n    title: \"Real Estate Portal\",\n    description: \"Developed property listing website with advanced search functionality\",\n    images: [\"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\", \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Property+Search\", \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Listing+Details\"],\n    technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"],\n    liveUrl: \"https://example-realestate.com\"\n  }]\n}, {\n  id: 4,\n  slug: \"freelance-designer\",\n  title: \"Freelance Designer\",\n  company: \"Self-Employed\",\n  duration: \"2020 - 2021\",\n  logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n  logoAlt: \"Freelance Logo\",\n  summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n  roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n  responsibilities: [\"Design logos and brand identities for small businesses\", \"Create marketing materials including flyers, brochures, and business cards\", \"Develop social media graphics and digital marketing assets\", \"Collaborate directly with business owners to understand their vision\", \"Manage multiple projects simultaneously while meeting deadlines\", \"Handle client communications and project billing\"],\n  skills: {\n    \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n    \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n    \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n  },\n  accomplishments: [{\n    metric: \"20+\",\n    description: \"Local businesses served with design solutions\"\n  }, {\n    metric: \"4.9/5\",\n    description: \"Average client rating on freelance platforms\"\n  }, {\n    metric: \"90%\",\n    description: \"Client retention rate for ongoing projects\"\n  }],\n  projects: [{\n    title: \"Local Coffee Shop Branding\",\n    description: \"Complete brand identity including logo, menu design, and signage\",\n    images: [\"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\", \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Logo+Design\", \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Menu+Design\"],\n    technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"],\n    liveUrl: \"https://example-coffeeshop.com\"\n  }, {\n    title: \"Fitness Studio Marketing Kit\",\n    description: \"Comprehensive marketing materials for new fitness studio launch\",\n    images: [\"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\", \"https://via.placeholder.com/400x250/DC143C/FFFFFF?text=Brochure+Design\", \"https://via.placeholder.com/400x250/FF4500/FFFFFF?text=Social+Media+Kit\"],\n    technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"],\n    liveUrl: \"https://example-fitness.com\"\n  }]\n}];", "map": {"version": 3, "names": ["jobsData", "id", "slug", "title", "company", "companyLink", "duration", "logo", "logoAlt", "summary", "roleOverview", "responsibilities", "skills", "accomplishments", "metric", "description", "projects", "images", "technologies", "liveUrl"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/data/jobsData.js"], "sourcesContent": ["export const jobsData = [\n  {\n    id: 1,\n    slug: \"frontend-receeto\",\n    title: \"Frontend Developer Angular\",\n    company: \"Company : Receeto\",\n    companyLink: \"https://receeto.com\",\n    duration: \"2/2025 - 6/2025\",\n    logo: \"/Receeto_logo.jpg\",\n    logoAlt: \"Receeto Logo\",\n    summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n    roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n    responsibilities: [\n      \"Develop responsive web applications using Angular and modern frontend technologies\",\n      \"Implement financial data visualizations and interactive charts\",\n      \"Optimize application performance through lazy loading and build optimization\",\n      \"Create expense tracking and budgeting tools with real-time data processing\",\n      \"Build responsive interfaces for both mobile and desktop platforms\",\n      \"Implement Angular reactive state management using signals\"\n    ],\n    skills: {\n      \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n      \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n      \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Improved application performance through lazy loading and build optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Responsive design compatibility across mobile and desktop platforms\"\n      },\n      {\n        metric: \"NDA\",\n        description: \"Confidential project delivered successfully while maintaining client privacy\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Img 1\",\n        description: \"NDA - details confidential\",\n        images: [\"../NDA.jpg\", \"../NDA.jpg\", \"../NDA.jpg\"],\n        technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"],\n        liveUrl: \"https://receeto.com\"\n      },\n      {\n        title: \"Img 2\",\n        description: \"NDA - details confidential\",\n        images: [\"../NDA.jpg\", \"../NDA.jpg\"],\n        technologies: [\"Angular\", \"RxJS\", \"Angular Material\"],\n        liveUrl: \"https://receeto.com\"\n      }\n    ]\n  },\n  {\n    id: 2,\n    slug: \"3d-ecommerce-platform\",\n    title: \"UI/UX Designer & Frontend Developer\",\n    company: \"DigitalStudio Creative\",\n    companyLink: \"https://threed-e-commerce.onrender.com\",\n    duration: \"2022 - 2023\",\n    logo: \"/3D E Logo.png\",\n    logoAlt: \"DigitalStudio Creative Logo\",\n    summary: \"Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.\",\n    roleOverview: \"As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\\n\\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\\n\\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.\",\n    responsibilities: [\n      \"Design user interfaces and experiences for the 3D e-commerce platform\",\n      \"Develop responsive frontend using React.js and modern web technologies\",\n      \"Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions\",\n      \"Create comprehensive design systems for consistent user experience across devices\",\n      \"Conduct usability testing to refine the shopping experience and 3D interactions\",\n      \"Optimize application performance across various devices and network conditions\",\n      \"Collaborate with clients to understand business requirements and technical constraints\",\n      \"Develop fallback mechanisms and error boundaries for graceful failure handling\",\n      \"Implement progressive enhancement and mobile-first responsive design principles\"\n    ],\n    skills: {\n      \"Frontend\": [\"React.js\", \"Three.js/WebGL\", \"JavaScript\", \"CSS3\", \"React Router\", \"Axios\"],\n      \"3D Technologies\": [\"Model Viewer\", \"GLTF/GLB\", \"OrbitControls\", \"Real-time Rendering\"],\n      \"Backend\": [\"Node.js\", \"Express.js\", \"MongoDB\", \"RESTful APIs\"],\n      \"Design & UX\": [\"Figma\", \"Responsive Design\", \"Mobile-First Design\", \"User Testing\", \"Performance Optimization\"],\n      \"Tools & Deployment\": [\"Git\", \"Render\", \"Webpack\", \"Font Awesome\", \"Chrome DevTools\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Increased user engagement through improved UX design and 3D interactions\"\n      },\n      {\n        metric: \"95%\",\n        description: \"Client satisfaction rate based on project feedback\"\n      }\n    ],\n    projects: [\n      {\n        title: \"3D Product Visualization Engine\",\n        description: \"Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.\",\n        images: [\n          \"../3D E Commerce Home.PNG\",\n          \"../3D E Commerce Product.PNG\",\n        ],\n        technologies: [\"Three.js\", \"WebGL\", \"GLTF/GLB\", \"OrbitControls\"],\n        liveUrl: \"https://threed-e-commerce-front.onrender.com\"\n      },\n      {\n        title: \"Mobile-Responsive Shopping Interface\",\n        description: \"Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.\",\n        images: [\n          \"../All product pc.PNG\",\n          \"../3D E Commerce Mobile.PNG\",\n          \"../3D E Commerce Cart.PNG\"\n        ],\n        technologies: [\"React.js\", \"CSS3\", \"Responsive Design\", \"Mobile UX\"],\n        liveUrl: \"https://threed-e-commerce-front.onrender.com\"\n      },\n      {\n        title: \"Performance-Optimized Architecture\",\n        description: \"Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.\",\n        images: [\n          \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Architecture+System\",\n          \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=API+Design\",\n          \"https://via.placeholder.com/400x250/48D1CC/FFFFFF?text=Database+Schema\"\n        ],\n        technologies: [\"Node.js\", \"Express.js\", \"MongoDB\", \"Render\", \"API Design\"],\n        liveUrl: \"https://threed-e-commerce-backend.onrender.com\"\n      }\n    ]\n  },\n  {\n    id: 3,\n    slug: \"junior-web-developer\",\n    title: \"Junior Web Developer\",\n    company: \"WebDev Agency\",\n    duration: \"2021 - 2022\",\n    logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n    logoAlt: \"WebDev Agency Logo\",\n    summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n    roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n    responsibilities: [\n      \"Develop custom WordPress themes and plugins\",\n      \"Build responsive websites using HTML, CSS, and JavaScript\",\n      \"Create e-commerce solutions using WooCommerce and Shopify\",\n      \"Collaborate with designers to implement pixel-perfect designs\",\n      \"Optimize websites for performance and SEO\",\n      \"Provide technical support and maintenance for client websites\"\n    ],\n    skills: {\n      \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n      \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n      \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n    },\n    accomplishments: [\n      {\n        metric: \"30+\",\n        description: \"Websites successfully developed and launched\"\n      },\n      {\n        metric: \"50%\",\n        description: \"Improvement in page load speeds through optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Client satisfaction rate for delivered projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Restaurant Chain Website\",\n        description: \"Built a multi-location restaurant website with online ordering system\",\n        images: [\n          \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n          \"https://via.placeholder.com/400x250/008B8B/FFFFFF?text=Menu+System\",\n          \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=Order+Management\"\n        ],\n        technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"],\n        liveUrl: \"https://example-restaurant.com\"\n      },\n      {\n        title: \"Real Estate Portal\",\n        description: \"Developed property listing website with advanced search functionality\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Property+Search\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Listing+Details\"\n        ],\n        technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"],\n        liveUrl: \"https://example-realestate.com\"\n      }\n    ]\n  },\n  {\n    id: 4,\n    slug: \"freelance-designer\",\n    title: \"Freelance Designer\",\n    company: \"Self-Employed\",\n    duration: \"2020 - 2021\",\n    logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n    logoAlt: \"Freelance Logo\",\n    summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n    roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n    responsibilities: [\n      \"Design logos and brand identities for small businesses\",\n      \"Create marketing materials including flyers, brochures, and business cards\",\n      \"Develop social media graphics and digital marketing assets\",\n      \"Collaborate directly with business owners to understand their vision\",\n      \"Manage multiple projects simultaneously while meeting deadlines\",\n      \"Handle client communications and project billing\"\n    ],\n    skills: {\n      \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n      \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n      \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"20+\",\n        description: \"Local businesses served with design solutions\"\n      },\n      {\n        metric: \"4.9/5\",\n        description: \"Average client rating on freelance platforms\"\n      },\n      {\n        metric: \"90%\",\n        description: \"Client retention rate for ongoing projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Local Coffee Shop Branding\",\n        description: \"Complete brand identity including logo, menu design, and signage\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Logo+Design\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Menu+Design\"\n        ],\n        technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"],\n        liveUrl: \"https://example-coffeeshop.com\"\n      },\n      {\n        title: \"Fitness Studio Marketing Kit\",\n        description: \"Comprehensive marketing materials for new fitness studio launch\",\n        images: [\n          \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n          \"https://via.placeholder.com/400x250/DC143C/FFFFFF?text=Brochure+Design\",\n          \"https://via.placeholder.com/400x250/FF4500/FFFFFF?text=Social+Media+Kit\"\n        ],\n        technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"],\n        liveUrl: \"https://example-fitness.com\"\n      }\n    ]\n  }\n];\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE,4BAA4B;EACnCC,OAAO,EAAE,mBAAmB;EAC5BC,WAAW,EAAE,qBAAqB;EAClCC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,mBAAmB;EACzBC,OAAO,EAAE,cAAc;EACvBC,OAAO,EAAE,gJAAgJ;EACzJC,YAAY,EAAE,4jCAA4jC;EAC1kCC,gBAAgB,EAAE,CAChB,oFAAoF,EACpF,gEAAgE,EAChE,8EAA8E,EAC9E,4EAA4E,EAC5E,mEAAmE,EACnE,2DAA2D,CAC5D;EACDC,MAAM,EAAE;IACN,UAAU,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAE,aAAa,CAAC;IAC/E,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,WAAW,CAAC;IACtF,iBAAiB,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,uCAAuC,EAAE,OAAO;EACvG,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEb,KAAK,EAAE,OAAO;IACdY,WAAW,EAAE,4BAA4B;IACzCE,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;IAClDC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC;IACpDC,OAAO,EAAE;EACX,CAAC,EACD;IACEhB,KAAK,EAAE,OAAO;IACdY,WAAW,EAAE,4BAA4B;IACzCE,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;IACpCC,YAAY,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB,CAAC;IACrDC,OAAO,EAAE;EACX,CAAC;AAEL,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,uBAAuB;EAC7BC,KAAK,EAAE,qCAAqC;EAC5CC,OAAO,EAAE,wBAAwB;EACjCC,WAAW,EAAE,wCAAwC;EACrDC,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,OAAO,EAAE,6BAA6B;EACtCC,OAAO,EAAE,gPAAgP;EACzPC,YAAY,EAAE,g8BAAg8B;EAC98BC,gBAAgB,EAAE,CAChB,uEAAuE,EACvE,wEAAwE,EACxE,wFAAwF,EACxF,mFAAmF,EACnF,iFAAiF,EACjF,gFAAgF,EAChF,wFAAwF,EACxF,gFAAgF,EAChF,iFAAiF,CAClF;EACDC,MAAM,EAAE;IACN,UAAU,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC;IACzF,iBAAiB,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,qBAAqB,CAAC;IACvF,SAAS,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,CAAC;IAC/D,aAAa,EAAE,CAAC,OAAO,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,cAAc,EAAE,0BAA0B,CAAC;IAChH,oBAAoB,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB;EACtF,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEb,KAAK,EAAE,iCAAiC;IACxCY,WAAW,EAAE,2KAA2K;IACxLE,MAAM,EAAE,CACN,2BAA2B,EAC3B,8BAA8B,CAC/B;IACDC,YAAY,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;IAChEC,OAAO,EAAE;EACX,CAAC,EACD;IACEhB,KAAK,EAAE,sCAAsC;IAC7CY,WAAW,EAAE,qKAAqK;IAClLE,MAAM,EAAE,CACN,uBAAuB,EACvB,6BAA6B,EAC7B,2BAA2B,CAC5B;IACDC,YAAY,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,mBAAmB,EAAE,WAAW,CAAC;IACpEC,OAAO,EAAE;EACX,CAAC,EACD;IACEhB,KAAK,EAAE,oCAAoC;IAC3CY,WAAW,EAAE,uJAAuJ;IACpKE,MAAM,EAAE,CACN,4EAA4E,EAC5E,mEAAmE,EACnE,wEAAwE,CACzE;IACDC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC;IAC1EC,OAAO,EAAE;EACX,CAAC;AAEL,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,sBAAsB;EAC5BC,KAAK,EAAE,sBAAsB;EAC7BC,OAAO,EAAE,eAAe;EACxBE,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,2DAA2D;EACjEC,OAAO,EAAE,oBAAoB;EAC7BC,OAAO,EAAE,gNAAgN;EACzNC,YAAY,EAAE,2PAA2P;EACzQC,gBAAgB,EAAE,CAChB,6CAA6C,EAC7C,2DAA2D,EAC3D,2DAA2D,EAC3D,+DAA+D,EAC/D,2CAA2C,EAC3C,+DAA+D,CAChE;EACDC,MAAM,EAAE;IACN,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC;IAClE,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC;IACvD,OAAO,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,KAAK,EAAE,QAAQ;EAClE,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEb,KAAK,EAAE,0BAA0B;IACjCY,WAAW,EAAE,uEAAuE;IACpFE,MAAM,EAAE,CACN,2EAA2E,EAC3E,oEAAoE,EACpE,yEAAyE,CAC1E;IACDC,YAAY,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC;IACjDC,OAAO,EAAE;EACX,CAAC,EACD;IACEhB,KAAK,EAAE,oBAAoB;IAC3BY,WAAW,EAAE,uEAAuE;IACpFE,MAAM,EAAE,CACN,2EAA2E,EAC3E,wEAAwE,EACxE,wEAAwE,CACzE;IACDC,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC;IAClDC,OAAO,EAAE;EACX,CAAC;AAEL,CAAC,EACD;EACElB,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,oBAAoB;EAC3BC,OAAO,EAAE,eAAe;EACxBE,QAAQ,EAAE,aAAa;EACvBC,IAAI,EAAE,2DAA2D;EACjEC,OAAO,EAAE,gBAAgB;EACzBC,OAAO,EAAE,iNAAiN;EAC1NC,YAAY,EAAE,yRAAyR;EACvSC,gBAAgB,EAAE,CAChB,wDAAwD,EACxD,4EAA4E,EAC5E,4DAA4D,EAC5D,sEAAsE,EACtE,iEAAiE,EACjE,kDAAkD,CACnD;EACDC,MAAM,EAAE;IACN,iBAAiB,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,OAAO,CAAC;IACtF,eAAe,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,CAAC;IACtF,iBAAiB,EAAE,CAAC,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,SAAS;EAChG,CAAC;EACDC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,OAAO;IACfC,WAAW,EAAE;EACf,CAAC,EACD;IACED,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE;EACf,CAAC,CACF;EACDC,QAAQ,EAAE,CACR;IACEb,KAAK,EAAE,4BAA4B;IACnCY,WAAW,EAAE,kEAAkE;IAC/EE,MAAM,EAAE,CACN,0EAA0E,EAC1E,oEAAoE,EACpE,oEAAoE,CACrE;IACDC,YAAY,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC;IACtDC,OAAO,EAAE;EACX,CAAC,EACD;IACEhB,KAAK,EAAE,8BAA8B;IACrCY,WAAW,EAAE,iEAAiE;IAC9EE,MAAM,EAAE,CACN,0EAA0E,EAC1E,wEAAwE,EACxE,yEAAyE,CAC1E;IACDC,YAAY,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,cAAc,CAAC;IAC1DC,OAAO,EAAE;EACX,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}