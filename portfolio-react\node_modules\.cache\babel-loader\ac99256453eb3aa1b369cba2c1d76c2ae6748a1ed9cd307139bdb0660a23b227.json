{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\FullscreenImageViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport './FullscreenImageViewer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FullscreenImageViewer = ({\n  isOpen,\n  imageUrl,\n  imageAlt,\n  onClose\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fullscreen-overlay\",\n    onClick: onClose,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-background\",\n      style: {\n        backgroundImage: `url(${imageUrl})`,\n        filter: 'blur(20px) brightness(0.3)',\n        backgroundSize: 'cover',\n        backgroundPosition: 'center',\n        backgroundRepeat: 'no-repeat'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-dark-overlay\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fullscreen-image-wrapper\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"fullscreen-close\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imageUrl,\n        alt: imageAlt,\n        className: \"fullscreen-image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(FullscreenImageViewer, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = FullscreenImageViewer;\nexport default FullscreenImageViewer;\nvar _c;\n$RefreshReg$(_c, \"FullscreenImageViewer\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "FullscreenImageViewer", "isOpen", "imageUrl", "imageAlt", "onClose", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "className", "onClick", "children", "backgroundImage", "filter", "backgroundSize", "backgroundPosition", "backgroundRepeat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "stopPropagation", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/FullscreenImageViewer.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './FullscreenImageViewer.css';\n\nconst FullscreenImageViewer = ({ isOpen, imageUrl, imageAlt, onClose }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fullscreen-overlay\" onClick={onClose}>\n      {/* Blurred Background */}\n      <div\n        className=\"fullscreen-background\"\n        style={{\n          backgroundImage: `url(${imageUrl})`,\n          filter: 'blur(20px) brightness(0.3)',\n          backgroundSize: 'cover',\n          backgroundPosition: 'center',\n          backgroundRepeat: 'no-repeat'\n        }}\n      ></div>\n\n      {/* Dark Overlay */}\n      <div className=\"fullscreen-dark-overlay\"></div>\n\n      {/* Image Content */}\n      <div className=\"fullscreen-image-wrapper\" onClick={(e) => e.stopPropagation()}>\n        <button className=\"fullscreen-close\" onClick={onClose}>\n          <span>×</span>\n        </button>\n        <img\n          src={imageUrl}\n          alt={imageAlt}\n          className=\"fullscreen-image\"\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default FullscreenImageViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACzER,SAAS,CAAC,MAAM;IACd,MAAMS,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBJ,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAIH,MAAM,EAAE;MACVQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACZ,MAAM,EAAEG,OAAO,CAAC,CAAC;EAErB,IAAI,CAACH,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKgB,SAAS,EAAC,oBAAoB;IAACC,OAAO,EAAEZ,OAAQ;IAAAa,QAAA,gBAEnDlB,OAAA;MACEgB,SAAS,EAAC,uBAAuB;MACjCH,KAAK,EAAE;QACLM,eAAe,EAAE,OAAOhB,QAAQ,GAAG;QACnCiB,MAAM,EAAE,4BAA4B;QACpCC,cAAc,EAAE,OAAO;QACvBC,kBAAkB,EAAE,QAAQ;QAC5BC,gBAAgB,EAAE;MACpB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3B,OAAA;MAAKgB,SAAS,EAAC;IAAyB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAG/C3B,OAAA;MAAKgB,SAAS,EAAC,0BAA0B;MAACC,OAAO,EAAGT,CAAC,IAAKA,CAAC,CAACoB,eAAe,CAAC,CAAE;MAAAV,QAAA,gBAC5ElB,OAAA;QAAQgB,SAAS,EAAC,kBAAkB;QAACC,OAAO,EAAEZ,OAAQ;QAAAa,QAAA,eACpDlB,OAAA;UAAAkB,QAAA,EAAM;QAAC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACT3B,OAAA;QACE6B,GAAG,EAAE1B,QAAS;QACd2B,GAAG,EAAE1B,QAAS;QACdY,SAAS,EAAC;MAAkB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAnDIL,qBAAqB;AAAA8B,EAAA,GAArB9B,qBAAqB;AAqD3B,eAAeA,qBAAqB;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}