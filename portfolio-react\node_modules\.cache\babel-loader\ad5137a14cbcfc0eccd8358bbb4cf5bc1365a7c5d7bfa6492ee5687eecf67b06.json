{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\Home.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"home\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Med Amine Chouchane\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Frontend Developer & UI/UX Designer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Passionate about creating beautiful, functional, and user-friendly web applications using modern technologies like React, Angular, and TypeScript.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/experience\",\n          className: \"btn btn-primary\",\n          children: \"View Experience\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#contact\",\n          className: \"btn btn-secondary\",\n          children: \"Get In Touch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skills-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Technologies I Work With\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tech-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"React\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"Angular\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"TypeScript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"JavaScript\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"HTML5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"CSS3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"Node.js\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tech-item\",\n          children: \"MongoDB\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Home", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/Home.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Home = () => {\n  return (\n    <section className=\"home\">\n      <div className=\"hero\">\n        <h1>Med Amine Chouchane</h1>\n        <h2>Frontend Developer & UI/UX Designer</h2>\n        <p>\n          Passionate about creating beautiful, functional, and user-friendly web applications\n          using modern technologies like React, Angular, and TypeScript.\n        </p>\n        <div className=\"cta-buttons\">\n          <Link to=\"/experience\" className=\"btn btn-primary\">\n            View Experience\n          </Link>\n          <a href=\"#contact\" className=\"btn btn-secondary\">\n            Get In Touch\n          </a>\n        </div>\n      </div>\n      \n      <div className=\"skills-preview\">\n        <h3>Technologies I Work With</h3>\n        <div className=\"tech-grid\">\n          <span className=\"tech-item\">React</span>\n          <span className=\"tech-item\">Angular</span>\n          <span className=\"tech-item\">TypeScript</span>\n          <span className=\"tech-item\">JavaScript</span>\n          <span className=\"tech-item\">HTML5</span>\n          <span className=\"tech-item\">CSS3</span>\n          <span className=\"tech-item\">Node.js</span>\n          <span className=\"tech-item\">MongoDB</span>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACvBH,OAAA;MAAKE,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBH,OAAA;QAAAG,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BP,OAAA;QAAAG,QAAA,EAAI;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5CP,OAAA;QAAAG,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,aAAa;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPP,OAAA;UAAGS,IAAI,EAAC,UAAU;UAACP,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BH,OAAA;QAAAG,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCP,OAAA;QAAKE,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBH,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7CP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7CP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxCP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1CP,OAAA;UAAME,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACG,EAAA,GAnCIT,IAAI;AAqCV,eAAeA,IAAI;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}