{"ast": null, "code": "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { m as makeElementsArray, j as classesToTokens, c as createElement, n as nextTick, d as elementOffset } from '../shared/utils.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nfunction Scrollbar(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault && e.cancelable) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n      if (!el.length) return;\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(classesToSelector(swiper.params.scrollbar.dragClass));\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(...classesToTokens(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass));\n    }\n    disableDraggable();\n  }\n  on('changeDirection', () => {\n    if (!swiper.scrollbar || !swiper.scrollbar.el) return;\n    const params = swiper.params.scrollbar;\n    let {\n      el\n    } = swiper.scrollbar;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock changeDirection', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}\nexport { Scrollbar as default };", "map": {"version": 3, "names": ["g", "getDocument", "m", "makeElementsArray", "j", "classesToTokens", "c", "createElement", "n", "nextTick", "d", "elementOffset", "createElementIfNotDefined", "classesToSelector", "Sc<PERSON><PERSON>", "_ref", "swiper", "extendParams", "on", "emit", "document", "isTouched", "timeout", "dragTimeout", "dragStartPos", "dragSize", "trackSize", "divider", "scrollbar", "el", "hide", "draggable", "snapOnRelease", "lockClass", "dragClass", "scrollbarDisabledClass", "horizontalClass", "verticalClass", "dragEl", "setTranslate", "params", "rtlTranslate", "rtl", "progress", "loop", "progressLoop", "newSize", "newPos", "isHorizontal", "style", "transform", "width", "height", "clearTimeout", "opacity", "setTimeout", "transitionDuration", "setTransition", "duration", "updateSize", "offsetWidth", "offsetHeight", "size", "virtualSize", "slidesOffsetBefore", "centeredSlides", "snapGrid", "parseInt", "display", "watchOverflow", "enabled", "classList", "isLocked", "getPointerPosition", "e", "clientX", "clientY", "setDragPosition", "positionRatio", "Math", "max", "min", "position", "minTranslate", "maxTranslate", "updateProgress", "updateActiveIndex", "updateSlidesClasses", "onDragStart", "wrapperEl", "target", "getBoundingClientRect", "preventDefault", "stopPropagation", "cssMode", "onDragMove", "cancelable", "returnValue", "onDragEnd", "slideToClosest", "events", "method", "activeListener", "passiveListeners", "passive", "capture", "passiveListener", "eventMethod", "enableDraggable", "disableDraggable", "init", "swiperEl", "originalParams", "isElement", "querySelector", "querySelectorAll", "length", "uniqueNavElements", "add", "append", "Object", "assign", "destroy", "remove", "for<PERSON>ach", "subEl", "disable", "_s", "enable", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/scrollbar.mjs"], "sourcesContent": ["import { g as getDocument } from '../shared/ssr-window.esm.mjs';\nimport { m as makeElementsArray, j as classesToTokens, c as createElement, n as nextTick, d as elementOffset } from '../shared/utils.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { c as classesToSelector } from '../shared/classes-to-selector.mjs';\n\nfunction Scrollbar(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const document = getDocument();\n  let isTouched = false;\n  let timeout = null;\n  let dragTimeout = null;\n  let dragStartPos;\n  let dragSize;\n  let trackSize;\n  let divider;\n  extendParams({\n    scrollbar: {\n      el: null,\n      dragSize: 'auto',\n      hide: false,\n      draggable: false,\n      snapOnRelease: true,\n      lockClass: 'swiper-scrollbar-lock',\n      dragClass: 'swiper-scrollbar-drag',\n      scrollbarDisabledClass: 'swiper-scrollbar-disabled',\n      horizontalClass: `swiper-scrollbar-horizontal`,\n      verticalClass: `swiper-scrollbar-vertical`\n    }\n  });\n  swiper.scrollbar = {\n    el: null,\n    dragEl: null\n  };\n  function setTranslate() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    const params = swiper.params.scrollbar;\n    const progress = swiper.params.loop ? swiper.progressLoop : swiper.progress;\n    let newSize = dragSize;\n    let newPos = (trackSize - dragSize) * progress;\n    if (rtl) {\n      newPos = -newPos;\n      if (newPos > 0) {\n        newSize = dragSize - newPos;\n        newPos = 0;\n      } else if (-newPos + dragSize > trackSize) {\n        newSize = trackSize + newPos;\n      }\n    } else if (newPos < 0) {\n      newSize = dragSize + newPos;\n      newPos = 0;\n    } else if (newPos + dragSize > trackSize) {\n      newSize = trackSize - newPos;\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.transform = `translate3d(${newPos}px, 0, 0)`;\n      dragEl.style.width = `${newSize}px`;\n    } else {\n      dragEl.style.transform = `translate3d(0px, ${newPos}px, 0)`;\n      dragEl.style.height = `${newSize}px`;\n    }\n    if (params.hide) {\n      clearTimeout(timeout);\n      el.style.opacity = 1;\n      timeout = setTimeout(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n  }\n  function setTransition(duration) {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    swiper.scrollbar.dragEl.style.transitionDuration = `${duration}ms`;\n  }\n  function updateSize() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    const {\n      scrollbar\n    } = swiper;\n    const {\n      dragEl,\n      el\n    } = scrollbar;\n    dragEl.style.width = '';\n    dragEl.style.height = '';\n    trackSize = swiper.isHorizontal() ? el.offsetWidth : el.offsetHeight;\n    divider = swiper.size / (swiper.virtualSize + swiper.params.slidesOffsetBefore - (swiper.params.centeredSlides ? swiper.snapGrid[0] : 0));\n    if (swiper.params.scrollbar.dragSize === 'auto') {\n      dragSize = trackSize * divider;\n    } else {\n      dragSize = parseInt(swiper.params.scrollbar.dragSize, 10);\n    }\n    if (swiper.isHorizontal()) {\n      dragEl.style.width = `${dragSize}px`;\n    } else {\n      dragEl.style.height = `${dragSize}px`;\n    }\n    if (divider >= 1) {\n      el.style.display = 'none';\n    } else {\n      el.style.display = '';\n    }\n    if (swiper.params.scrollbar.hide) {\n      el.style.opacity = 0;\n    }\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      scrollbar.el.classList[swiper.isLocked ? 'add' : 'remove'](swiper.params.scrollbar.lockClass);\n    }\n  }\n  function getPointerPosition(e) {\n    return swiper.isHorizontal() ? e.clientX : e.clientY;\n  }\n  function setDragPosition(e) {\n    const {\n      scrollbar,\n      rtlTranslate: rtl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    let positionRatio;\n    positionRatio = (getPointerPosition(e) - elementOffset(el)[swiper.isHorizontal() ? 'left' : 'top'] - (dragStartPos !== null ? dragStartPos : dragSize / 2)) / (trackSize - dragSize);\n    positionRatio = Math.max(Math.min(positionRatio, 1), 0);\n    if (rtl) {\n      positionRatio = 1 - positionRatio;\n    }\n    const position = swiper.minTranslate() + (swiper.maxTranslate() - swiper.minTranslate()) * positionRatio;\n    swiper.updateProgress(position);\n    swiper.setTranslate(position);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  function onDragStart(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    isTouched = true;\n    dragStartPos = e.target === dragEl ? getPointerPosition(e) - e.target.getBoundingClientRect()[swiper.isHorizontal() ? 'left' : 'top'] : null;\n    e.preventDefault();\n    e.stopPropagation();\n    wrapperEl.style.transitionDuration = '100ms';\n    dragEl.style.transitionDuration = '100ms';\n    setDragPosition(e);\n    clearTimeout(dragTimeout);\n    el.style.transitionDuration = '0ms';\n    if (params.hide) {\n      el.style.opacity = 1;\n    }\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = 'none';\n    }\n    emit('scrollbarDragStart', e);\n  }\n  function onDragMove(e) {\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el,\n      dragEl\n    } = scrollbar;\n    if (!isTouched) return;\n    if (e.preventDefault && e.cancelable) e.preventDefault();else e.returnValue = false;\n    setDragPosition(e);\n    wrapperEl.style.transitionDuration = '0ms';\n    el.style.transitionDuration = '0ms';\n    dragEl.style.transitionDuration = '0ms';\n    emit('scrollbarDragMove', e);\n  }\n  function onDragEnd(e) {\n    const params = swiper.params.scrollbar;\n    const {\n      scrollbar,\n      wrapperEl\n    } = swiper;\n    const {\n      el\n    } = scrollbar;\n    if (!isTouched) return;\n    isTouched = false;\n    if (swiper.params.cssMode) {\n      swiper.wrapperEl.style['scroll-snap-type'] = '';\n      wrapperEl.style.transitionDuration = '';\n    }\n    if (params.hide) {\n      clearTimeout(dragTimeout);\n      dragTimeout = nextTick(() => {\n        el.style.opacity = 0;\n        el.style.transitionDuration = '400ms';\n      }, 1000);\n    }\n    emit('scrollbarDragEnd', e);\n    if (params.snapOnRelease) {\n      swiper.slideToClosest();\n    }\n  }\n  function events(method) {\n    const {\n      scrollbar,\n      params\n    } = swiper;\n    const el = scrollbar.el;\n    if (!el) return;\n    const target = el;\n    const activeListener = params.passiveListeners ? {\n      passive: false,\n      capture: false\n    } : false;\n    const passiveListener = params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    if (!target) return;\n    const eventMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n    target[eventMethod]('pointerdown', onDragStart, activeListener);\n    document[eventMethod]('pointermove', onDragMove, activeListener);\n    document[eventMethod]('pointerup', onDragEnd, passiveListener);\n  }\n  function enableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('on');\n  }\n  function disableDraggable() {\n    if (!swiper.params.scrollbar.el || !swiper.scrollbar.el) return;\n    events('off');\n  }\n  function init() {\n    const {\n      scrollbar,\n      el: swiperEl\n    } = swiper;\n    swiper.params.scrollbar = createElementIfNotDefined(swiper, swiper.originalParams.scrollbar, swiper.params.scrollbar, {\n      el: 'swiper-scrollbar'\n    });\n    const params = swiper.params.scrollbar;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = document.querySelectorAll(params.el);\n      if (!el.length) return;\n    } else if (!el) {\n      el = params.el;\n    }\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && el.length > 1 && swiperEl.querySelectorAll(params.el).length === 1) {\n      el = swiperEl.querySelector(params.el);\n    }\n    if (el.length > 0) el = el[0];\n    el.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    let dragEl;\n    if (el) {\n      dragEl = el.querySelector(classesToSelector(swiper.params.scrollbar.dragClass));\n      if (!dragEl) {\n        dragEl = createElement('div', swiper.params.scrollbar.dragClass);\n        el.append(dragEl);\n      }\n    }\n    Object.assign(scrollbar, {\n      el,\n      dragEl\n    });\n    if (params.draggable) {\n      enableDraggable();\n    }\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  }\n  function destroy() {\n    const params = swiper.params.scrollbar;\n    const el = swiper.scrollbar.el;\n    if (el) {\n      el.classList.remove(...classesToTokens(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass));\n    }\n    disableDraggable();\n  }\n  on('changeDirection', () => {\n    if (!swiper.scrollbar || !swiper.scrollbar.el) return;\n    const params = swiper.params.scrollbar;\n    let {\n      el\n    } = swiper.scrollbar;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.scrollbar.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      updateSize();\n      setTranslate();\n    }\n  });\n  on('update resize observerUpdate lock unlock changeDirection', () => {\n    updateSize();\n  });\n  on('setTranslate', () => {\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    setTransition(duration);\n  });\n  on('enable disable', () => {\n    const {\n      el\n    } = swiper.scrollbar;\n    if (el) {\n      el.classList[swiper.enabled ? 'remove' : 'add'](...classesToTokens(swiper.params.scrollbar.lockClass));\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.remove(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    init();\n    updateSize();\n    setTranslate();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    if (swiper.scrollbar.el) {\n      swiper.scrollbar.el.classList.add(...classesToTokens(swiper.params.scrollbar.scrollbarDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.scrollbar, {\n    enable,\n    disable,\n    updateSize,\n    setTranslate,\n    init,\n    destroy\n  });\n}\n\nexport { Scrollbar as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,WAAW,QAAQ,8BAA8B;AAC/D,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;AACzI,SAASL,CAAC,IAAIM,yBAAyB,QAAQ,6CAA6C;AAC5F,SAASN,CAAC,IAAIO,iBAAiB,QAAQ,mCAAmC;AAE1E,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,IAAIoB,SAAS,GAAG,KAAK;EACrB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,YAAY;EAChB,IAAIC,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACXV,YAAY,CAAC;IACXW,SAAS,EAAE;MACTC,EAAE,EAAE,IAAI;MACRJ,QAAQ,EAAE,MAAM;MAChBK,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,uBAAuB;MAClCC,SAAS,EAAE,uBAAuB;MAClCC,sBAAsB,EAAE,2BAA2B;MACnDC,eAAe,EAAE,6BAA6B;MAC9CC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACFrB,MAAM,CAACY,SAAS,GAAG;IACjBC,EAAE,EAAE,IAAI;IACRS,MAAM,EAAE;EACV,CAAC;EACD,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACvB,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzD,MAAM;MACJD,SAAS;MACTa,YAAY,EAAEC;IAChB,CAAC,GAAG1B,MAAM;IACV,MAAM;MACJsB,MAAM;MACNT;IACF,CAAC,GAAGD,SAAS;IACb,MAAMY,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAMe,QAAQ,GAAG3B,MAAM,CAACwB,MAAM,CAACI,IAAI,GAAG5B,MAAM,CAAC6B,YAAY,GAAG7B,MAAM,CAAC2B,QAAQ;IAC3E,IAAIG,OAAO,GAAGrB,QAAQ;IACtB,IAAIsB,MAAM,GAAG,CAACrB,SAAS,GAAGD,QAAQ,IAAIkB,QAAQ;IAC9C,IAAID,GAAG,EAAE;MACPK,MAAM,GAAG,CAACA,MAAM;MAChB,IAAIA,MAAM,GAAG,CAAC,EAAE;QACdD,OAAO,GAAGrB,QAAQ,GAAGsB,MAAM;QAC3BA,MAAM,GAAG,CAAC;MACZ,CAAC,MAAM,IAAI,CAACA,MAAM,GAAGtB,QAAQ,GAAGC,SAAS,EAAE;QACzCoB,OAAO,GAAGpB,SAAS,GAAGqB,MAAM;MAC9B;IACF,CAAC,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;MACrBD,OAAO,GAAGrB,QAAQ,GAAGsB,MAAM;MAC3BA,MAAM,GAAG,CAAC;IACZ,CAAC,MAAM,IAAIA,MAAM,GAAGtB,QAAQ,GAAGC,SAAS,EAAE;MACxCoB,OAAO,GAAGpB,SAAS,GAAGqB,MAAM;IAC9B;IACA,IAAI/B,MAAM,CAACgC,YAAY,CAAC,CAAC,EAAE;MACzBV,MAAM,CAACW,KAAK,CAACC,SAAS,GAAG,eAAeH,MAAM,WAAW;MACzDT,MAAM,CAACW,KAAK,CAACE,KAAK,GAAG,GAAGL,OAAO,IAAI;IACrC,CAAC,MAAM;MACLR,MAAM,CAACW,KAAK,CAACC,SAAS,GAAG,oBAAoBH,MAAM,QAAQ;MAC3DT,MAAM,CAACW,KAAK,CAACG,MAAM,GAAG,GAAGN,OAAO,IAAI;IACtC;IACA,IAAIN,MAAM,CAACV,IAAI,EAAE;MACfuB,YAAY,CAAC/B,OAAO,CAAC;MACrBO,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;MACpBhC,OAAO,GAAGiC,UAAU,CAAC,MAAM;QACzB1B,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;QACpBzB,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,OAAO;MACvC,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EACA,SAASC,aAAaA,CAACC,QAAQ,EAAE;IAC/B,IAAI,CAAC1C,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDb,MAAM,CAACY,SAAS,CAACU,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAG,GAAGE,QAAQ,IAAI;EACpE;EACA,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAC3C,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzD,MAAM;MACJD;IACF,CAAC,GAAGZ,MAAM;IACV,MAAM;MACJsB,MAAM;MACNT;IACF,CAAC,GAAGD,SAAS;IACbU,MAAM,CAACW,KAAK,CAACE,KAAK,GAAG,EAAE;IACvBb,MAAM,CAACW,KAAK,CAACG,MAAM,GAAG,EAAE;IACxB1B,SAAS,GAAGV,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAGnB,EAAE,CAAC+B,WAAW,GAAG/B,EAAE,CAACgC,YAAY;IACpElC,OAAO,GAAGX,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC+C,WAAW,GAAG/C,MAAM,CAACwB,MAAM,CAACwB,kBAAkB,IAAIhD,MAAM,CAACwB,MAAM,CAACyB,cAAc,GAAGjD,MAAM,CAACkD,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzI,IAAIlD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACH,QAAQ,KAAK,MAAM,EAAE;MAC/CA,QAAQ,GAAGC,SAAS,GAAGC,OAAO;IAChC,CAAC,MAAM;MACLF,QAAQ,GAAG0C,QAAQ,CAACnD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACH,QAAQ,EAAE,EAAE,CAAC;IAC3D;IACA,IAAIT,MAAM,CAACgC,YAAY,CAAC,CAAC,EAAE;MACzBV,MAAM,CAACW,KAAK,CAACE,KAAK,GAAG,GAAG1B,QAAQ,IAAI;IACtC,CAAC,MAAM;MACLa,MAAM,CAACW,KAAK,CAACG,MAAM,GAAG,GAAG3B,QAAQ,IAAI;IACvC;IACA,IAAIE,OAAO,IAAI,CAAC,EAAE;MAChBE,EAAE,CAACoB,KAAK,CAACmB,OAAO,GAAG,MAAM;IAC3B,CAAC,MAAM;MACLvC,EAAE,CAACoB,KAAK,CAACmB,OAAO,GAAG,EAAE;IACvB;IACA,IAAIpD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACE,IAAI,EAAE;MAChCD,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;IACtB;IACA,IAAItC,MAAM,CAACwB,MAAM,CAAC6B,aAAa,IAAIrD,MAAM,CAACsD,OAAO,EAAE;MACjD1C,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACwD,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACxD,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC;IAC/F;EACF;EACA,SAASwC,kBAAkBA,CAACC,CAAC,EAAE;IAC7B,OAAO1D,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAG0B,CAAC,CAACC,OAAO,GAAGD,CAAC,CAACE,OAAO;EACtD;EACA,SAASC,eAAeA,CAACH,CAAC,EAAE;IAC1B,MAAM;MACJ9C,SAAS;MACTa,YAAY,EAAEC;IAChB,CAAC,GAAG1B,MAAM;IACV,MAAM;MACJa;IACF,CAAC,GAAGD,SAAS;IACb,IAAIkD,aAAa;IACjBA,aAAa,GAAG,CAACL,kBAAkB,CAACC,CAAC,CAAC,GAAG/D,aAAa,CAACkB,EAAE,CAAC,CAACb,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,IAAIxB,YAAY,KAAK,IAAI,GAAGA,YAAY,GAAGC,QAAQ,GAAG,CAAC,CAAC,KAAKC,SAAS,GAAGD,QAAQ,CAAC;IACpLqD,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACvD,IAAIpC,GAAG,EAAE;MACPoC,aAAa,GAAG,CAAC,GAAGA,aAAa;IACnC;IACA,MAAMI,QAAQ,GAAGlE,MAAM,CAACmE,YAAY,CAAC,CAAC,GAAG,CAACnE,MAAM,CAACoE,YAAY,CAAC,CAAC,GAAGpE,MAAM,CAACmE,YAAY,CAAC,CAAC,IAAIL,aAAa;IACxG9D,MAAM,CAACqE,cAAc,CAACH,QAAQ,CAAC;IAC/BlE,MAAM,CAACuB,YAAY,CAAC2C,QAAQ,CAAC;IAC7BlE,MAAM,CAACsE,iBAAiB,CAAC,CAAC;IAC1BtE,MAAM,CAACuE,mBAAmB,CAAC,CAAC;EAC9B;EACA,SAASC,WAAWA,CAACd,CAAC,EAAE;IACtB,MAAMlC,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAM;MACJA,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa,EAAE;MACFS;IACF,CAAC,GAAGV,SAAS;IACbP,SAAS,GAAG,IAAI;IAChBG,YAAY,GAAGkD,CAAC,CAACgB,MAAM,KAAKpD,MAAM,GAAGmC,kBAAkB,CAACC,CAAC,CAAC,GAAGA,CAAC,CAACgB,MAAM,CAACC,qBAAqB,CAAC,CAAC,CAAC3E,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI;IAC5I0B,CAAC,CAACkB,cAAc,CAAC,CAAC;IAClBlB,CAAC,CAACmB,eAAe,CAAC,CAAC;IACnBJ,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,OAAO;IAC5ClB,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAG,OAAO;IACzCqB,eAAe,CAACH,CAAC,CAAC;IAClBrB,YAAY,CAAC9B,WAAW,CAAC;IACzBM,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACnC,IAAIhB,MAAM,CAACV,IAAI,EAAE;MACfD,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;IACtB;IACA,IAAItC,MAAM,CAACwB,MAAM,CAACsD,OAAO,EAAE;MACzB9E,MAAM,CAACyE,SAAS,CAACxC,KAAK,CAAC,kBAAkB,CAAC,GAAG,MAAM;IACrD;IACA9B,IAAI,CAAC,oBAAoB,EAAEuD,CAAC,CAAC;EAC/B;EACA,SAASqB,UAAUA,CAACrB,CAAC,EAAE;IACrB,MAAM;MACJ9C,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa,EAAE;MACFS;IACF,CAAC,GAAGV,SAAS;IACb,IAAI,CAACP,SAAS,EAAE;IAChB,IAAIqD,CAAC,CAACkB,cAAc,IAAIlB,CAAC,CAACsB,UAAU,EAAEtB,CAAC,CAACkB,cAAc,CAAC,CAAC,CAAC,KAAKlB,CAAC,CAACuB,WAAW,GAAG,KAAK;IACnFpB,eAAe,CAACH,CAAC,CAAC;IAClBe,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,KAAK;IAC1C3B,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACnClB,MAAM,CAACW,KAAK,CAACO,kBAAkB,GAAG,KAAK;IACvCrC,IAAI,CAAC,mBAAmB,EAAEuD,CAAC,CAAC;EAC9B;EACA,SAASwB,SAASA,CAACxB,CAAC,EAAE;IACpB,MAAMlC,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAM;MACJA,SAAS;MACT6D;IACF,CAAC,GAAGzE,MAAM;IACV,MAAM;MACJa;IACF,CAAC,GAAGD,SAAS;IACb,IAAI,CAACP,SAAS,EAAE;IAChBA,SAAS,GAAG,KAAK;IACjB,IAAIL,MAAM,CAACwB,MAAM,CAACsD,OAAO,EAAE;MACzB9E,MAAM,CAACyE,SAAS,CAACxC,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE;MAC/CwC,SAAS,CAACxC,KAAK,CAACO,kBAAkB,GAAG,EAAE;IACzC;IACA,IAAIhB,MAAM,CAACV,IAAI,EAAE;MACfuB,YAAY,CAAC9B,WAAW,CAAC;MACzBA,WAAW,GAAGd,QAAQ,CAAC,MAAM;QAC3BoB,EAAE,CAACoB,KAAK,CAACK,OAAO,GAAG,CAAC;QACpBzB,EAAE,CAACoB,KAAK,CAACO,kBAAkB,GAAG,OAAO;MACvC,CAAC,EAAE,IAAI,CAAC;IACV;IACArC,IAAI,CAAC,kBAAkB,EAAEuD,CAAC,CAAC;IAC3B,IAAIlC,MAAM,CAACR,aAAa,EAAE;MACxBhB,MAAM,CAACmF,cAAc,CAAC,CAAC;IACzB;EACF;EACA,SAASC,MAAMA,CAACC,MAAM,EAAE;IACtB,MAAM;MACJzE,SAAS;MACTY;IACF,CAAC,GAAGxB,MAAM;IACV,MAAMa,EAAE,GAAGD,SAAS,CAACC,EAAE;IACvB,IAAI,CAACA,EAAE,EAAE;IACT,MAAM6D,MAAM,GAAG7D,EAAE;IACjB,MAAMyE,cAAc,GAAG9D,MAAM,CAAC+D,gBAAgB,GAAG;MAC/CC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,MAAMC,eAAe,GAAGlE,MAAM,CAAC+D,gBAAgB,GAAG;MAChDC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC,GAAG,KAAK;IACT,IAAI,CAACf,MAAM,EAAE;IACb,MAAMiB,WAAW,GAAGN,MAAM,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB;IAChFX,MAAM,CAACiB,WAAW,CAAC,CAAC,aAAa,EAAEnB,WAAW,EAAEc,cAAc,CAAC;IAC/DlF,QAAQ,CAACuF,WAAW,CAAC,CAAC,aAAa,EAAEZ,UAAU,EAAEO,cAAc,CAAC;IAChElF,QAAQ,CAACuF,WAAW,CAAC,CAAC,WAAW,EAAET,SAAS,EAAEQ,eAAe,CAAC;EAChE;EACA,SAASE,eAAeA,CAAA,EAAG;IACzB,IAAI,CAAC5F,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDuE,MAAM,CAAC,IAAI,CAAC;EACd;EACA,SAASS,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC7F,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACC,EAAE,IAAI,CAACb,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IACzDuE,MAAM,CAAC,KAAK,CAAC;EACf;EACA,SAASU,IAAIA,CAAA,EAAG;IACd,MAAM;MACJlF,SAAS;MACTC,EAAE,EAAEkF;IACN,CAAC,GAAG/F,MAAM;IACVA,MAAM,CAACwB,MAAM,CAACZ,SAAS,GAAGhB,yBAAyB,CAACI,MAAM,EAAEA,MAAM,CAACgG,cAAc,CAACpF,SAAS,EAAEZ,MAAM,CAACwB,MAAM,CAACZ,SAAS,EAAE;MACpHC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,MAAMW,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,IAAI,CAACY,MAAM,CAACX,EAAE,EAAE;IAChB,IAAIA,EAAE;IACN,IAAI,OAAOW,MAAM,CAACX,EAAE,KAAK,QAAQ,IAAIb,MAAM,CAACiG,SAAS,EAAE;MACrDpF,EAAE,GAAGb,MAAM,CAACa,EAAE,CAACqF,aAAa,CAAC1E,MAAM,CAACX,EAAE,CAAC;IACzC;IACA,IAAI,CAACA,EAAE,IAAI,OAAOW,MAAM,CAACX,EAAE,KAAK,QAAQ,EAAE;MACxCA,EAAE,GAAGT,QAAQ,CAAC+F,gBAAgB,CAAC3E,MAAM,CAACX,EAAE,CAAC;MACzC,IAAI,CAACA,EAAE,CAACuF,MAAM,EAAE;IAClB,CAAC,MAAM,IAAI,CAACvF,EAAE,EAAE;MACdA,EAAE,GAAGW,MAAM,CAACX,EAAE;IAChB;IACA,IAAIb,MAAM,CAACwB,MAAM,CAAC6E,iBAAiB,IAAI,OAAO7E,MAAM,CAACX,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACuF,MAAM,GAAG,CAAC,IAAIL,QAAQ,CAACI,gBAAgB,CAAC3E,MAAM,CAACX,EAAE,CAAC,CAACuF,MAAM,KAAK,CAAC,EAAE;MAC1IvF,EAAE,GAAGkF,QAAQ,CAACG,aAAa,CAAC1E,MAAM,CAACX,EAAE,CAAC;IACxC;IACA,IAAIA,EAAE,CAACuF,MAAM,GAAG,CAAC,EAAEvF,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IAC7BA,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAACtG,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAGR,MAAM,CAACJ,eAAe,GAAGI,MAAM,CAACH,aAAa,CAAC;IACvF,IAAIC,MAAM;IACV,IAAIT,EAAE,EAAE;MACNS,MAAM,GAAGT,EAAE,CAACqF,aAAa,CAACrG,iBAAiB,CAACG,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACM,SAAS,CAAC,CAAC;MAC/E,IAAI,CAACI,MAAM,EAAE;QACXA,MAAM,GAAG/B,aAAa,CAAC,KAAK,EAAES,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACM,SAAS,CAAC;QAChEL,EAAE,CAAC0F,MAAM,CAACjF,MAAM,CAAC;MACnB;IACF;IACAkF,MAAM,CAACC,MAAM,CAAC7F,SAAS,EAAE;MACvBC,EAAE;MACFS;IACF,CAAC,CAAC;IACF,IAAIE,MAAM,CAACT,SAAS,EAAE;MACpB6E,eAAe,CAAC,CAAC;IACnB;IACA,IAAI/E,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACsD,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAGjE,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC,CAAC;IACxG;EACF;EACA,SAASyF,OAAOA,CAAA,EAAG;IACjB,MAAMlF,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,MAAMC,EAAE,GAAGb,MAAM,CAACY,SAAS,CAACC,EAAE;IAC9B,IAAIA,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC,GAAGtH,eAAe,CAACW,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAGR,MAAM,CAACJ,eAAe,GAAGI,MAAM,CAACH,aAAa,CAAC,CAAC;IAChH;IACAwE,gBAAgB,CAAC,CAAC;EACpB;EACA3F,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAI,CAACF,MAAM,CAACY,SAAS,IAAI,CAACZ,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;IAC/C,MAAMW,MAAM,GAAGxB,MAAM,CAACwB,MAAM,CAACZ,SAAS;IACtC,IAAI;MACFC;IACF,CAAC,GAAGb,MAAM,CAACY,SAAS;IACpBC,EAAE,GAAG1B,iBAAiB,CAAC0B,EAAE,CAAC;IAC1BA,EAAE,CAAC+F,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACtD,SAAS,CAACoD,MAAM,CAACnF,MAAM,CAACJ,eAAe,EAAEI,MAAM,CAACH,aAAa,CAAC;MACpEwF,KAAK,CAACtD,SAAS,CAAC+C,GAAG,CAACtG,MAAM,CAACgC,YAAY,CAAC,CAAC,GAAGR,MAAM,CAACJ,eAAe,GAAGI,MAAM,CAACH,aAAa,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EACFnB,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAAC0C,OAAO,KAAK,KAAK,EAAE;MAC7C;MACAwD,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLhB,IAAI,CAAC,CAAC;MACNnD,UAAU,CAAC,CAAC;MACZpB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;EACFrB,EAAE,CAAC,0DAA0D,EAAE,MAAM;IACnEyC,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACFzC,EAAE,CAAC,cAAc,EAAE,MAAM;IACvBqB,YAAY,CAAC,CAAC;EAChB,CAAC,CAAC;EACFrB,EAAE,CAAC,eAAe,EAAE,CAAC6G,EAAE,EAAErE,QAAQ,KAAK;IACpCD,aAAa,CAACC,QAAQ,CAAC;EACzB,CAAC,CAAC;EACFxC,EAAE,CAAC,gBAAgB,EAAE,MAAM;IACzB,MAAM;MACJW;IACF,CAAC,GAAGb,MAAM,CAACY,SAAS;IACpB,IAAIC,EAAE,EAAE;MACNA,EAAE,CAAC0C,SAAS,CAACvD,MAAM,CAACsD,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC,GAAGjE,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACK,SAAS,CAAC,CAAC;IACxG;EACF,CAAC,CAAC;EACFf,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBwG,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACF,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACnBhH,MAAM,CAACa,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC,GAAGtH,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC,CAAC;IAC9F,IAAInB,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;MACvBb,MAAM,CAACY,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAACoD,MAAM,CAAC,GAAGtH,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC,CAAC;IAC1G;IACA2E,IAAI,CAAC,CAAC;IACNnD,UAAU,CAAC,CAAC;IACZpB,YAAY,CAAC,CAAC;EAChB,CAAC;EACD,MAAMuF,OAAO,GAAGA,CAAA,KAAM;IACpB9G,MAAM,CAACa,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAAC,GAAGjH,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC,CAAC;IAC3F,IAAInB,MAAM,CAACY,SAAS,CAACC,EAAE,EAAE;MACvBb,MAAM,CAACY,SAAS,CAACC,EAAE,CAAC0C,SAAS,CAAC+C,GAAG,CAAC,GAAGjH,eAAe,CAACW,MAAM,CAACwB,MAAM,CAACZ,SAAS,CAACO,sBAAsB,CAAC,CAAC;IACvG;IACAuF,OAAO,CAAC,CAAC;EACX,CAAC;EACDF,MAAM,CAACC,MAAM,CAACzG,MAAM,CAACY,SAAS,EAAE;IAC9BoG,MAAM;IACNF,OAAO;IACPnE,UAAU;IACVpB,YAAY;IACZuE,IAAI;IACJY;EACF,CAAC,CAAC;AACJ;AAEA,SAAS5G,SAAS,IAAImH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}