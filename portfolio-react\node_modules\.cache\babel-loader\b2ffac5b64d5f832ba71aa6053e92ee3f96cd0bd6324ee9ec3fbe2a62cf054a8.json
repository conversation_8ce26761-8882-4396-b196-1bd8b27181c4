{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\NDANotification.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport './NDANotification.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NDANotification = ({\n  isOpen,\n  onClose,\n  projectTitle\n}) => {\n  _s();\n  useEffect(() => {\n    const handleEscape = e => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"nda-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nda-content\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nda-icon\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"nda-title\",\n        children: \"Project Under NDA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"nda-message\",\n        children: \"This project is protected under a Non-Disclosure Agreement (NDA). Due to confidentiality requirements, detailed information and live demos cannot be shared publicly.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nda-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"What I can share:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"General technical scope and technologies used\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"My role and contributions to the project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Skills and methodologies applied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Overall project outcomes and achievements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nda-contact\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"For more information about this project, please contact me directly.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"nda-close-btn\",\n        onClick: onClose,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Understood\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(NDANotification, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = NDANotification;\nexport default NDANotification;\nvar _c;\n$RefreshReg$(_c, \"NDANotification\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "NDANotification", "isOpen", "onClose", "projectTitle", "_s", "handleEscape", "e", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "className", "onClick", "children", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/NDANotification.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport './NDANotification.css';\n\nconst NDANotification = ({ isOpen, onClose, projectTitle }) => {\n  useEffect(() => {\n    const handleEscape = (e) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"nda-overlay\" onClick={onClose}>\n      <div className=\"nda-content\" onClick={(e) => e.stopPropagation()}>\n        <div className=\"nda-icon\">\n          <span>🔒</span>\n        </div>\n        <h2 className=\"nda-title\">Project Under NDA</h2>\n        <p className=\"nda-message\">\n          This project is protected under a Non-Disclosure Agreement (NDA). \n          Due to confidentiality requirements, detailed information and live demos cannot be shared publicly.\n        </p>\n        <div className=\"nda-details\">\n          <h3>What I can share:</h3>\n          <ul>\n            <li>General technical scope and technologies used</li>\n            <li>My role and contributions to the project</li>\n            <li>Skills and methodologies applied</li>\n            <li>Overall project outcomes and achievements</li>\n          </ul>\n        </div>\n        <div className=\"nda-contact\">\n          <p>For more information about this project, please contact me directly.</p>\n        </div>\n        <button className=\"nda-close-btn\" onClick={onClose}>\n          <span>Understood</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default NDANotification;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC7DP,SAAS,CAAC,MAAM;IACd,MAAMQ,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;QACtBL,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAED,IAAID,MAAM,EAAE;MACVO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,YAAY,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACX,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErB,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKe,SAAS,EAAC,aAAa;IAACC,OAAO,EAAEb,OAAQ;IAAAc,QAAA,eAC5CjB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAACC,OAAO,EAAGT,CAAC,IAAKA,CAAC,CAACW,eAAe,CAAC,CAAE;MAAAD,QAAA,gBAC/DjB,OAAA;QAAKe,SAAS,EAAC,UAAU;QAAAE,QAAA,eACvBjB,OAAA;UAAAiB,QAAA,EAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACNtB,OAAA;QAAIe,SAAS,EAAC,WAAW;QAAAE,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDtB,OAAA;QAAGe,SAAS,EAAC,aAAa;QAAAE,QAAA,EAAC;MAG3B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJtB,OAAA;QAAKe,SAAS,EAAC,aAAa;QAAAE,QAAA,gBAC1BjB,OAAA;UAAAiB,QAAA,EAAI;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BtB,OAAA;UAAAiB,QAAA,gBACEjB,OAAA;YAAAiB,QAAA,EAAI;UAA6C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDtB,OAAA;YAAAiB,QAAA,EAAI;UAAwC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDtB,OAAA;YAAAiB,QAAA,EAAI;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCtB,OAAA;YAAAiB,QAAA,EAAI;UAAyC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNtB,OAAA;QAAKe,SAAS,EAAC,aAAa;QAAAE,QAAA,eAC1BjB,OAAA;UAAAiB,QAAA,EAAG;QAAoE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACNtB,OAAA;QAAQe,SAAS,EAAC,eAAe;QAACC,OAAO,EAAEb,OAAQ;QAAAc,QAAA,eACjDjB,OAAA;UAAAiB,QAAA,EAAM;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAlDIJ,eAAe;AAAAsB,EAAA,GAAftB,eAAe;AAoDrB,eAAeA,eAAe;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}