{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\ProjectImageSwiper.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\nimport FullscreenImageViewer from './FullscreenImageViewer';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = ({\n  images,\n  title,\n  isNDA = false\n}) => {\n  _s();\n  const [fullscreenImage, setFullscreenImage] = useState(null);\n  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);\n  const handleImageClick = (e, imageUrl) => {\n    // Prevent event bubbling to parent elements\n    e.stopPropagation();\n\n    // Only open fullscreen for non-NDA projects\n    if (!isNDA) {\n      setFullscreenImage(imageUrl);\n      setIsFullscreenOpen(true);\n    }\n  };\n  const closeFullscreen = () => {\n    setIsFullscreenOpen(false);\n    setFullscreenImage(null);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"project-image-swiper\",\n      children: /*#__PURE__*/_jsxDEV(Swiper, {\n        modules: [Navigation, Pagination, Autoplay, EffectFade],\n        spaceBetween: 0,\n        slidesPerView: 1,\n        navigation: {\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom'\n        },\n        pagination: {\n          clickable: true,\n          dynamicBullets: true\n        },\n        autoplay: {\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true\n        },\n        effect: \"fade\",\n        fadeEffect: {\n          crossFade: true\n        },\n        loop: images.length > 1,\n        className: \"project-swiper\",\n        children: [images.map((image, index) => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-slide-content\",\n            onClick: e => handleImageClick(e, image),\n            style: {\n              cursor: 'zoom-in'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: `${title} - View ${index + 1}`,\n              className: \"swiper-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)), images.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-prev-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2039\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-next-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u203A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swipe-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"swipe-text\",\n            children: \"Swipe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-animation\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"double-click-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"double-click-text\",\n            children: isNDA ? 'Double-click for NDA info' : 'Double-click to open'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"double-click-icon\",\n            children: isNDA ? '🔒' : '👆'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 9\n        }, this), !isNDA && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fullscreen-click-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"fullscreen-click-text\",\n            children: \"Click for fullscreen\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"fullscreen-click-icon\",\n            children: \"\\uD83D\\uDD0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FullscreenImageViewer, {\n      isOpen: isFullscreenOpen,\n      imageUrl: fullscreenImage,\n      imageAlt: `${title} - Fullscreen View`,\n      onClose: closeFullscreen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProjectImageSwiper, \"5dDwe1gb7JctFoRpQgjqf9ucKjE=\");\n_c = ProjectImageSwiper;\nexport default ProjectImageSwiper;\nvar _c;\n$RefreshReg$(_c, \"ProjectImageSwiper\");", "map": {"version": 3, "names": ["React", "useState", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Autoplay", "EffectFade", "FullscreenImageViewer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProjectImageSwiper", "images", "title", "isNDA", "_s", "fullscreenImage", "setFullscreenImage", "isFullscreenOpen", "setIsFullscreenOpen", "handleImageClick", "e", "imageUrl", "stopPropagation", "closeFullscreen", "children", "className", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "nextEl", "prevEl", "pagination", "clickable", "dynamicBullets", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "effect", "fadeEffect", "crossFade", "loop", "length", "map", "image", "index", "onClick", "style", "cursor", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "imageAlt", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ProjectImageSwiper.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\nimport FullscreenImageViewer from './FullscreenImageViewer';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\nconst ProjectImageSwiper = ({ images, title, isNDA = false }) => {\n  const [fullscreenImage, setFullscreenImage] = useState(null);\n  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);\n\n  const handleImageClick = (e, imageUrl) => {\n    // Prevent event bubbling to parent elements\n    e.stopPropagation();\n\n    // Only open fullscreen for non-NDA projects\n    if (!isNDA) {\n      setFullscreenImage(imageUrl);\n      setIsFullscreenOpen(true);\n    }\n  };\n\n  const closeFullscreen = () => {\n    setIsFullscreenOpen(false);\n    setFullscreenImage(null);\n  };\n\n  return (\n    <>\n      <div className=\"project-image-swiper\">\n      <Swiper\n        modules={[Navigation, Pagination, Autoplay, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        navigation={{\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom',\n        }}\n        pagination={{\n          clickable: true,\n          dynamicBullets: true,\n        }}\n        autoplay={{\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true,\n        }}\n        effect=\"fade\"\n        fadeEffect={{\n          crossFade: true\n        }}\n        loop={images.length > 1}\n        className=\"project-swiper\"\n      >\n        {images.map((image, index) => (\n          <SwiperSlide key={index}>\n            <div\n              className=\"swiper-slide-content\"\n              onClick={(e) => handleImageClick(e, image)}\n              style={{ cursor: 'zoom-in' }}\n            >\n              <img\n                src={image}\n                alt={`${title} - View ${index + 1}`}\n                className=\"swiper-image\"\n              />\n            </div>\n          </SwiperSlide>\n        ))}\n        \n        {/* Custom Navigation Buttons */}\n        {images.length > 1 && (\n          <>\n            <div className=\"swiper-button-prev-custom\">\n              <span>‹</span>\n            </div>\n            <div className=\"swiper-button-next-custom\">\n              <span>›</span>\n            </div>\n          </>\n        )}\n        \n        {/* Swipe Indicator */}\n        {images.length > 1 && (\n          <div className=\"swipe-indicator\">\n            <span className=\"swipe-text\">Swipe</span>\n            <div className=\"swipe-animation\">\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n            </div>\n          </div>\n        )}\n\n        {/* Click Indicators */}\n        <div className=\"double-click-indicator\">\n          <span className=\"double-click-text\">\n            {isNDA ? 'Double-click for NDA info' : 'Double-click to open'}\n          </span>\n          <span className=\"double-click-icon\">{isNDA ? '🔒' : '👆'}</span>\n        </div>\n\n        {!isNDA && (\n          <div className=\"fullscreen-click-indicator\">\n            <span className=\"fullscreen-click-text\">Click for fullscreen</span>\n            <span className=\"fullscreen-click-icon\">🔍</span>\n          </div>\n        )}\n      </Swiper>\n      </div>\n\n      {/* Fullscreen Image Viewer */}\n      <FullscreenImageViewer\n        isOpen={isFullscreenOpen}\n        imageUrl={fullscreenImage}\n        imageAlt={`${title} - Fullscreen View`}\n        onClose={closeFullscreen}\n      />\n    </>\n  );\n};\n\nexport default ProjectImageSwiper;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAC7E,OAAOC,qBAAqB,MAAM,yBAAyB;;AAE3D;AACA,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,KAAK,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMqB,gBAAgB,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACxC;IACAD,CAAC,CAACE,eAAe,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACT,KAAK,EAAE;MACVG,kBAAkB,CAACK,QAAQ,CAAC;MAC5BH,mBAAmB,CAAC,IAAI,CAAC;IAC3B;EACF,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BL,mBAAmB,CAAC,KAAK,CAAC;IAC1BF,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,oBACET,OAAA,CAAAE,SAAA;IAAAe,QAAA,gBACEjB,OAAA;MAAKkB,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACrCjB,OAAA,CAACR,MAAM;QACL2B,OAAO,EAAE,CAACzB,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,CAAE;QACxDuB,YAAY,EAAE,CAAE;QAChBC,aAAa,EAAE,CAAE;QACjBC,UAAU,EAAE;UACVC,MAAM,EAAE,4BAA4B;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAE;UACVC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAE;QACFC,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAI;UACXC,oBAAoB,EAAE,KAAK;UAC3BC,iBAAiB,EAAE;QACrB,CAAE;QACFC,MAAM,EAAC,MAAM;QACbC,UAAU,EAAE;UACVC,SAAS,EAAE;QACb,CAAE;QACFC,IAAI,EAAE/B,MAAM,CAACgC,MAAM,GAAG,CAAE;QACxBlB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,GAEzBb,MAAM,CAACiC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBvC,OAAA,CAACP,WAAW;UAAAwB,QAAA,eACVjB,OAAA;YACEkB,SAAS,EAAC,sBAAsB;YAChCsB,OAAO,EAAG3B,CAAC,IAAKD,gBAAgB,CAACC,CAAC,EAAEyB,KAAK,CAAE;YAC3CG,KAAK,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAzB,QAAA,eAE7BjB,OAAA;cACE2C,GAAG,EAAEL,KAAM;cACXM,GAAG,EAAE,GAAGvC,KAAK,WAAWkC,KAAK,GAAG,CAAC,EAAG;cACpCrB,SAAS,EAAC;YAAc;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAXUT,KAAK;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYV,CACd,CAAC,EAGD5C,MAAM,CAACgC,MAAM,GAAG,CAAC,iBAChBpC,OAAA,CAAAE,SAAA;UAAAe,QAAA,gBACEjB,OAAA;YAAKkB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCjB,OAAA;cAAAiB,QAAA,EAAM;YAAC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNhD,OAAA;YAAKkB,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxCjB,OAAA;cAAAiB,QAAA,EAAM;YAAC;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA,eACN,CACH,EAGA5C,MAAM,CAACgC,MAAM,GAAG,CAAC,iBAChBpC,OAAA;UAAKkB,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BjB,OAAA;YAAMkB,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzChD,OAAA;YAAKkB,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9BjB,OAAA;cAAKkB,SAAS,EAAC;YAAW;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjChD,OAAA;cAAKkB,SAAS,EAAC;YAAW;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjChD,OAAA;cAAKkB,SAAS,EAAC;YAAW;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDhD,OAAA;UAAKkB,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrCjB,OAAA;YAAMkB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAChCX,KAAK,GAAG,2BAA2B,GAAG;UAAsB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACPhD,OAAA;YAAMkB,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAEX,KAAK,GAAG,IAAI,GAAG;UAAI;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,EAEL,CAAC1C,KAAK,iBACLN,OAAA;UAAKkB,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCjB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAoB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnEhD,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAD,QAAA,EAAC;UAAE;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhD,OAAA,CAACF,qBAAqB;MACpBmD,MAAM,EAAEvC,gBAAiB;MACzBI,QAAQ,EAAEN,eAAgB;MAC1B0C,QAAQ,EAAE,GAAG7C,KAAK,oBAAqB;MACvC8C,OAAO,EAAEnC;IAAgB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACzC,EAAA,CAjHIJ,kBAAkB;AAAAiD,EAAA,GAAlBjD,kBAAkB;AAmHxB,eAAeA,kBAAkB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}