{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\ProjectImageSwiper.js\";\nimport React, { useState } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\nimport FullscreenImageViewer from './FullscreenImageViewer';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = ({\n  images,\n  title\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"project-image-swiper\",\n    children: /*#__PURE__*/_jsxDEV(Swiper, {\n      modules: [Navigation, Pagination, Autoplay, EffectFade],\n      spaceBetween: 0,\n      slidesPerView: 1,\n      navigation: {\n        nextEl: '.swiper-button-next-custom',\n        prevEl: '.swiper-button-prev-custom'\n      },\n      pagination: {\n        clickable: true,\n        dynamicBullets: true\n      },\n      autoplay: {\n        delay: 4000,\n        disableOnInteraction: false,\n        pauseOnMouseEnter: true\n      },\n      effect: \"fade\",\n      fadeEffect: {\n        crossFade: true\n      },\n      loop: images.length > 1,\n      className: \"project-swiper\",\n      children: [images.map((image, index) => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swiper-slide-content\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: image,\n            alt: `${title} - View ${index + 1}`,\n            className: \"swiper-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)), images.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swiper-button-prev-custom\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2039\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swiper-button-next-custom\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u203A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"swipe-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"swipe-text\",\n          children: \"Swipe\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swipe-animation\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"double-click-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"double-click-text\",\n          children: \"Double-click to open\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"double-click-icon\",\n          children: \"\\uD83D\\uDC46\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = ProjectImageSwiper;\nexport default ProjectImageSwiper;\nvar _c;\n$RefreshReg$(_c, \"ProjectImageSwiper\");", "map": {"version": 3, "names": ["React", "useState", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Autoplay", "EffectFade", "FullscreenImageViewer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProjectImageSwiper", "images", "title", "className", "children", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "nextEl", "prevEl", "pagination", "clickable", "dynamicBullets", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "effect", "fadeEffect", "crossFade", "loop", "length", "map", "image", "index", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ProjectImageSwiper.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\nimport FullscreenImageViewer from './FullscreenImageViewer';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\nconst ProjectImageSwiper = ({ images, title }) => {\n  return (\n    <div className=\"project-image-swiper\">\n      <Swiper\n        modules={[Navigation, Pagination, Autoplay, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        navigation={{\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom',\n        }}\n        pagination={{\n          clickable: true,\n          dynamicBullets: true,\n        }}\n        autoplay={{\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true,\n        }}\n        effect=\"fade\"\n        fadeEffect={{\n          crossFade: true\n        }}\n        loop={images.length > 1}\n        className=\"project-swiper\"\n      >\n        {images.map((image, index) => (\n          <SwiperSlide key={index}>\n            <div className=\"swiper-slide-content\">\n              <img\n                src={image}\n                alt={`${title} - View ${index + 1}`}\n                className=\"swiper-image\"\n              />\n            </div>\n          </SwiperSlide>\n        ))}\n        \n        {/* Custom Navigation Buttons */}\n        {images.length > 1 && (\n          <>\n            <div className=\"swiper-button-prev-custom\">\n              <span>‹</span>\n            </div>\n            <div className=\"swiper-button-next-custom\">\n              <span>›</span>\n            </div>\n          </>\n        )}\n        \n        {/* Swipe Indicator */}\n        {images.length > 1 && (\n          <div className=\"swipe-indicator\">\n            <span className=\"swipe-text\">Swipe</span>\n            <div className=\"swipe-animation\">\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n            </div>\n          </div>\n        )}\n\n        {/* Double-click Indicator */}\n        <div className=\"double-click-indicator\">\n          <span className=\"double-click-text\">Double-click to open</span>\n          <span className=\"double-click-icon\">👆</span>\n        </div>\n      </Swiper>\n    </div>\n  );\n};\n\nexport default ProjectImageSwiper;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;AAC7E,OAAOC,qBAAqB,MAAM,yBAAyB;;AAE3D;AACA,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAM,CAAC,KAAK;EAChD,oBACEL,OAAA;IAAKM,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACnCP,OAAA,CAACR,MAAM;MACLgB,OAAO,EAAE,CAACd,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,CAAE;MACxDY,YAAY,EAAE,CAAE;MAChBC,aAAa,EAAE,CAAE;MACjBC,UAAU,EAAE;QACVC,MAAM,EAAE,4BAA4B;QACpCC,MAAM,EAAE;MACV,CAAE;MACFC,UAAU,EAAE;QACVC,SAAS,EAAE,IAAI;QACfC,cAAc,EAAE;MAClB,CAAE;MACFC,QAAQ,EAAE;QACRC,KAAK,EAAE,IAAI;QACXC,oBAAoB,EAAE,KAAK;QAC3BC,iBAAiB,EAAE;MACrB,CAAE;MACFC,MAAM,EAAC,MAAM;MACbC,UAAU,EAAE;QACVC,SAAS,EAAE;MACb,CAAE;MACFC,IAAI,EAAEpB,MAAM,CAACqB,MAAM,GAAG,CAAE;MACxBnB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,GAEzBH,MAAM,CAACsB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvB5B,OAAA,CAACP,WAAW;QAAAc,QAAA,eACVP,OAAA;UAAKM,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnCP,OAAA;YACE6B,GAAG,EAAEF,KAAM;YACXG,GAAG,EAAE,GAAGzB,KAAK,WAAWuB,KAAK,GAAG,CAAC,EAAG;YACpCtB,SAAS,EAAC;UAAc;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GAPUN,KAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQV,CACd,CAAC,EAGD9B,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAChBzB,OAAA,CAAAE,SAAA;QAAAK,QAAA,gBACEP,OAAA;UAAKM,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCP,OAAA;YAAAO,QAAA,EAAM;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNlC,OAAA;UAAKM,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCP,OAAA;YAAAO,QAAA,EAAM;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA,eACN,CACH,EAGA9B,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAChBzB,OAAA;QAAKM,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BP,OAAA;UAAMM,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzClC,OAAA;UAAKM,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BP,OAAA;YAAKM,SAAS,EAAC;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjClC,OAAA;YAAKM,SAAS,EAAC;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjClC,OAAA;YAAKM,SAAS,EAAC;UAAW;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDlC,OAAA;QAAKM,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCP,OAAA;UAAMM,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAoB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/DlC,OAAA;UAAMM,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAE;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GAvEIhC,kBAAkB;AAyExB,eAAeA,kBAAkB;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}