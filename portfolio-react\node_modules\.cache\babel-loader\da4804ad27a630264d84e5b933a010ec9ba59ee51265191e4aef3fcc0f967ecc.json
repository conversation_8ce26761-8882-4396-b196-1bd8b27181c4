{"ast": null, "code": "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\nvar classes = {\n  addClasses,\n  removeClasses\n};\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport { Swiper as S, defaults as d };", "map": {"version": 3, "names": ["a", "getWindow", "g", "getDocument", "b", "elementParents", "q", "elementStyle", "e", "elementChildren", "setCSSProperty", "h", "elementOuterSize", "r", "elementNextAll", "t", "elementPrevAll", "k", "getTranslate", "u", "animateCSSModeScroll", "n", "nextTick", "v", "showWarning", "c", "createElement", "w", "elementIsChildOf", "f", "now", "x", "extend", "i", "elementIndex", "y", "deleteProps", "support", "calcSupport", "window", "document", "smoothScroll", "documentElement", "style", "touch", "DocumentTouch", "getSupport", "deviceCached", "calcDevice", "_temp", "userAgent", "platform", "navigator", "ua", "device", "ios", "android", "screenWidth", "screen", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "indexOf", "os", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "String", "includes", "major", "minor", "split", "map", "num", "Number", "isWebView", "test", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "_ref", "swiper", "on", "emit", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "destroyed", "initialized", "createObserver", "ResizeObserver", "entries", "requestAnimationFrame", "newWidth", "newHeight", "for<PERSON>ach", "_ref2", "contentBoxSize", "contentRect", "target", "el", "inlineSize", "blockSize", "observe", "removeObserver", "cancelAnimationFrame", "unobserve", "orientationChangeHandler", "params", "resizeObserver", "addEventListener", "removeEventListener", "Observer", "extendParams", "observers", "attach", "options", "ObserverFunc", "MutationObserver", "WebkitMutationObserver", "mutations", "__preventObserver__", "length", "observerUpdate", "setTimeout", "attributes", "childList", "isElement", "characterData", "push", "init", "observeParents", "containerParents", "hostEl", "observeSlideChildren", "wrapperEl", "destroy", "disconnect", "splice", "eventsEmitter", "events", "handler", "priority", "self", "eventsListeners", "method", "event", "once", "once<PERSON><PERSON><PERSON>", "off", "__emitterProxy", "_len", "arguments", "args", "Array", "_key", "apply", "onAny", "eventsAnyListeners", "offAny", "index", "<PERSON><PERSON><PERSON><PERSON>", "data", "context", "_len2", "_key2", "isArray", "slice", "unshift", "eventsArray", "updateSize", "clientWidth", "clientHeight", "isHorizontal", "isVertical", "parseInt", "isNaN", "Object", "assign", "size", "updateSlides", "getDirectionPropertyValue", "node", "label", "parseFloat", "getPropertyValue", "getDirectionLabel", "slidesEl", "swiperSize", "rtlTranslate", "rtl", "wrongRTL", "isVirtual", "virtual", "enabled", "previousSlidesLength", "slides", "slideClass", "<PERSON><PERSON><PERSON><PERSON>", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "slidesOffsetBefore", "call", "offsetAfter", "slidesOffsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "replace", "virtualSize", "slideEl", "marginLeft", "marginRight", "marginBottom", "marginTop", "centeredSlides", "cssMode", "gridEnabled", "grid", "rows", "initSlides", "unsetSlides", "slideSize", "shouldResetSlideSize", "<PERSON><PERSON><PERSON><PERSON>iew", "breakpoints", "keys", "filter", "key", "slide", "updateSlide", "slideStyles", "getComputedStyle", "currentTransform", "transform", "currentWebKitTransform", "webkitTransform", "roundLengths", "paddingLeft", "paddingRight", "boxSizing", "offsetWidth", "Math", "floor", "swiperSlideSize", "abs", "slidesPerGroup", "min", "slidesPerGroupSkip", "max", "effect", "setWrapperSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "loop", "groups", "ceil", "slidesBefore", "slidesAfter", "groupSize", "_", "slideIndex", "centeredSlidesBounds", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "centerInsufficientSlides", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "watchOverflow", "checkOverflow", "watchSlidesProgress", "updateSlidesOffset", "backFaceHiddenClass", "containerModifierClass", "hasClassBackfaceClassAdded", "classList", "contains", "maxBackfaceHiddenSlides", "add", "remove", "updateAutoHeight", "speed", "activeSlides", "setTransition", "getSlideByIndex", "getSlideIndexByData", "visibleSlides", "activeIndex", "offsetHeight", "minusOffset", "offsetLeft", "offsetTop", "swiperSlideOffset", "cssOverflowAdjustment", "toggleSlideClasses$1", "condition", "className", "updateSlidesProgress", "translate", "offsetCenter", "visibleSlidesIndexes", "slideOffset", "slideProgress", "minTranslate", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "slideVisibleClass", "slideFullyVisibleClass", "progress", "originalProgress", "updateProgress", "multiplier", "translatesDiff", "maxTranslate", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "autoHeight", "toggleSlideClasses", "updateSlidesClasses", "getFilteredSlide", "selector", "activeSlide", "prevSlide", "nextSlide", "find", "column", "slideActiveClass", "slideNextClass", "slidePrevClass", "emitSlidesClasses", "processLazyPreloader", "imageEl", "slideSelector", "closest", "lazyEl", "querySelector", "lazyPreloaderClass", "shadowRoot", "unlazy", "removeAttribute", "preload", "amount", "lazyPreloadPrevNext", "len", "slidesPerViewDynamic", "activeColumn", "preloadColumns", "from", "slideIndexLastInView", "rewind", "realIndex", "getActiveIndexByTranslate", "normalizeSlideIndex", "updateActiveIndex", "newActiveIndex", "previousIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "getAttribute", "runCallbacksOnInit", "updateClickedSlide", "path", "pathEl", "matches", "slideFound", "clickedSlide", "clickedIndex", "undefined", "slideToClickedSlide", "update", "getSwiperTranslate", "axis", "virtualTranslate", "currentTranslate", "setTranslate", "byController", "z", "previousTranslate", "newProgress", "translateTo", "runCallbacks", "translateBounds", "internal", "animating", "preventInteractionOnTransition", "newTranslate", "isH", "targetPosition", "side", "scrollTo", "behavior", "onTranslateToWrapperTransitionEnd", "transitionEnd", "duration", "transitionDuration", "transitionDelay", "transitionEmit", "direction", "step", "dir", "transitionStart", "transition", "slideTo", "initial", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "allowSlideNext", "allowSlidePrev", "isInitialVirtual", "scrollSnapType", "_immediateVirtual", "_cssModeVirtualInitialSet", "initialSlide", "onSlideToWrapperTransitionEnd", "slideToLoop", "indexAsNumber", "newIndex", "targetSlideIndex", "cols", "needLoopFix", "loopFix", "slideRealIndex", "slideNext", "perGroup", "slidesPerGroupAuto", "increment", "loopPreventsSliding", "_clientLeft", "clientLeft", "slidePrev", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "freeMode", "prevSnap", "prevSnapIndex", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToIndex", "loopedSlides", "getSlideIndex", "loopCreate", "setAttribute", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slideBlankClass", "append", "loopAddBlankSlides", "slidesToAdd", "recalcSlides", "byMousewheel", "loopAdditionalSlides", "fill", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndex", "activeColIndexWithShift", "colIndexToPrepend", "swiperLoopMoveDOM", "prepend", "currentSlideTranslate", "newSlideTranslate", "diff", "touchEventsData", "startTranslate", "shift", "controller", "control", "loopParams", "constructor", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "swiperSlideIndex", "setGrabCursor", "moving", "simulate<PERSON>ouch", "isLocked", "touchEventsTarget", "cursor", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "assignedSlot", "found", "getRootNode", "host", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "innerWidth", "preventDefault", "onTouchStart", "originalEvent", "type", "pointerId", "targetTouches", "touchId", "identifier", "pageX", "touches", "pointerType", "targetEl", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "noSwipingSelector", "isTargetShadow", "noSwiping", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "currentY", "pageY", "startY", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "swipeDirection", "allowThresholdMove", "focusableElements", "nodeName", "activeElement", "blur", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "onTouchMove", "id", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "previousX", "previousY", "diffX", "diffY", "sqrt", "touchAngle", "atan2", "PI", "preventTouchMoveFromPointerMove", "cancelable", "touchMoveStopPropagation", "nested", "stopPropagation", "touchesDiff", "oneWayMovement", "touchRatio", "prevTouchesDirection", "touchesDirection", "isLoop", "allowLoopFix", "evt", "CustomEvent", "bubbles", "detail", "bySwiperTouchMove", "dispatchEvent", "allowMomentumBounce", "loopFixed", "Date", "getTime", "_loopSwapReset", "loopSwapReset", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "onTouchEnd", "isTouchEvent", "proceed", "touchEndTime", "timeDiff", "pathTree", "lastClickTime", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "longSwipesMs", "longSwipes", "longSwipesRatio", "shortSwipes", "isNavButtonTarget", "navigation", "nextEl", "prevEl", "onResize", "setBreakpoint", "isVirtualLoop", "autoplay", "running", "paused", "clearTimeout", "resizeTimeout", "resume", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "scrollLeft", "scrollTop", "onLoad", "onDocumentTouchStart", "documentTouchHandlerProceeded", "touchAction", "capture", "dom<PERSON>ethod", "swiperMethod", "passive", "updateOnWindowResize", "attachEvents", "bind", "detachEvents", "events$1", "isGridEnabled", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpoint<PERSON>nly<PERSON><PERSON><PERSON>", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "emitContainerClasses", "prop", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "changeDirection", "isEnabled", "<PERSON><PERSON><PERSON>", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "value", "sort", "matchMedia", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "classes", "wasLocked", "lastSlideRightEdge", "checkOverflow$1", "defaults", "swiperElementNodeName", "createElements", "eventsPrefix", "url", "uniqueNavElements", "passiveListeners", "wrapperClass", "_emitClasses", "moduleExtendParams", "allModulesParams", "obj", "moduleParamName", "moduleParams", "auto", "prototypes", "extendedDefaults", "Swiper", "prototype", "toString", "querySelectorAll", "swipers", "newParams", "__swiper__", "modules", "__modules__", "mod", "swiperParams", "passedParams", "eventName", "velocity", "trunc", "clickTimeout", "velocities", "imagesToLoad", "imagesLoaded", "property", "setProgress", "current", "cls", "join", "getSlideClasses", "updates", "view", "exact", "spv", "breakLoop", "slideInView", "complete", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "element", "mounted", "parentNode", "toUpperCase", "getWrapperSelector", "trim", "getWrapper", "res", "slideSlots", "lazyElements", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "installModule", "use", "module", "m", "prototypeGroup", "protoMethod", "S", "d"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/shared/swiper-core.mjs"], "sourcesContent": ["import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,WAAW,QAAQ,sBAAsB;AACvE,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,eAAe,EAAET,CAAC,IAAIU,cAAc,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,WAAW,QAAQ,aAAa;AAEtW,IAAIC,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMC,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMuC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,OAAO;IACLsC,YAAY,EAAED,QAAQ,CAACE,eAAe,IAAIF,QAAQ,CAACE,eAAe,CAACC,KAAK,IAAI,gBAAgB,IAAIH,QAAQ,CAACE,eAAe,CAACC,KAAK;IAC9HC,KAAK,EAAE,CAAC,EAAE,cAAc,IAAIL,MAAM,IAAIA,MAAM,CAACM,aAAa,IAAIL,QAAQ,YAAYD,MAAM,CAACM,aAAa;EACxG,CAAC;AACH;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACT,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,CAAC,CAAC;EACzB;EACA,OAAOD,OAAO;AAChB;AAEA,IAAIU,YAAY;AAChB,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI;IACFC;EACF,CAAC,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAMZ,OAAO,GAAGS,UAAU,CAAC,CAAC;EAC5B,MAAMP,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMkD,QAAQ,GAAGZ,MAAM,CAACa,SAAS,CAACD,QAAQ;EAC1C,MAAME,EAAE,GAAGH,SAAS,IAAIX,MAAM,CAACa,SAAS,CAACF,SAAS;EAClD,MAAMI,MAAM,GAAG;IACbC,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC;EACD,MAAMC,WAAW,GAAGlB,MAAM,CAACmB,MAAM,CAACC,KAAK;EACvC,MAAMC,YAAY,GAAGrB,MAAM,CAACmB,MAAM,CAACG,MAAM;EACzC,MAAML,OAAO,GAAGH,EAAE,CAACS,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;EACzD,IAAIC,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,sBAAsB,CAAC;EAC3C,MAAME,IAAI,GAAGX,EAAE,CAACS,KAAK,CAAC,yBAAyB,CAAC;EAChD,MAAMG,MAAM,GAAG,CAACF,IAAI,IAAIV,EAAE,CAACS,KAAK,CAAC,4BAA4B,CAAC;EAC9D,MAAMI,OAAO,GAAGf,QAAQ,KAAK,OAAO;EACpC,IAAIgB,KAAK,GAAGhB,QAAQ,KAAK,UAAU;;EAEnC;EACA,MAAMiB,WAAW,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EACtK,IAAI,CAACL,IAAI,IAAII,KAAK,IAAI9B,OAAO,CAACO,KAAK,IAAIwB,WAAW,CAACC,OAAO,CAAC,GAAGZ,WAAW,IAAIG,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE;IACjGG,IAAI,GAAGV,EAAE,CAACS,KAAK,CAAC,qBAAqB,CAAC;IACtC,IAAI,CAACC,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC;IAClCI,KAAK,GAAG,KAAK;EACf;;EAEA;EACA,IAAIX,OAAO,IAAI,CAACU,OAAO,EAAE;IACvBZ,MAAM,CAACgB,EAAE,GAAG,SAAS;IACrBhB,MAAM,CAACE,OAAO,GAAG,IAAI;EACvB;EACA,IAAIO,IAAI,IAAIE,MAAM,IAAID,IAAI,EAAE;IAC1BV,MAAM,CAACgB,EAAE,GAAG,KAAK;IACjBhB,MAAM,CAACC,GAAG,GAAG,IAAI;EACnB;;EAEA;EACA,OAAOD,MAAM;AACf;AACA,SAASiB,SAASA,CAACC,SAAS,EAAE;EAC5B,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC,CAAC;EAChB;EACA,IAAI,CAACzB,YAAY,EAAE;IACjBA,YAAY,GAAGC,UAAU,CAACwB,SAAS,CAAC;EACtC;EACA,OAAOzB,YAAY;AACrB;AAEA,IAAI0B,OAAO;AACX,SAASC,WAAWA,CAAA,EAAG;EACrB,MAAMnC,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMqD,MAAM,GAAGiB,SAAS,CAAC,CAAC;EAC1B,IAAII,kBAAkB,GAAG,KAAK;EAC9B,SAASC,QAAQA,CAAA,EAAG;IAClB,MAAMvB,EAAE,GAAGd,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC2B,WAAW,CAAC,CAAC;IACnD,OAAOxB,EAAE,CAACgB,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAIhB,EAAE,CAACgB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAIhB,EAAE,CAACgB,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;EAC3F;EACA,IAAIO,QAAQ,CAAC,CAAC,EAAE;IACd,MAAMvB,EAAE,GAAGyB,MAAM,CAACvC,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC;IAC7C,IAAIG,EAAE,CAAC0B,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC3B,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAG5B,EAAE,CAAC6B,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,GAAG,IAAIC,MAAM,CAACD,GAAG,CAAC,CAAC;MAC/FT,kBAAkB,GAAGK,KAAK,GAAG,EAAE,IAAIA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAC;IAC9D;EACF;EACA,MAAMK,SAAS,GAAG,8CAA8C,CAACC,IAAI,CAAChD,MAAM,CAACa,SAAS,CAACF,SAAS,CAAC;EACjG,MAAMsC,eAAe,GAAGZ,QAAQ,CAAC,CAAC;EAClC,MAAMa,SAAS,GAAGD,eAAe,IAAIF,SAAS,IAAIhC,MAAM,CAACC,GAAG;EAC5D,OAAO;IACLqB,QAAQ,EAAED,kBAAkB,IAAIa,eAAe;IAC/Cb,kBAAkB;IAClBc,SAAS;IACTH;EACF,CAAC;AACH;AACA,SAASI,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACjB,OAAO,EAAE;IACZA,OAAO,GAAGC,WAAW,CAAC,CAAC;EACzB;EACA,OAAOD,OAAO;AAChB;AAEA,SAASkB,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI;IACFC,MAAM;IACNC,EAAE;IACFC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMrD,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,IAAI+F,QAAQ,GAAG,IAAI;EACnB,IAAIC,cAAc,GAAG,IAAI;EACzB,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACL,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDL,IAAI,CAAC,cAAc,CAAC;IACpBA,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC;EACD,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACR,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDJ,QAAQ,GAAG,IAAIM,cAAc,CAACC,OAAO,IAAI;MACvCN,cAAc,GAAG1D,MAAM,CAACiE,qBAAqB,CAAC,MAAM;QAClD,MAAM;UACJ7C,KAAK;UACLE;QACF,CAAC,GAAGgC,MAAM;QACV,IAAIY,QAAQ,GAAG9C,KAAK;QACpB,IAAI+C,SAAS,GAAG7C,MAAM;QACtB0C,OAAO,CAACI,OAAO,CAACC,KAAK,IAAI;UACvB,IAAI;YACFC,cAAc;YACdC,WAAW;YACXC;UACF,CAAC,GAAGH,KAAK;UACT,IAAIG,MAAM,IAAIA,MAAM,KAAKlB,MAAM,CAACmB,EAAE,EAAE;UACpCP,QAAQ,GAAGK,WAAW,GAAGA,WAAW,CAACnD,KAAK,GAAG,CAACkD,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEI,UAAU;UAC7FP,SAAS,GAAGI,WAAW,GAAGA,WAAW,CAACjD,MAAM,GAAG,CAACgD,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,EAAEK,SAAS;QAChG,CAAC,CAAC;QACF,IAAIT,QAAQ,KAAK9C,KAAK,IAAI+C,SAAS,KAAK7C,MAAM,EAAE;UAC9CqC,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFF,QAAQ,CAACmB,OAAO,CAACtB,MAAM,CAACmB,EAAE,CAAC;EAC7B,CAAC;EACD,MAAMI,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAInB,cAAc,EAAE;MAClB1D,MAAM,CAAC8E,oBAAoB,CAACpB,cAAc,CAAC;IAC7C;IACA,IAAID,QAAQ,IAAIA,QAAQ,CAACsB,SAAS,IAAIzB,MAAM,CAACmB,EAAE,EAAE;MAC/ChB,QAAQ,CAACsB,SAAS,CAACzB,MAAM,CAACmB,EAAE,CAAC;MAC7BhB,QAAQ,GAAG,IAAI;IACjB;EACF,CAAC;EACD,MAAMuB,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAI,CAAC1B,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAACO,WAAW,EAAE;IACxDL,IAAI,CAAC,mBAAmB,CAAC;EAC3B,CAAC;EACDD,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAID,MAAM,CAAC2B,MAAM,CAACC,cAAc,IAAI,OAAOlF,MAAM,CAAC+D,cAAc,KAAK,WAAW,EAAE;MAChFD,cAAc,CAAC,CAAC;MAChB;IACF;IACA9D,MAAM,CAACmF,gBAAgB,CAAC,QAAQ,EAAExB,aAAa,CAAC;IAChD3D,MAAM,CAACmF,gBAAgB,CAAC,mBAAmB,EAAEH,wBAAwB,CAAC;EACxE,CAAC,CAAC;EACFzB,EAAE,CAAC,SAAS,EAAE,MAAM;IAClBsB,cAAc,CAAC,CAAC;IAChB7E,MAAM,CAACoF,mBAAmB,CAAC,QAAQ,EAAEzB,aAAa,CAAC;IACnD3D,MAAM,CAACoF,mBAAmB,CAAC,mBAAmB,EAAEJ,wBAAwB,CAAC;EAC3E,CAAC,CAAC;AACJ;AAEA,SAASK,QAAQA,CAAChC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNgC,YAAY;IACZ/B,EAAE;IACFC;EACF,CAAC,GAAGH,IAAI;EACR,MAAMkC,SAAS,GAAG,EAAE;EACpB,MAAMvF,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAM8H,MAAM,GAAG,SAAAA,CAAUhB,MAAM,EAAEiB,OAAO,EAAE;IACxC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,MAAMC,YAAY,GAAG1F,MAAM,CAAC2F,gBAAgB,IAAI3F,MAAM,CAAC4F,sBAAsB;IAC7E,MAAMnC,QAAQ,GAAG,IAAIiC,YAAY,CAACG,SAAS,IAAI;MAC7C;MACA;MACA;MACA,IAAIvC,MAAM,CAACwC,mBAAmB,EAAE;MAChC,IAAID,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;QAC1BvC,IAAI,CAAC,gBAAgB,EAAEqC,SAAS,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;MACA,MAAMG,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC/CxC,IAAI,CAAC,gBAAgB,EAAEqC,SAAS,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC;MACD,IAAI7F,MAAM,CAACiE,qBAAqB,EAAE;QAChCjE,MAAM,CAACiE,qBAAqB,CAAC+B,cAAc,CAAC;MAC9C,CAAC,MAAM;QACLhG,MAAM,CAACiG,UAAU,CAACD,cAAc,EAAE,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IACFvC,QAAQ,CAACmB,OAAO,CAACJ,MAAM,EAAE;MACvB0B,UAAU,EAAE,OAAOT,OAAO,CAACS,UAAU,KAAK,WAAW,GAAG,IAAI,GAAGT,OAAO,CAACS,UAAU;MACjFC,SAAS,EAAE7C,MAAM,CAAC8C,SAAS,IAAI,CAAC,OAAOX,OAAO,CAACU,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGV,OAAO,EAAEU,SAAS;MACpGE,aAAa,EAAE,OAAOZ,OAAO,CAACY,aAAa,KAAK,WAAW,GAAG,IAAI,GAAGZ,OAAO,CAACY;IAC/E,CAAC,CAAC;IACFd,SAAS,CAACe,IAAI,CAAC7C,QAAQ,CAAC;EAC1B,CAAC;EACD,MAAM8C,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAI,CAACjD,MAAM,CAAC2B,MAAM,CAACxB,QAAQ,EAAE;IAC7B,IAAIH,MAAM,CAAC2B,MAAM,CAACuB,cAAc,EAAE;MAChC,MAAMC,gBAAgB,GAAG3I,cAAc,CAACwF,MAAM,CAACoD,MAAM,CAAC;MACtD,KAAK,IAAIhH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,gBAAgB,CAACV,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;QACnD8F,MAAM,CAACiB,gBAAgB,CAAC/G,CAAC,CAAC,CAAC;MAC7B;IACF;IACA;IACA8F,MAAM,CAAClC,MAAM,CAACoD,MAAM,EAAE;MACpBP,SAAS,EAAE7C,MAAM,CAAC2B,MAAM,CAAC0B;IAC3B,CAAC,CAAC;;IAEF;IACAnB,MAAM,CAAClC,MAAM,CAACsD,SAAS,EAAE;MACvBV,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;EACD,MAAMW,OAAO,GAAGA,CAAA,KAAM;IACpBtB,SAAS,CAACnB,OAAO,CAACX,QAAQ,IAAI;MAC5BA,QAAQ,CAACqD,UAAU,CAAC,CAAC;IACvB,CAAC,CAAC;IACFvB,SAAS,CAACwB,MAAM,CAAC,CAAC,EAAExB,SAAS,CAACQ,MAAM,CAAC;EACvC,CAAC;EACDT,YAAY,CAAC;IACX7B,QAAQ,EAAE,KAAK;IACf+C,cAAc,EAAE,KAAK;IACrBG,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACFpD,EAAE,CAAC,MAAM,EAAEgD,IAAI,CAAC;EAChBhD,EAAE,CAAC,SAAS,EAAEsD,OAAO,CAAC;AACxB;;AAEA;;AAEA,IAAIG,aAAa,GAAG;EAClBzD,EAAEA,CAAC0D,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC5B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAME,MAAM,GAAGH,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5CF,MAAM,CAACtE,KAAK,CAAC,GAAG,CAAC,CAACyB,OAAO,CAACmD,KAAK,IAAI;MACjC,IAAI,CAACH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAEH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,GAAG,EAAE;MAClEH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACD,MAAM,CAAC,CAACJ,OAAO,CAAC;IAC9C,CAAC,CAAC;IACF,OAAOE,IAAI;EACb,CAAC;EACDI,IAAIA,CAACP,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAC9B,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,SAASK,WAAWA,CAAA,EAAG;MACrBL,IAAI,CAACM,GAAG,CAACT,MAAM,EAAEQ,WAAW,CAAC;MAC7B,IAAIA,WAAW,CAACE,cAAc,EAAE;QAC9B,OAAOF,WAAW,CAACE,cAAc;MACnC;MACA,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAAC9B,MAAM,EAAE+B,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MACAd,OAAO,CAACe,KAAK,CAACb,IAAI,EAAEU,IAAI,CAAC;IAC3B;IACAL,WAAW,CAACE,cAAc,GAAGT,OAAO;IACpC,OAAOE,IAAI,CAAC7D,EAAE,CAAC0D,MAAM,EAAEQ,WAAW,EAAEN,QAAQ,CAAC;EAC/C,CAAC;EACDe,KAAKA,CAAChB,OAAO,EAAEC,QAAQ,EAAE;IACvB,MAAMC,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE,OAAOE,IAAI;IAC9C,MAAME,MAAM,GAAGH,QAAQ,GAAG,SAAS,GAAG,MAAM;IAC5C,IAAIC,IAAI,CAACe,kBAAkB,CAACrG,OAAO,CAACoF,OAAO,CAAC,GAAG,CAAC,EAAE;MAChDE,IAAI,CAACe,kBAAkB,CAACb,MAAM,CAAC,CAACJ,OAAO,CAAC;IAC1C;IACA,OAAOE,IAAI;EACb,CAAC;EACDgB,MAAMA,CAAClB,OAAO,EAAE;IACd,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACe,kBAAkB,EAAE,OAAOf,IAAI;IACzC,MAAMiB,KAAK,GAAGjB,IAAI,CAACe,kBAAkB,CAACrG,OAAO,CAACoF,OAAO,CAAC;IACtD,IAAImB,KAAK,IAAI,CAAC,EAAE;MACdjB,IAAI,CAACe,kBAAkB,CAACpB,MAAM,CAACsB,KAAK,EAAE,CAAC,CAAC;IAC1C;IACA,OAAOjB,IAAI;EACb,CAAC;EACDM,GAAGA,CAACT,MAAM,EAAEC,OAAO,EAAE;IACnB,MAAME,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtCH,MAAM,CAACtE,KAAK,CAAC,GAAG,CAAC,CAACyB,OAAO,CAACmD,KAAK,IAAI;MACjC,IAAI,OAAOL,OAAO,KAAK,WAAW,EAAE;QAClCE,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,GAAG,EAAE;MAClC,CAAC,MAAM,IAAIH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAE;QACtCH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACnD,OAAO,CAAC,CAACkE,YAAY,EAAED,KAAK,KAAK;UAC3D,IAAIC,YAAY,KAAKpB,OAAO,IAAIoB,YAAY,CAACX,cAAc,IAAIW,YAAY,CAACX,cAAc,KAAKT,OAAO,EAAE;YACtGE,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACR,MAAM,CAACsB,KAAK,EAAE,CAAC,CAAC;UAC9C;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOjB,IAAI;EACb,CAAC;EACD5D,IAAIA,CAAA,EAAG;IACL,MAAM4D,IAAI,GAAG,IAAI;IACjB,IAAI,CAACA,IAAI,CAACC,eAAe,IAAID,IAAI,CAACxD,SAAS,EAAE,OAAOwD,IAAI;IACxD,IAAI,CAACA,IAAI,CAACC,eAAe,EAAE,OAAOD,IAAI;IACtC,IAAIH,MAAM;IACV,IAAIsB,IAAI;IACR,IAAIC,OAAO;IACX,KAAK,IAAIC,KAAK,GAAGZ,SAAS,CAAC9B,MAAM,EAAE+B,IAAI,GAAG,IAAIC,KAAK,CAACU,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;MAC7FZ,IAAI,CAACY,KAAK,CAAC,GAAGb,SAAS,CAACa,KAAK,CAAC;IAChC;IACA,IAAI,OAAOZ,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAIC,KAAK,CAACY,OAAO,CAACb,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;MACzDb,MAAM,GAAGa,IAAI,CAAC,CAAC,CAAC;MAChBS,IAAI,GAAGT,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEd,IAAI,CAAC/B,MAAM,CAAC;MACjCyC,OAAO,GAAGpB,IAAI;IAChB,CAAC,MAAM;MACLH,MAAM,GAAGa,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM;MACvBsB,IAAI,GAAGT,IAAI,CAAC,CAAC,CAAC,CAACS,IAAI;MACnBC,OAAO,GAAGV,IAAI,CAAC,CAAC,CAAC,CAACU,OAAO,IAAIpB,IAAI;IACnC;IACAmB,IAAI,CAACM,OAAO,CAACL,OAAO,CAAC;IACrB,MAAMM,WAAW,GAAGf,KAAK,CAACY,OAAO,CAAC1B,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACtE,KAAK,CAAC,GAAG,CAAC;IACtEmG,WAAW,CAAC1E,OAAO,CAACmD,KAAK,IAAI;MAC3B,IAAIH,IAAI,CAACe,kBAAkB,IAAIf,IAAI,CAACe,kBAAkB,CAACpC,MAAM,EAAE;QAC7DqB,IAAI,CAACe,kBAAkB,CAAC/D,OAAO,CAACkE,YAAY,IAAI;UAC9CA,YAAY,CAACL,KAAK,CAACO,OAAO,EAAE,CAACjB,KAAK,EAAE,GAAGgB,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC;MACJ;MACA,IAAInB,IAAI,CAACC,eAAe,IAAID,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,EAAE;QACvDH,IAAI,CAACC,eAAe,CAACE,KAAK,CAAC,CAACnD,OAAO,CAACkE,YAAY,IAAI;UAClDA,YAAY,CAACL,KAAK,CAACO,OAAO,EAAED,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAOnB,IAAI;EACb;AACF,CAAC;AAED,SAAS2B,UAAUA,CAAA,EAAG;EACpB,MAAMzF,MAAM,GAAG,IAAI;EACnB,IAAIlC,KAAK;EACT,IAAIE,MAAM;EACV,MAAMmD,EAAE,GAAGnB,MAAM,CAACmB,EAAE;EACpB,IAAI,OAAOnB,MAAM,CAAC2B,MAAM,CAAC7D,KAAK,KAAK,WAAW,IAAIkC,MAAM,CAAC2B,MAAM,CAAC7D,KAAK,KAAK,IAAI,EAAE;IAC9EA,KAAK,GAAGkC,MAAM,CAAC2B,MAAM,CAAC7D,KAAK;EAC7B,CAAC,MAAM;IACLA,KAAK,GAAGqD,EAAE,CAACuE,WAAW;EACxB;EACA,IAAI,OAAO1F,MAAM,CAAC2B,MAAM,CAAC3D,MAAM,KAAK,WAAW,IAAIgC,MAAM,CAAC2B,MAAM,CAAC3D,MAAM,KAAK,IAAI,EAAE;IAChFA,MAAM,GAAGgC,MAAM,CAAC2B,MAAM,CAAC3D,MAAM;EAC/B,CAAC,MAAM;IACLA,MAAM,GAAGmD,EAAE,CAACwE,YAAY;EAC1B;EACA,IAAI7H,KAAK,KAAK,CAAC,IAAIkC,MAAM,CAAC4F,YAAY,CAAC,CAAC,IAAI5H,MAAM,KAAK,CAAC,IAAIgC,MAAM,CAAC6F,UAAU,CAAC,CAAC,EAAE;IAC/E;EACF;;EAEA;EACA/H,KAAK,GAAGA,KAAK,GAAGgI,QAAQ,CAACpL,YAAY,CAACyG,EAAE,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG2E,QAAQ,CAACpL,YAAY,CAACyG,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC1HnD,MAAM,GAAGA,MAAM,GAAG8H,QAAQ,CAACpL,YAAY,CAACyG,EAAE,EAAE,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG2E,QAAQ,CAACpL,YAAY,CAACyG,EAAE,EAAE,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;EAC5H,IAAI3B,MAAM,CAACuG,KAAK,CAACjI,KAAK,CAAC,EAAEA,KAAK,GAAG,CAAC;EAClC,IAAI0B,MAAM,CAACuG,KAAK,CAAC/H,MAAM,CAAC,EAAEA,MAAM,GAAG,CAAC;EACpCgI,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;IACpBlC,KAAK;IACLE,MAAM;IACNkI,IAAI,EAAElG,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG9H,KAAK,GAAGE;EACxC,CAAC,CAAC;AACJ;AAEA,SAASmI,YAAYA,CAAA,EAAG;EACtB,MAAMnG,MAAM,GAAG,IAAI;EACnB,SAASoG,yBAAyBA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAC9C,OAAOC,UAAU,CAACF,IAAI,CAACG,gBAAgB,CAACxG,MAAM,CAACyG,iBAAiB,CAACH,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;EAChF;EACA,MAAM3E,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAM;IACJ2B,SAAS;IACToD,QAAQ;IACRR,IAAI,EAAES,UAAU;IAChBC,YAAY,EAAEC,GAAG;IACjBC;EACF,CAAC,GAAG9G,MAAM;EACV,MAAM+G,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO;EAC1D,MAAMC,oBAAoB,GAAGH,SAAS,GAAG/G,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAGzC,MAAM,CAACmH,MAAM,CAAC1E,MAAM;EAC5F,MAAM0E,MAAM,GAAGvM,eAAe,CAAC8L,QAAQ,EAAE,IAAI1G,MAAM,CAAC2B,MAAM,CAACyF,UAAU,gBAAgB,CAAC;EACtF,MAAMC,YAAY,GAAGN,SAAS,GAAG/G,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAG0E,MAAM,CAAC1E,MAAM;EAC7E,IAAI6E,QAAQ,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,eAAe,GAAG,EAAE;EAC1B,IAAIC,YAAY,GAAG9F,MAAM,CAAC+F,kBAAkB;EAC5C,IAAI,OAAOD,YAAY,KAAK,UAAU,EAAE;IACtCA,YAAY,GAAG9F,MAAM,CAAC+F,kBAAkB,CAACC,IAAI,CAAC3H,MAAM,CAAC;EACvD;EACA,IAAI4H,WAAW,GAAGjG,MAAM,CAACkG,iBAAiB;EAC1C,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;IACrCA,WAAW,GAAGjG,MAAM,CAACkG,iBAAiB,CAACF,IAAI,CAAC3H,MAAM,CAAC;EACrD;EACA,MAAM8H,sBAAsB,GAAG9H,MAAM,CAACsH,QAAQ,CAAC7E,MAAM;EACrD,MAAMsF,wBAAwB,GAAG/H,MAAM,CAACuH,UAAU,CAAC9E,MAAM;EACzD,IAAIuF,YAAY,GAAGrG,MAAM,CAACqG,YAAY;EACtC,IAAIC,aAAa,GAAG,CAACR,YAAY;EACjC,IAAIS,aAAa,GAAG,CAAC;EACrB,IAAInD,KAAK,GAAG,CAAC;EACb,IAAI,OAAO4B,UAAU,KAAK,WAAW,EAAE;IACrC;EACF;EACA,IAAI,OAAOqB,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACxJ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtEwJ,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGxB,UAAU;EAC7E,CAAC,MAAM,IAAI,OAAOqB,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAAC;EACzC;EACAhI,MAAM,CAACoI,WAAW,GAAG,CAACJ,YAAY;;EAElC;EACAb,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;IACxB,IAAIxB,GAAG,EAAE;MACPwB,OAAO,CAACvL,KAAK,CAACwL,UAAU,GAAG,EAAE;IAC/B,CAAC,MAAM;MACLD,OAAO,CAACvL,KAAK,CAACyL,WAAW,GAAG,EAAE;IAChC;IACAF,OAAO,CAACvL,KAAK,CAAC0L,YAAY,GAAG,EAAE;IAC/BH,OAAO,CAACvL,KAAK,CAAC2L,SAAS,GAAG,EAAE;EAC9B,CAAC,CAAC;;EAEF;EACA,IAAI9G,MAAM,CAAC+G,cAAc,IAAI/G,MAAM,CAACgH,OAAO,EAAE;IAC3C9N,cAAc,CAACyI,SAAS,EAAE,iCAAiC,EAAE,EAAE,CAAC;IAChEzI,cAAc,CAACyI,SAAS,EAAE,gCAAgC,EAAE,EAAE,CAAC;EACjE;EACA,MAAMsF,WAAW,GAAGjH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC,IAAI9I,MAAM,CAAC6I,IAAI;EACtE,IAAID,WAAW,EAAE;IACf5I,MAAM,CAAC6I,IAAI,CAACE,UAAU,CAAC5B,MAAM,CAAC;EAChC,CAAC,MAAM,IAAInH,MAAM,CAAC6I,IAAI,EAAE;IACtB7I,MAAM,CAAC6I,IAAI,CAACG,WAAW,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAIC,SAAS;EACb,MAAMC,oBAAoB,GAAGvH,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAIxH,MAAM,CAACyH,WAAW,IAAIpD,MAAM,CAACqD,IAAI,CAAC1H,MAAM,CAACyH,WAAW,CAAC,CAACE,MAAM,CAACC,GAAG,IAAI;IAClI,OAAO,OAAO5H,MAAM,CAACyH,WAAW,CAACG,GAAG,CAAC,CAACJ,aAAa,KAAK,WAAW;EACrE,CAAC,CAAC,CAAC1G,MAAM,GAAG,CAAC;EACb,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,YAAY,EAAEjL,CAAC,IAAI,CAAC,EAAE;IACxC6M,SAAS,GAAG,CAAC;IACb,IAAIO,KAAK;IACT,IAAIrC,MAAM,CAAC/K,CAAC,CAAC,EAAEoN,KAAK,GAAGrC,MAAM,CAAC/K,CAAC,CAAC;IAChC,IAAIwM,WAAW,EAAE;MACf5I,MAAM,CAAC6I,IAAI,CAACY,WAAW,CAACrN,CAAC,EAAEoN,KAAK,EAAErC,MAAM,CAAC;IAC3C;IACA,IAAIA,MAAM,CAAC/K,CAAC,CAAC,IAAI1B,YAAY,CAAC8O,KAAK,EAAE,SAAS,CAAC,KAAK,MAAM,EAAE,SAAS,CAAC;;IAEtE,IAAI7H,MAAM,CAACwH,aAAa,KAAK,MAAM,EAAE;MACnC,IAAID,oBAAoB,EAAE;QACxB/B,MAAM,CAAC/K,CAAC,CAAC,CAACU,KAAK,CAACkD,MAAM,CAACyG,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE;MACzD;MACA,MAAMiD,WAAW,GAAGC,gBAAgB,CAACH,KAAK,CAAC;MAC3C,MAAMI,gBAAgB,GAAGJ,KAAK,CAAC1M,KAAK,CAAC+M,SAAS;MAC9C,MAAMC,sBAAsB,GAAGN,KAAK,CAAC1M,KAAK,CAACiN,eAAe;MAC1D,IAAIH,gBAAgB,EAAE;QACpBJ,KAAK,CAAC1M,KAAK,CAAC+M,SAAS,GAAG,MAAM;MAChC;MACA,IAAIC,sBAAsB,EAAE;QAC1BN,KAAK,CAAC1M,KAAK,CAACiN,eAAe,GAAG,MAAM;MACtC;MACA,IAAIpI,MAAM,CAACqI,YAAY,EAAE;QACvBf,SAAS,GAAGjJ,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG7K,gBAAgB,CAACyO,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,GAAGzO,gBAAgB,CAACyO,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;MACtH,CAAC,MAAM;QACL;QACA,MAAM1L,KAAK,GAAGsI,yBAAyB,CAACsD,WAAW,EAAE,OAAO,CAAC;QAC7D,MAAMO,WAAW,GAAG7D,yBAAyB,CAACsD,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMQ,YAAY,GAAG9D,yBAAyB,CAACsD,WAAW,EAAE,eAAe,CAAC;QAC5E,MAAMpB,UAAU,GAAGlC,yBAAyB,CAACsD,WAAW,EAAE,aAAa,CAAC;QACxE,MAAMnB,WAAW,GAAGnC,yBAAyB,CAACsD,WAAW,EAAE,cAAc,CAAC;QAC1E,MAAMS,SAAS,GAAGT,WAAW,CAAClD,gBAAgB,CAAC,YAAY,CAAC;QAC5D,IAAI2D,SAAS,IAAIA,SAAS,KAAK,YAAY,EAAE;UAC3ClB,SAAS,GAAGnL,KAAK,GAAGwK,UAAU,GAAGC,WAAW;QAC9C,CAAC,MAAM;UACL,MAAM;YACJ7C,WAAW;YACX0E;UACF,CAAC,GAAGZ,KAAK;UACTP,SAAS,GAAGnL,KAAK,GAAGmM,WAAW,GAAGC,YAAY,GAAG5B,UAAU,GAAGC,WAAW,IAAI6B,WAAW,GAAG1E,WAAW,CAAC;QACzG;MACF;MACA,IAAIkE,gBAAgB,EAAE;QACpBJ,KAAK,CAAC1M,KAAK,CAAC+M,SAAS,GAAGD,gBAAgB;MAC1C;MACA,IAAIE,sBAAsB,EAAE;QAC1BN,KAAK,CAAC1M,KAAK,CAACiN,eAAe,GAAGD,sBAAsB;MACtD;MACA,IAAInI,MAAM,CAACqI,YAAY,EAAEf,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAACrB,SAAS,CAAC;IAC5D,CAAC,MAAM;MACLA,SAAS,GAAG,CAACtC,UAAU,GAAG,CAAChF,MAAM,CAACwH,aAAa,GAAG,CAAC,IAAInB,YAAY,IAAIrG,MAAM,CAACwH,aAAa;MAC3F,IAAIxH,MAAM,CAACqI,YAAY,EAAEf,SAAS,GAAGoB,IAAI,CAACC,KAAK,CAACrB,SAAS,CAAC;MAC1D,IAAI9B,MAAM,CAAC/K,CAAC,CAAC,EAAE;QACb+K,MAAM,CAAC/K,CAAC,CAAC,CAACU,KAAK,CAACkD,MAAM,CAACyG,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAG,GAAGwC,SAAS,IAAI;MACvE;IACF;IACA,IAAI9B,MAAM,CAAC/K,CAAC,CAAC,EAAE;MACb+K,MAAM,CAAC/K,CAAC,CAAC,CAACmO,eAAe,GAAGtB,SAAS;IACvC;IACAzB,eAAe,CAACxE,IAAI,CAACiG,SAAS,CAAC;IAC/B,IAAItH,MAAM,CAAC+G,cAAc,EAAE;MACzBT,aAAa,GAAGA,aAAa,GAAGgB,SAAS,GAAG,CAAC,GAAGf,aAAa,GAAG,CAAC,GAAGF,YAAY;MAChF,IAAIE,aAAa,KAAK,CAAC,IAAI9L,CAAC,KAAK,CAAC,EAAE6L,aAAa,GAAGA,aAAa,GAAGtB,UAAU,GAAG,CAAC,GAAGqB,YAAY;MACjG,IAAI5L,CAAC,KAAK,CAAC,EAAE6L,aAAa,GAAGA,aAAa,GAAGtB,UAAU,GAAG,CAAC,GAAGqB,YAAY;MAC1E,IAAIqC,IAAI,CAACG,GAAG,CAACvC,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,EAAEA,aAAa,GAAG,CAAC;MACzD,IAAItG,MAAM,CAACqI,YAAY,EAAE/B,aAAa,GAAGoC,IAAI,CAACC,KAAK,CAACrC,aAAa,CAAC;MAClE,IAAIlD,KAAK,GAAGpD,MAAM,CAAC8I,cAAc,KAAK,CAAC,EAAEnD,QAAQ,CAACtE,IAAI,CAACiF,aAAa,CAAC;MACrEV,UAAU,CAACvE,IAAI,CAACiF,aAAa,CAAC;IAChC,CAAC,MAAM;MACL,IAAItG,MAAM,CAACqI,YAAY,EAAE/B,aAAa,GAAGoC,IAAI,CAACC,KAAK,CAACrC,aAAa,CAAC;MAClE,IAAI,CAAClD,KAAK,GAAGsF,IAAI,CAACK,GAAG,CAAC1K,MAAM,CAAC2B,MAAM,CAACgJ,kBAAkB,EAAE5F,KAAK,CAAC,IAAI/E,MAAM,CAAC2B,MAAM,CAAC8I,cAAc,KAAK,CAAC,EAAEnD,QAAQ,CAACtE,IAAI,CAACiF,aAAa,CAAC;MAClIV,UAAU,CAACvE,IAAI,CAACiF,aAAa,CAAC;MAC9BA,aAAa,GAAGA,aAAa,GAAGgB,SAAS,GAAGjB,YAAY;IAC1D;IACAhI,MAAM,CAACoI,WAAW,IAAIa,SAAS,GAAGjB,YAAY;IAC9CE,aAAa,GAAGe,SAAS;IACzBlE,KAAK,IAAI,CAAC;EACZ;EACA/E,MAAM,CAACoI,WAAW,GAAGiC,IAAI,CAACO,GAAG,CAAC5K,MAAM,CAACoI,WAAW,EAAEzB,UAAU,CAAC,GAAGiB,WAAW;EAC3E,IAAIf,GAAG,IAAIC,QAAQ,KAAKnF,MAAM,CAACkJ,MAAM,KAAK,OAAO,IAAIlJ,MAAM,CAACkJ,MAAM,KAAK,WAAW,CAAC,EAAE;IACnFvH,SAAS,CAACxG,KAAK,CAACgB,KAAK,GAAG,GAAGkC,MAAM,CAACoI,WAAW,GAAGJ,YAAY,IAAI;EAClE;EACA,IAAIrG,MAAM,CAACmJ,cAAc,EAAE;IACzBxH,SAAS,CAACxG,KAAK,CAACkD,MAAM,CAACyG,iBAAiB,CAAC,OAAO,CAAC,CAAC,GAAG,GAAGzG,MAAM,CAACoI,WAAW,GAAGJ,YAAY,IAAI;EAC/F;EACA,IAAIY,WAAW,EAAE;IACf5I,MAAM,CAAC6I,IAAI,CAACkC,iBAAiB,CAAC9B,SAAS,EAAE3B,QAAQ,CAAC;EACpD;;EAEA;EACA,IAAI,CAAC3F,MAAM,CAAC+G,cAAc,EAAE;IAC1B,MAAMsC,aAAa,GAAG,EAAE;IACxB,KAAK,IAAI5O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkL,QAAQ,CAAC7E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;MAC3C,IAAI6O,cAAc,GAAG3D,QAAQ,CAAClL,CAAC,CAAC;MAChC,IAAIuF,MAAM,CAACqI,YAAY,EAAEiB,cAAc,GAAGZ,IAAI,CAACC,KAAK,CAACW,cAAc,CAAC;MACpE,IAAI3D,QAAQ,CAAClL,CAAC,CAAC,IAAI4D,MAAM,CAACoI,WAAW,GAAGzB,UAAU,EAAE;QAClDqE,aAAa,CAAChI,IAAI,CAACiI,cAAc,CAAC;MACpC;IACF;IACA3D,QAAQ,GAAG0D,aAAa;IACxB,IAAIX,IAAI,CAACC,KAAK,CAACtK,MAAM,CAACoI,WAAW,GAAGzB,UAAU,CAAC,GAAG0D,IAAI,CAACC,KAAK,CAAChD,QAAQ,CAACA,QAAQ,CAAC7E,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC/F6E,QAAQ,CAACtE,IAAI,CAAChD,MAAM,CAACoI,WAAW,GAAGzB,UAAU,CAAC;IAChD;EACF;EACA,IAAII,SAAS,IAAIpF,MAAM,CAACuJ,IAAI,EAAE;IAC5B,MAAMhF,IAAI,GAAGsB,eAAe,CAAC,CAAC,CAAC,GAAGQ,YAAY;IAC9C,IAAIrG,MAAM,CAAC8I,cAAc,GAAG,CAAC,EAAE;MAC7B,MAAMU,MAAM,GAAGd,IAAI,CAACe,IAAI,CAAC,CAACpL,MAAM,CAACgH,OAAO,CAACqE,YAAY,GAAGrL,MAAM,CAACgH,OAAO,CAACsE,WAAW,IAAI3J,MAAM,CAAC8I,cAAc,CAAC;MAC5G,MAAMc,SAAS,GAAGrF,IAAI,GAAGvE,MAAM,CAAC8I,cAAc;MAC9C,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+O,MAAM,EAAE/O,CAAC,IAAI,CAAC,EAAE;QAClCkL,QAAQ,CAACtE,IAAI,CAACsE,QAAQ,CAACA,QAAQ,CAAC7E,MAAM,GAAG,CAAC,CAAC,GAAG8I,SAAS,CAAC;MAC1D;IACF;IACA,KAAK,IAAInP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,MAAM,CAACgH,OAAO,CAACqE,YAAY,GAAGrL,MAAM,CAACgH,OAAO,CAACsE,WAAW,EAAElP,CAAC,IAAI,CAAC,EAAE;MACpF,IAAIuF,MAAM,CAAC8I,cAAc,KAAK,CAAC,EAAE;QAC/BnD,QAAQ,CAACtE,IAAI,CAACsE,QAAQ,CAACA,QAAQ,CAAC7E,MAAM,GAAG,CAAC,CAAC,GAAGyD,IAAI,CAAC;MACrD;MACAqB,UAAU,CAACvE,IAAI,CAACuE,UAAU,CAACA,UAAU,CAAC9E,MAAM,GAAG,CAAC,CAAC,GAAGyD,IAAI,CAAC;MACzDlG,MAAM,CAACoI,WAAW,IAAIlC,IAAI;IAC5B;EACF;EACA,IAAIoB,QAAQ,CAAC7E,MAAM,KAAK,CAAC,EAAE6E,QAAQ,GAAG,CAAC,CAAC,CAAC;EACzC,IAAIU,YAAY,KAAK,CAAC,EAAE;IACtB,MAAMuB,GAAG,GAAGvJ,MAAM,CAAC4F,YAAY,CAAC,CAAC,IAAIiB,GAAG,GAAG,YAAY,GAAG7G,MAAM,CAACyG,iBAAiB,CAAC,aAAa,CAAC;IACjGU,MAAM,CAACmC,MAAM,CAAC,CAACkC,CAAC,EAAEC,UAAU,KAAK;MAC/B,IAAI,CAAC9J,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAACuJ,IAAI,EAAE,OAAO,IAAI;MAC/C,IAAIO,UAAU,KAAKtE,MAAM,CAAC1E,MAAM,GAAG,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;MACA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC3B,OAAO,CAACuH,OAAO,IAAI;MACpBA,OAAO,CAACvL,KAAK,CAACyM,GAAG,CAAC,GAAG,GAAGvB,YAAY,IAAI;IAC1C,CAAC,CAAC;EACJ;EACA,IAAIrG,MAAM,CAAC+G,cAAc,IAAI/G,MAAM,CAAC+J,oBAAoB,EAAE;IACxD,IAAIC,aAAa,GAAG,CAAC;IACrBnE,eAAe,CAAC1G,OAAO,CAAC8K,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAI5D,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACF2D,aAAa,IAAI3D,YAAY;IAC7B,MAAM6D,OAAO,GAAGF,aAAa,GAAGhF,UAAU,GAAGgF,aAAa,GAAGhF,UAAU,GAAG,CAAC;IAC3EW,QAAQ,GAAGA,QAAQ,CAAChI,GAAG,CAACwM,IAAI,IAAI;MAC9B,IAAIA,IAAI,IAAI,CAAC,EAAE,OAAO,CAACrE,YAAY;MACnC,IAAIqE,IAAI,GAAGD,OAAO,EAAE,OAAOA,OAAO,GAAGjE,WAAW;MAChD,OAAOkE,IAAI;IACb,CAAC,CAAC;EACJ;EACA,IAAInK,MAAM,CAACoK,wBAAwB,EAAE;IACnC,IAAIJ,aAAa,GAAG,CAAC;IACrBnE,eAAe,CAAC1G,OAAO,CAAC8K,cAAc,IAAI;MACxCD,aAAa,IAAIC,cAAc,IAAI5D,YAAY,IAAI,CAAC,CAAC;IACvD,CAAC,CAAC;IACF2D,aAAa,IAAI3D,YAAY;IAC7B,MAAMgE,UAAU,GAAG,CAACrK,MAAM,CAAC+F,kBAAkB,IAAI,CAAC,KAAK/F,MAAM,CAACkG,iBAAiB,IAAI,CAAC,CAAC;IACrF,IAAI8D,aAAa,GAAGK,UAAU,GAAGrF,UAAU,EAAE;MAC3C,MAAMsF,eAAe,GAAG,CAACtF,UAAU,GAAGgF,aAAa,GAAGK,UAAU,IAAI,CAAC;MACrE1E,QAAQ,CAACxG,OAAO,CAAC,CAACgL,IAAI,EAAEI,SAAS,KAAK;QACpC5E,QAAQ,CAAC4E,SAAS,CAAC,GAAGJ,IAAI,GAAGG,eAAe;MAC9C,CAAC,CAAC;MACF1E,UAAU,CAACzG,OAAO,CAAC,CAACgL,IAAI,EAAEI,SAAS,KAAK;QACtC3E,UAAU,CAAC2E,SAAS,CAAC,GAAGJ,IAAI,GAAGG,eAAe;MAChD,CAAC,CAAC;IACJ;EACF;EACAjG,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;IACpBmH,MAAM;IACNG,QAAQ;IACRC,UAAU;IACVC;EACF,CAAC,CAAC;EACF,IAAI7F,MAAM,CAAC+G,cAAc,IAAI/G,MAAM,CAACgH,OAAO,IAAI,CAAChH,MAAM,CAAC+J,oBAAoB,EAAE;IAC3E7Q,cAAc,CAACyI,SAAS,EAAE,iCAAiC,EAAE,GAAG,CAACgE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IACjFzM,cAAc,CAACyI,SAAS,EAAE,gCAAgC,EAAE,GAAGtD,MAAM,CAACkG,IAAI,GAAG,CAAC,GAAGsB,eAAe,CAACA,eAAe,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;IACrI,MAAM0J,aAAa,GAAG,CAACnM,MAAM,CAACsH,QAAQ,CAAC,CAAC,CAAC;IACzC,MAAM8E,eAAe,GAAG,CAACpM,MAAM,CAACuH,UAAU,CAAC,CAAC,CAAC;IAC7CvH,MAAM,CAACsH,QAAQ,GAAGtH,MAAM,CAACsH,QAAQ,CAAChI,GAAG,CAAC5D,CAAC,IAAIA,CAAC,GAAGyQ,aAAa,CAAC;IAC7DnM,MAAM,CAACuH,UAAU,GAAGvH,MAAM,CAACuH,UAAU,CAACjI,GAAG,CAAC5D,CAAC,IAAIA,CAAC,GAAG0Q,eAAe,CAAC;EACrE;EACA,IAAI/E,YAAY,KAAKH,oBAAoB,EAAE;IACzClH,MAAM,CAACE,IAAI,CAAC,oBAAoB,CAAC;EACnC;EACA,IAAIoH,QAAQ,CAAC7E,MAAM,KAAKqF,sBAAsB,EAAE;IAC9C,IAAI9H,MAAM,CAAC2B,MAAM,CAAC0K,aAAa,EAAErM,MAAM,CAACsM,aAAa,CAAC,CAAC;IACvDtM,MAAM,CAACE,IAAI,CAAC,sBAAsB,CAAC;EACrC;EACA,IAAIqH,UAAU,CAAC9E,MAAM,KAAKsF,wBAAwB,EAAE;IAClD/H,MAAM,CAACE,IAAI,CAAC,wBAAwB,CAAC;EACvC;EACA,IAAIyB,MAAM,CAAC4K,mBAAmB,EAAE;IAC9BvM,MAAM,CAACwM,kBAAkB,CAAC,CAAC;EAC7B;EACAxM,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;EAC5B,IAAI,CAAC6G,SAAS,IAAI,CAACpF,MAAM,CAACgH,OAAO,KAAKhH,MAAM,CAACkJ,MAAM,KAAK,OAAO,IAAIlJ,MAAM,CAACkJ,MAAM,KAAK,MAAM,CAAC,EAAE;IAC5F,MAAM4B,mBAAmB,GAAG,GAAG9K,MAAM,CAAC+K,sBAAsB,iBAAiB;IAC7E,MAAMC,0BAA0B,GAAG3M,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACC,QAAQ,CAACJ,mBAAmB,CAAC;IACpF,IAAIpF,YAAY,IAAI1F,MAAM,CAACmL,uBAAuB,EAAE;MAClD,IAAI,CAACH,0BAA0B,EAAE3M,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACG,GAAG,CAACN,mBAAmB,CAAC;IAC/E,CAAC,MAAM,IAAIE,0BAA0B,EAAE;MACrC3M,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACI,MAAM,CAACP,mBAAmB,CAAC;IACjD;EACF;AACF;AAEA,SAASQ,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMlN,MAAM,GAAG,IAAI;EACnB,MAAMmN,YAAY,GAAG,EAAE;EACvB,MAAMpG,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO;EACjE,IAAIpG,SAAS,GAAG,CAAC;EACjB,IAAIzE,CAAC;EACL,IAAI,OAAO8Q,KAAK,KAAK,QAAQ,EAAE;IAC7BlN,MAAM,CAACoN,aAAa,CAACF,KAAK,CAAC;EAC7B,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,EAAE;IACzBlN,MAAM,CAACoN,aAAa,CAACpN,MAAM,CAAC2B,MAAM,CAACuL,KAAK,CAAC;EAC3C;EACA,MAAMG,eAAe,GAAGtI,KAAK,IAAI;IAC/B,IAAIgC,SAAS,EAAE;MACb,OAAO/G,MAAM,CAACmH,MAAM,CAACnH,MAAM,CAACsN,mBAAmB,CAACvI,KAAK,CAAC,CAAC;IACzD;IACA,OAAO/E,MAAM,CAACmH,MAAM,CAACpC,KAAK,CAAC;EAC7B,CAAC;EACD;EACA,IAAI/E,MAAM,CAAC2B,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAInJ,MAAM,CAAC2B,MAAM,CAACwH,aAAa,GAAG,CAAC,EAAE;IAC7E,IAAInJ,MAAM,CAAC2B,MAAM,CAAC+G,cAAc,EAAE;MAChC,CAAC1I,MAAM,CAACuN,aAAa,IAAI,EAAE,EAAEzM,OAAO,CAAC0I,KAAK,IAAI;QAC5C2D,YAAY,CAACnK,IAAI,CAACwG,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,KAAKpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiO,IAAI,CAACe,IAAI,CAACpL,MAAM,CAAC2B,MAAM,CAACwH,aAAa,CAAC,EAAE/M,CAAC,IAAI,CAAC,EAAE;QAC9D,MAAM2I,KAAK,GAAG/E,MAAM,CAACwN,WAAW,GAAGpR,CAAC;QACpC,IAAI2I,KAAK,GAAG/E,MAAM,CAACmH,MAAM,CAAC1E,MAAM,IAAI,CAACsE,SAAS,EAAE;QAChDoG,YAAY,CAACnK,IAAI,CAACqK,eAAe,CAACtI,KAAK,CAAC,CAAC;MAC3C;IACF;EACF,CAAC,MAAM;IACLoI,YAAY,CAACnK,IAAI,CAACqK,eAAe,CAACrN,MAAM,CAACwN,WAAW,CAAC,CAAC;EACxD;;EAEA;EACA,KAAKpR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+Q,YAAY,CAAC1K,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;IAC3C,IAAI,OAAO+Q,YAAY,CAAC/Q,CAAC,CAAC,KAAK,WAAW,EAAE;MAC1C,MAAM4B,MAAM,GAAGmP,YAAY,CAAC/Q,CAAC,CAAC,CAACqR,YAAY;MAC3C5M,SAAS,GAAG7C,MAAM,GAAG6C,SAAS,GAAG7C,MAAM,GAAG6C,SAAS;IACrD;EACF;;EAEA;EACA,IAAIA,SAAS,IAAIA,SAAS,KAAK,CAAC,EAAEb,MAAM,CAACsD,SAAS,CAACxG,KAAK,CAACkB,MAAM,GAAG,GAAG6C,SAAS,IAAI;AACpF;AAEA,SAAS2L,kBAAkBA,CAAA,EAAG;EAC5B,MAAMxM,MAAM,GAAG,IAAI;EACnB,MAAMmH,MAAM,GAAGnH,MAAM,CAACmH,MAAM;EAC5B;EACA,MAAMuG,WAAW,GAAG1N,MAAM,CAAC8C,SAAS,GAAG9C,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG5F,MAAM,CAACsD,SAAS,CAACqK,UAAU,GAAG3N,MAAM,CAACsD,SAAS,CAACsK,SAAS,GAAG,CAAC;EAC3H,KAAK,IAAIxR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+K,MAAM,CAAC1E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;IACzC+K,MAAM,CAAC/K,CAAC,CAAC,CAACyR,iBAAiB,GAAG,CAAC7N,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAGuB,MAAM,CAAC/K,CAAC,CAAC,CAACuR,UAAU,GAAGxG,MAAM,CAAC/K,CAAC,CAAC,CAACwR,SAAS,IAAIF,WAAW,GAAG1N,MAAM,CAAC8N,qBAAqB,CAAC,CAAC;EACnJ;AACF;AAEA,MAAMC,oBAAoB,GAAGA,CAAC1F,OAAO,EAAE2F,SAAS,EAAEC,SAAS,KAAK;EAC9D,IAAID,SAAS,IAAI,CAAC3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IACvD5F,OAAO,CAACuE,SAAS,CAACG,GAAG,CAACkB,SAAS,CAAC;EAClC,CAAC,MAAM,IAAI,CAACD,SAAS,IAAI3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IAC9D5F,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACiB,SAAS,CAAC;EACrC;AACF,CAAC;AACD,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EACvC,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,IAAI,IAAI,IAAI,CAACA,SAAS,IAAI,CAAC;EACzC;EACA,MAAMnO,MAAM,GAAG,IAAI;EACnB,MAAM2B,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAM;IACJwF,MAAM;IACNP,YAAY,EAAEC,GAAG;IACjBS;EACF,CAAC,GAAGtH,MAAM;EACV,IAAImH,MAAM,CAAC1E,MAAM,KAAK,CAAC,EAAE;EACzB,IAAI,OAAO0E,MAAM,CAAC,CAAC,CAAC,CAAC0G,iBAAiB,KAAK,WAAW,EAAE7N,MAAM,CAACwM,kBAAkB,CAAC,CAAC;EACnF,IAAI4B,YAAY,GAAG,CAACD,SAAS;EAC7B,IAAItH,GAAG,EAAEuH,YAAY,GAAGD,SAAS;EACjCnO,MAAM,CAACqO,oBAAoB,GAAG,EAAE;EAChCrO,MAAM,CAACuN,aAAa,GAAG,EAAE;EACzB,IAAIvF,YAAY,GAAGrG,MAAM,CAACqG,YAAY;EACtC,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,CAACxJ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;IACtEwJ,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAACG,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGnI,MAAM,CAACkG,IAAI;EAC9E,CAAC,MAAM,IAAI,OAAO8B,YAAY,KAAK,QAAQ,EAAE;IAC3CA,YAAY,GAAGzB,UAAU,CAACyB,YAAY,CAAC;EACzC;EACA,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+K,MAAM,CAAC1E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;IACzC,MAAMoN,KAAK,GAAGrC,MAAM,CAAC/K,CAAC,CAAC;IACvB,IAAIkS,WAAW,GAAG9E,KAAK,CAACqE,iBAAiB;IACzC,IAAIlM,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC+G,cAAc,EAAE;MAC3C4F,WAAW,IAAInH,MAAM,CAAC,CAAC,CAAC,CAAC0G,iBAAiB;IAC5C;IACA,MAAMU,aAAa,GAAG,CAACH,YAAY,IAAIzM,MAAM,CAAC+G,cAAc,GAAG1I,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGF,WAAW,KAAK9E,KAAK,CAACe,eAAe,GAAGvC,YAAY,CAAC;IACjJ,MAAMyG,qBAAqB,GAAG,CAACL,YAAY,GAAG9G,QAAQ,CAAC,CAAC,CAAC,IAAI3F,MAAM,CAAC+G,cAAc,GAAG1I,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGF,WAAW,KAAK9E,KAAK,CAACe,eAAe,GAAGvC,YAAY,CAAC;IACvK,MAAM0G,WAAW,GAAG,EAAEN,YAAY,GAAGE,WAAW,CAAC;IACjD,MAAMK,UAAU,GAAGD,WAAW,GAAG1O,MAAM,CAACwH,eAAe,CAACpL,CAAC,CAAC;IAC1D,MAAMwS,cAAc,GAAGF,WAAW,IAAI,CAAC,IAAIA,WAAW,IAAI1O,MAAM,CAACkG,IAAI,GAAGlG,MAAM,CAACwH,eAAe,CAACpL,CAAC,CAAC;IACjG,MAAMyS,SAAS,GAAGH,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAG1O,MAAM,CAACkG,IAAI,GAAG,CAAC,IAAIyI,UAAU,GAAG,CAAC,IAAIA,UAAU,IAAI3O,MAAM,CAACkG,IAAI,IAAIwI,WAAW,IAAI,CAAC,IAAIC,UAAU,IAAI3O,MAAM,CAACkG,IAAI;IACnK,IAAI2I,SAAS,EAAE;MACb7O,MAAM,CAACuN,aAAa,CAACvK,IAAI,CAACwG,KAAK,CAAC;MAChCxJ,MAAM,CAACqO,oBAAoB,CAACrL,IAAI,CAAC5G,CAAC,CAAC;IACrC;IACA2R,oBAAoB,CAACvE,KAAK,EAAEqF,SAAS,EAAElN,MAAM,CAACmN,iBAAiB,CAAC;IAChEf,oBAAoB,CAACvE,KAAK,EAAEoF,cAAc,EAAEjN,MAAM,CAACoN,sBAAsB,CAAC;IAC1EvF,KAAK,CAACwF,QAAQ,GAAGnI,GAAG,GAAG,CAAC0H,aAAa,GAAGA,aAAa;IACrD/E,KAAK,CAACyF,gBAAgB,GAAGpI,GAAG,GAAG,CAAC4H,qBAAqB,GAAGA,qBAAqB;EAC/E;AACF;AAEA,SAASS,cAAcA,CAACf,SAAS,EAAE;EACjC,MAAMnO,MAAM,GAAG,IAAI;EACnB,IAAI,OAAOmO,SAAS,KAAK,WAAW,EAAE;IACpC,MAAMgB,UAAU,GAAGnP,MAAM,CAAC4G,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C;IACAuH,SAAS,GAAGnO,MAAM,IAAIA,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACmO,SAAS,GAAGgB,UAAU,IAAI,CAAC;EAC9E;EACA,MAAMxN,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,MAAMyN,cAAc,GAAGpP,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAGrP,MAAM,CAACwO,YAAY,CAAC,CAAC;EACpE,IAAI;IACFQ,QAAQ;IACRM,WAAW;IACXC,KAAK;IACLC;EACF,CAAC,GAAGxP,MAAM;EACV,MAAMyP,YAAY,GAAGH,WAAW;EAChC,MAAMI,MAAM,GAAGH,KAAK;EACpB,IAAIH,cAAc,KAAK,CAAC,EAAE;IACxBJ,QAAQ,GAAG,CAAC;IACZM,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAG,IAAI;EACd,CAAC,MAAM;IACLP,QAAQ,GAAG,CAACb,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,IAAIY,cAAc;IAC/D,MAAMO,kBAAkB,GAAGtF,IAAI,CAACG,GAAG,CAAC2D,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1E,MAAMoB,YAAY,GAAGvF,IAAI,CAACG,GAAG,CAAC2D,SAAS,GAAGnO,MAAM,CAACqP,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IACpEC,WAAW,GAAGK,kBAAkB,IAAIX,QAAQ,IAAI,CAAC;IACjDO,KAAK,GAAGK,YAAY,IAAIZ,QAAQ,IAAI,CAAC;IACrC,IAAIW,kBAAkB,EAAEX,QAAQ,GAAG,CAAC;IACpC,IAAIY,YAAY,EAAEZ,QAAQ,GAAG,CAAC;EAChC;EACA,IAAIrN,MAAM,CAACuJ,IAAI,EAAE;IACf,MAAM2E,eAAe,GAAG7P,MAAM,CAACsN,mBAAmB,CAAC,CAAC,CAAC;IACrD,MAAMwC,cAAc,GAAG9P,MAAM,CAACsN,mBAAmB,CAACtN,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAAC;IAC3E,MAAMsN,mBAAmB,GAAG/P,MAAM,CAACuH,UAAU,CAACsI,eAAe,CAAC;IAC9D,MAAMG,kBAAkB,GAAGhQ,MAAM,CAACuH,UAAU,CAACuI,cAAc,CAAC;IAC5D,MAAMG,YAAY,GAAGjQ,MAAM,CAACuH,UAAU,CAACvH,MAAM,CAACuH,UAAU,CAAC9E,MAAM,GAAG,CAAC,CAAC;IACpE,MAAMyN,YAAY,GAAG7F,IAAI,CAACG,GAAG,CAAC2D,SAAS,CAAC;IACxC,IAAI+B,YAAY,IAAIH,mBAAmB,EAAE;MACvCP,YAAY,GAAG,CAACU,YAAY,GAAGH,mBAAmB,IAAIE,YAAY;IACpE,CAAC,MAAM;MACLT,YAAY,GAAG,CAACU,YAAY,GAAGD,YAAY,GAAGD,kBAAkB,IAAIC,YAAY;IAClF;IACA,IAAIT,YAAY,GAAG,CAAC,EAAEA,YAAY,IAAI,CAAC;EACzC;EACAxJ,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;IACpBgP,QAAQ;IACRQ,YAAY;IACZF,WAAW;IACXC;EACF,CAAC,CAAC;EACF,IAAI5N,MAAM,CAAC4K,mBAAmB,IAAI5K,MAAM,CAAC+G,cAAc,IAAI/G,MAAM,CAACwO,UAAU,EAAEnQ,MAAM,CAACkO,oBAAoB,CAACC,SAAS,CAAC;EACpH,IAAImB,WAAW,IAAI,CAACG,YAAY,EAAE;IAChCzP,MAAM,CAACE,IAAI,CAAC,uBAAuB,CAAC;EACtC;EACA,IAAIqP,KAAK,IAAI,CAACG,MAAM,EAAE;IACpB1P,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;EAChC;EACA,IAAIuP,YAAY,IAAI,CAACH,WAAW,IAAII,MAAM,IAAI,CAACH,KAAK,EAAE;IACpDvP,MAAM,CAACE,IAAI,CAAC,UAAU,CAAC;EACzB;EACAF,MAAM,CAACE,IAAI,CAAC,UAAU,EAAE8O,QAAQ,CAAC;AACnC;AAEA,MAAMoB,kBAAkB,GAAGA,CAAC/H,OAAO,EAAE2F,SAAS,EAAEC,SAAS,KAAK;EAC5D,IAAID,SAAS,IAAI,CAAC3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IACvD5F,OAAO,CAACuE,SAAS,CAACG,GAAG,CAACkB,SAAS,CAAC;EAClC,CAAC,MAAM,IAAI,CAACD,SAAS,IAAI3F,OAAO,CAACuE,SAAS,CAACC,QAAQ,CAACoB,SAAS,CAAC,EAAE;IAC9D5F,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACiB,SAAS,CAAC;EACrC;AACF,CAAC;AACD,SAASoC,mBAAmBA,CAAA,EAAG;EAC7B,MAAMrQ,MAAM,GAAG,IAAI;EACnB,MAAM;IACJmH,MAAM;IACNxF,MAAM;IACN+E,QAAQ;IACR8G;EACF,CAAC,GAAGxN,MAAM;EACV,MAAM+G,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO;EAC1D,MAAM2B,WAAW,GAAG5I,MAAM,CAAC6I,IAAI,IAAIlH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,MAAMwH,gBAAgB,GAAGC,QAAQ,IAAI;IACnC,OAAO3V,eAAe,CAAC8L,QAAQ,EAAE,IAAI/E,MAAM,CAACyF,UAAU,GAAGmJ,QAAQ,iBAAiBA,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC;EACD,IAAIC,WAAW;EACf,IAAIC,SAAS;EACb,IAAIC,SAAS;EACb,IAAI3J,SAAS,EAAE;IACb,IAAIpF,MAAM,CAACuJ,IAAI,EAAE;MACf,IAAIO,UAAU,GAAG+B,WAAW,GAAGxN,MAAM,CAACgH,OAAO,CAACqE,YAAY;MAC1D,IAAII,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGzL,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAGgJ,UAAU;MAC1E,IAAIA,UAAU,IAAIzL,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,EAAEgJ,UAAU,IAAIzL,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM;MAC1F+N,WAAW,GAAGF,gBAAgB,CAAC,6BAA6B7E,UAAU,IAAI,CAAC;IAC7E,CAAC,MAAM;MACL+E,WAAW,GAAGF,gBAAgB,CAAC,6BAA6B9C,WAAW,IAAI,CAAC;IAC9E;EACF,CAAC,MAAM;IACL,IAAI5E,WAAW,EAAE;MACf4H,WAAW,GAAGrJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,CAAC;MACpEkD,SAAS,GAAGvJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,GAAG,CAAC,CAAC;MACtEiD,SAAS,GAAGtJ,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,GAAG,CAAC,CAAC;IACxE,CAAC,MAAM;MACLgD,WAAW,GAAGrJ,MAAM,CAACqG,WAAW,CAAC;IACnC;EACF;EACA,IAAIgD,WAAW,EAAE;IACf,IAAI,CAAC5H,WAAW,EAAE;MAChB;MACA8H,SAAS,GAAGzV,cAAc,CAACuV,WAAW,EAAE,IAAI7O,MAAM,CAACyF,UAAU,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACjF,IAAIzF,MAAM,CAACuJ,IAAI,IAAI,CAACwF,SAAS,EAAE;QAC7BA,SAAS,GAAGvJ,MAAM,CAAC,CAAC,CAAC;MACvB;;MAEA;MACAsJ,SAAS,GAAGtV,cAAc,CAACqV,WAAW,EAAE,IAAI7O,MAAM,CAACyF,UAAU,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACjF,IAAIzF,MAAM,CAACuJ,IAAI,IAAI,CAACuF,SAAS,KAAK,CAAC,EAAE;QACnCA,SAAS,GAAGtJ,MAAM,CAACA,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAAC;MACvC;IACF;EACF;EACA0E,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;IACxB+H,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKmI,WAAW,EAAE7O,MAAM,CAACkP,gBAAgB,CAAC;IAC7ET,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKqI,SAAS,EAAE/O,MAAM,CAACmP,cAAc,CAAC;IACzEV,kBAAkB,CAAC/H,OAAO,EAAEA,OAAO,KAAKoI,SAAS,EAAE9O,MAAM,CAACoP,cAAc,CAAC;EAC3E,CAAC,CAAC;EACF/Q,MAAM,CAACgR,iBAAiB,CAAC,CAAC;AAC5B;AAEA,MAAMC,oBAAoB,GAAGA,CAACjR,MAAM,EAAEkR,OAAO,KAAK;EAChD,IAAI,CAAClR,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAAC2B,MAAM,EAAE;EACnD,MAAMwP,aAAa,GAAGA,CAAA,KAAMnR,MAAM,CAAC8C,SAAS,GAAG,cAAc,GAAG,IAAI9C,MAAM,CAAC2B,MAAM,CAACyF,UAAU,EAAE;EAC9F,MAAMiB,OAAO,GAAG6I,OAAO,CAACE,OAAO,CAACD,aAAa,CAAC,CAAC,CAAC;EAChD,IAAI9I,OAAO,EAAE;IACX,IAAIgJ,MAAM,GAAGhJ,OAAO,CAACiJ,aAAa,CAAC,IAAItR,MAAM,CAAC2B,MAAM,CAAC4P,kBAAkB,EAAE,CAAC;IAC1E,IAAI,CAACF,MAAM,IAAIrR,MAAM,CAAC8C,SAAS,EAAE;MAC/B,IAAIuF,OAAO,CAACmJ,UAAU,EAAE;QACtBH,MAAM,GAAGhJ,OAAO,CAACmJ,UAAU,CAACF,aAAa,CAAC,IAAItR,MAAM,CAAC2B,MAAM,CAAC4P,kBAAkB,EAAE,CAAC;MACnF,CAAC,MAAM;QACL;QACA5Q,qBAAqB,CAAC,MAAM;UAC1B,IAAI0H,OAAO,CAACmJ,UAAU,EAAE;YACtBH,MAAM,GAAGhJ,OAAO,CAACmJ,UAAU,CAACF,aAAa,CAAC,IAAItR,MAAM,CAAC2B,MAAM,CAAC4P,kBAAkB,EAAE,CAAC;YACjF,IAAIF,MAAM,EAAEA,MAAM,CAACrE,MAAM,CAAC,CAAC;UAC7B;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAIqE,MAAM,EAAEA,MAAM,CAACrE,MAAM,CAAC,CAAC;EAC7B;AACF,CAAC;AACD,MAAMyE,MAAM,GAAGA,CAACzR,MAAM,EAAE+E,KAAK,KAAK;EAChC,IAAI,CAAC/E,MAAM,CAACmH,MAAM,CAACpC,KAAK,CAAC,EAAE;EAC3B,MAAMmM,OAAO,GAAGlR,MAAM,CAACmH,MAAM,CAACpC,KAAK,CAAC,CAACuM,aAAa,CAAC,kBAAkB,CAAC;EACtE,IAAIJ,OAAO,EAAEA,OAAO,CAACQ,eAAe,CAAC,SAAS,CAAC;AACjD,CAAC;AACD,MAAMC,OAAO,GAAG3R,MAAM,IAAI;EACxB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACM,SAAS,IAAI,CAACN,MAAM,CAAC2B,MAAM,EAAE;EACnD,IAAIiQ,MAAM,GAAG5R,MAAM,CAAC2B,MAAM,CAACkQ,mBAAmB;EAC9C,MAAMC,GAAG,GAAG9R,MAAM,CAACmH,MAAM,CAAC1E,MAAM;EAChC,IAAI,CAACqP,GAAG,IAAI,CAACF,MAAM,IAAIA,MAAM,GAAG,CAAC,EAAE;EACnCA,MAAM,GAAGvH,IAAI,CAACK,GAAG,CAACkH,MAAM,EAAEE,GAAG,CAAC;EAC9B,MAAM3I,aAAa,GAAGnJ,MAAM,CAAC2B,MAAM,CAACwH,aAAa,KAAK,MAAM,GAAGnJ,MAAM,CAAC+R,oBAAoB,CAAC,CAAC,GAAG1H,IAAI,CAACe,IAAI,CAACpL,MAAM,CAAC2B,MAAM,CAACwH,aAAa,CAAC;EACrI,MAAMqE,WAAW,GAAGxN,MAAM,CAACwN,WAAW;EACtC,IAAIxN,MAAM,CAAC2B,MAAM,CAACkH,IAAI,IAAI7I,MAAM,CAAC2B,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC,EAAE;IACrD,MAAMkJ,YAAY,GAAGxE,WAAW;IAChC,MAAMyE,cAAc,GAAG,CAACD,YAAY,GAAGJ,MAAM,CAAC;IAC9CK,cAAc,CAACjP,IAAI,CAAC,GAAGyB,KAAK,CAACyN,IAAI,CAAC;MAChCzP,MAAM,EAAEmP;IACV,CAAC,CAAC,CAACtS,GAAG,CAAC,CAACkM,CAAC,EAAEpP,CAAC,KAAK;MACf,OAAO4V,YAAY,GAAG7I,aAAa,GAAG/M,CAAC;IACzC,CAAC,CAAC,CAAC;IACH4D,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAAC,CAACuH,OAAO,EAAEjM,CAAC,KAAK;MACpC,IAAI6V,cAAc,CAAC/S,QAAQ,CAACmJ,OAAO,CAACuI,MAAM,CAAC,EAAEa,MAAM,CAACzR,MAAM,EAAE5D,CAAC,CAAC;IAChE,CAAC,CAAC;IACF;EACF;EACA,MAAM+V,oBAAoB,GAAG3E,WAAW,GAAGrE,aAAa,GAAG,CAAC;EAC5D,IAAInJ,MAAM,CAAC2B,MAAM,CAACyQ,MAAM,IAAIpS,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,EAAE;IAC9C,KAAK,IAAI9O,CAAC,GAAGoR,WAAW,GAAGoE,MAAM,EAAExV,CAAC,IAAI+V,oBAAoB,GAAGP,MAAM,EAAExV,CAAC,IAAI,CAAC,EAAE;MAC7E,MAAMiW,SAAS,GAAG,CAACjW,CAAC,GAAG0V,GAAG,GAAGA,GAAG,IAAIA,GAAG;MACvC,IAAIO,SAAS,GAAG7E,WAAW,IAAI6E,SAAS,GAAGF,oBAAoB,EAAEV,MAAM,CAACzR,MAAM,EAAEqS,SAAS,CAAC;IAC5F;EACF,CAAC,MAAM;IACL,KAAK,IAAIjW,CAAC,GAAGiO,IAAI,CAACO,GAAG,CAAC4C,WAAW,GAAGoE,MAAM,EAAE,CAAC,CAAC,EAAExV,CAAC,IAAIiO,IAAI,CAACK,GAAG,CAACyH,oBAAoB,GAAGP,MAAM,EAAEE,GAAG,GAAG,CAAC,CAAC,EAAE1V,CAAC,IAAI,CAAC,EAAE;MAC7G,IAAIA,CAAC,KAAKoR,WAAW,KAAKpR,CAAC,GAAG+V,oBAAoB,IAAI/V,CAAC,GAAGoR,WAAW,CAAC,EAAE;QACtEiE,MAAM,CAACzR,MAAM,EAAE5D,CAAC,CAAC;MACnB;IACF;EACF;AACF,CAAC;AAED,SAASkW,yBAAyBA,CAACtS,MAAM,EAAE;EACzC,MAAM;IACJuH,UAAU;IACV5F;EACF,CAAC,GAAG3B,MAAM;EACV,MAAMmO,SAAS,GAAGnO,MAAM,CAAC4G,YAAY,GAAG5G,MAAM,CAACmO,SAAS,GAAG,CAACnO,MAAM,CAACmO,SAAS;EAC5E,IAAIX,WAAW;EACf,KAAK,IAAIpR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,UAAU,CAAC9E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;IAC7C,IAAI,OAAOmL,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;MAC5C,IAAI+R,SAAS,IAAI5G,UAAU,CAACnL,CAAC,CAAC,IAAI+R,SAAS,GAAG5G,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,GAAG,CAACmL,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,GAAGmL,UAAU,CAACnL,CAAC,CAAC,IAAI,CAAC,EAAE;QACzGoR,WAAW,GAAGpR,CAAC;MACjB,CAAC,MAAM,IAAI+R,SAAS,IAAI5G,UAAU,CAACnL,CAAC,CAAC,IAAI+R,SAAS,GAAG5G,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,EAAE;QACtEoR,WAAW,GAAGpR,CAAC,GAAG,CAAC;MACrB;IACF,CAAC,MAAM,IAAI+R,SAAS,IAAI5G,UAAU,CAACnL,CAAC,CAAC,EAAE;MACrCoR,WAAW,GAAGpR,CAAC;IACjB;EACF;EACA;EACA,IAAIuF,MAAM,CAAC4Q,mBAAmB,EAAE;IAC9B,IAAI/E,WAAW,GAAG,CAAC,IAAI,OAAOA,WAAW,KAAK,WAAW,EAAEA,WAAW,GAAG,CAAC;EAC5E;EACA,OAAOA,WAAW;AACpB;AACA,SAASgF,iBAAiBA,CAACC,cAAc,EAAE;EACzC,MAAMzS,MAAM,GAAG,IAAI;EACnB,MAAMmO,SAAS,GAAGnO,MAAM,CAAC4G,YAAY,GAAG5G,MAAM,CAACmO,SAAS,GAAG,CAACnO,MAAM,CAACmO,SAAS;EAC5E,MAAM;IACJ7G,QAAQ;IACR3F,MAAM;IACN6L,WAAW,EAAEkF,aAAa;IAC1BL,SAAS,EAAEM,iBAAiB;IAC5BzG,SAAS,EAAE0G;EACb,CAAC,GAAG5S,MAAM;EACV,IAAIwN,WAAW,GAAGiF,cAAc;EAChC,IAAIvG,SAAS;EACb,MAAM2G,mBAAmB,GAAGC,MAAM,IAAI;IACpC,IAAIT,SAAS,GAAGS,MAAM,GAAG9S,MAAM,CAACgH,OAAO,CAACqE,YAAY;IACpD,IAAIgH,SAAS,GAAG,CAAC,EAAE;MACjBA,SAAS,GAAGrS,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAG4P,SAAS;IACtD;IACA,IAAIA,SAAS,IAAIrS,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,EAAE;MAC7C4P,SAAS,IAAIrS,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM;IAC3C;IACA,OAAO4P,SAAS;EAClB,CAAC;EACD,IAAI,OAAO7E,WAAW,KAAK,WAAW,EAAE;IACtCA,WAAW,GAAG8E,yBAAyB,CAACtS,MAAM,CAAC;EACjD;EACA,IAAIsH,QAAQ,CAAC9I,OAAO,CAAC2P,SAAS,CAAC,IAAI,CAAC,EAAE;IACpCjC,SAAS,GAAG5E,QAAQ,CAAC9I,OAAO,CAAC2P,SAAS,CAAC;EACzC,CAAC,MAAM;IACL,MAAM4E,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAC/I,MAAM,CAACgJ,kBAAkB,EAAE6C,WAAW,CAAC;IAC7DtB,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACkD,WAAW,GAAGuF,IAAI,IAAIpR,MAAM,CAAC8I,cAAc,CAAC;EAC7E;EACA,IAAIyB,SAAS,IAAI5E,QAAQ,CAAC7E,MAAM,EAAEyJ,SAAS,GAAG5E,QAAQ,CAAC7E,MAAM,GAAG,CAAC;EACjE,IAAI+K,WAAW,KAAKkF,aAAa,IAAI,CAAC1S,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,EAAE;IACxD,IAAIgB,SAAS,KAAK0G,iBAAiB,EAAE;MACnC5S,MAAM,CAACkM,SAAS,GAAGA,SAAS;MAC5BlM,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA;EACF;EACA,IAAIsN,WAAW,KAAKkF,aAAa,IAAI1S,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,IAAIlL,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;IAC1GjH,MAAM,CAACqS,SAAS,GAAGQ,mBAAmB,CAACrF,WAAW,CAAC;IACnD;EACF;EACA,MAAM5E,WAAW,GAAG5I,MAAM,CAAC6I,IAAI,IAAIlH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;;EAEtE;EACA,IAAIuJ,SAAS;EACb,IAAIrS,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO,IAAItF,MAAM,CAACuJ,IAAI,EAAE;IAC3DmH,SAAS,GAAGQ,mBAAmB,CAACrF,WAAW,CAAC;EAC9C,CAAC,MAAM,IAAI5E,WAAW,EAAE;IACtB,MAAMoK,kBAAkB,GAAGhT,MAAM,CAACmH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAACuI,MAAM,KAAKpD,WAAW,CAAC;IACxF,IAAIyF,gBAAgB,GAAGnN,QAAQ,CAACkN,kBAAkB,CAACE,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IAC/F,IAAI1T,MAAM,CAACuG,KAAK,CAACkN,gBAAgB,CAAC,EAAE;MAClCA,gBAAgB,GAAG5I,IAAI,CAACO,GAAG,CAAC5K,MAAM,CAACmH,MAAM,CAAC3I,OAAO,CAACwU,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC3E;IACAX,SAAS,GAAGhI,IAAI,CAACC,KAAK,CAAC2I,gBAAgB,GAAGtR,MAAM,CAACkH,IAAI,CAACC,IAAI,CAAC;EAC7D,CAAC,MAAM,IAAI9I,MAAM,CAACmH,MAAM,CAACqG,WAAW,CAAC,EAAE;IACrC,MAAM/B,UAAU,GAAGzL,MAAM,CAACmH,MAAM,CAACqG,WAAW,CAAC,CAAC0F,YAAY,CAAC,yBAAyB,CAAC;IACrF,IAAIzH,UAAU,EAAE;MACd4G,SAAS,GAAGvM,QAAQ,CAAC2F,UAAU,EAAE,EAAE,CAAC;IACtC,CAAC,MAAM;MACL4G,SAAS,GAAG7E,WAAW;IACzB;EACF,CAAC,MAAM;IACL6E,SAAS,GAAG7E,WAAW;EACzB;EACAxH,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;IACpB4S,iBAAiB;IACjB1G,SAAS;IACTyG,iBAAiB;IACjBN,SAAS;IACTK,aAAa;IACblF;EACF,CAAC,CAAC;EACF,IAAIxN,MAAM,CAACO,WAAW,EAAE;IACtBoR,OAAO,CAAC3R,MAAM,CAAC;EACjB;EACAA,MAAM,CAACE,IAAI,CAAC,mBAAmB,CAAC;EAChCF,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;EAC9B,IAAIF,MAAM,CAACO,WAAW,IAAIP,MAAM,CAAC2B,MAAM,CAACwR,kBAAkB,EAAE;IAC1D,IAAIR,iBAAiB,KAAKN,SAAS,EAAE;MACnCrS,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACAF,MAAM,CAACE,IAAI,CAAC,aAAa,CAAC;EAC5B;AACF;AAEA,SAASkT,kBAAkBA,CAACjS,EAAE,EAAEkS,IAAI,EAAE;EACpC,MAAMrT,MAAM,GAAG,IAAI;EACnB,MAAM2B,MAAM,GAAG3B,MAAM,CAAC2B,MAAM;EAC5B,IAAI6H,KAAK,GAAGrI,EAAE,CAACiQ,OAAO,CAAC,IAAIzP,MAAM,CAACyF,UAAU,gBAAgB,CAAC;EAC7D,IAAI,CAACoC,KAAK,IAAIxJ,MAAM,CAAC8C,SAAS,IAAIuQ,IAAI,IAAIA,IAAI,CAAC5Q,MAAM,GAAG,CAAC,IAAI4Q,IAAI,CAACnU,QAAQ,CAACiC,EAAE,CAAC,EAAE;IAC9E,CAAC,GAAGkS,IAAI,CAAC/N,KAAK,CAAC+N,IAAI,CAAC7U,OAAO,CAAC2C,EAAE,CAAC,GAAG,CAAC,EAAEkS,IAAI,CAAC5Q,MAAM,CAAC,CAAC,CAAC3B,OAAO,CAACwS,MAAM,IAAI;MACnE,IAAI,CAAC9J,KAAK,IAAI8J,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAAC,IAAI5R,MAAM,CAACyF,UAAU,gBAAgB,CAAC,EAAE;QACrFoC,KAAK,GAAG8J,MAAM;MAChB;IACF,CAAC,CAAC;EACJ;EACA,IAAIE,UAAU,GAAG,KAAK;EACtB,IAAI/H,UAAU;EACd,IAAIjC,KAAK,EAAE;IACT,KAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4D,MAAM,CAACmH,MAAM,CAAC1E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;MAChD,IAAI4D,MAAM,CAACmH,MAAM,CAAC/K,CAAC,CAAC,KAAKoN,KAAK,EAAE;QAC9BgK,UAAU,GAAG,IAAI;QACjB/H,UAAU,GAAGrP,CAAC;QACd;MACF;IACF;EACF;EACA,IAAIoN,KAAK,IAAIgK,UAAU,EAAE;IACvBxT,MAAM,CAACyT,YAAY,GAAGjK,KAAK;IAC3B,IAAIxJ,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;MACnDjH,MAAM,CAAC0T,YAAY,GAAG5N,QAAQ,CAAC0D,KAAK,CAAC0J,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACnF,CAAC,MAAM;MACLlT,MAAM,CAAC0T,YAAY,GAAGjI,UAAU;IAClC;EACF,CAAC,MAAM;IACLzL,MAAM,CAACyT,YAAY,GAAGE,SAAS;IAC/B3T,MAAM,CAAC0T,YAAY,GAAGC,SAAS;IAC/B;EACF;EACA,IAAIhS,MAAM,CAACiS,mBAAmB,IAAI5T,MAAM,CAAC0T,YAAY,KAAKC,SAAS,IAAI3T,MAAM,CAAC0T,YAAY,KAAK1T,MAAM,CAACwN,WAAW,EAAE;IACjHxN,MAAM,CAAC4T,mBAAmB,CAAC,CAAC;EAC9B;AACF;AAEA,IAAIC,MAAM,GAAG;EACXpO,UAAU;EACVU,YAAY;EACZ8G,gBAAgB;EAChBT,kBAAkB;EAClB0B,oBAAoB;EACpBgB,cAAc;EACdmB,mBAAmB;EACnBmC,iBAAiB;EACjBY;AACF,CAAC;AAED,SAASU,kBAAkBA,CAACC,IAAI,EAAE;EAChC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,IAAI,CAACnO,YAAY,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;EACxC;EACA,MAAM5F,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNiF,YAAY,EAAEC,GAAG;IACjBsH,SAAS;IACT7K;EACF,CAAC,GAAGtD,MAAM;EACV,IAAI2B,MAAM,CAACqS,gBAAgB,EAAE;IAC3B,OAAOnN,GAAG,GAAG,CAACsH,SAAS,GAAGA,SAAS;EACrC;EACA,IAAIxM,MAAM,CAACgH,OAAO,EAAE;IAClB,OAAOwF,SAAS;EAClB;EACA,IAAI8F,gBAAgB,GAAG5Y,YAAY,CAACiI,SAAS,EAAEyQ,IAAI,CAAC;EACpDE,gBAAgB,IAAIjU,MAAM,CAAC8N,qBAAqB,CAAC,CAAC;EAClD,IAAIjH,GAAG,EAAEoN,gBAAgB,GAAG,CAACA,gBAAgB;EAC7C,OAAOA,gBAAgB,IAAI,CAAC;AAC9B;AAEA,SAASC,YAAYA,CAAC/F,SAAS,EAAEgG,YAAY,EAAE;EAC7C,MAAMnU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ4G,YAAY,EAAEC,GAAG;IACjBlF,MAAM;IACN2B,SAAS;IACT0L;EACF,CAAC,GAAGhP,MAAM;EACV,IAAI9D,CAAC,GAAG,CAAC;EACT,IAAII,CAAC,GAAG,CAAC;EACT,MAAM8X,CAAC,GAAG,CAAC;EACX,IAAIpU,MAAM,CAAC4F,YAAY,CAAC,CAAC,EAAE;IACzB1J,CAAC,GAAG2K,GAAG,GAAG,CAACsH,SAAS,GAAGA,SAAS;EAClC,CAAC,MAAM;IACL7R,CAAC,GAAG6R,SAAS;EACf;EACA,IAAIxM,MAAM,CAACqI,YAAY,EAAE;IACvB9N,CAAC,GAAGmO,IAAI,CAACC,KAAK,CAACpO,CAAC,CAAC;IACjBI,CAAC,GAAG+N,IAAI,CAACC,KAAK,CAAChO,CAAC,CAAC;EACnB;EACA0D,MAAM,CAACqU,iBAAiB,GAAGrU,MAAM,CAACmO,SAAS;EAC3CnO,MAAM,CAACmO,SAAS,GAAGnO,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG1J,CAAC,GAAGI,CAAC;EAChD,IAAIqF,MAAM,CAACgH,OAAO,EAAE;IAClBrF,SAAS,CAACtD,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG5F,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG,CAAC1J,CAAC,GAAG,CAACI,CAAC;EACjG,CAAC,MAAM,IAAI,CAACqF,MAAM,CAACqS,gBAAgB,EAAE;IACnC,IAAIhU,MAAM,CAAC4F,YAAY,CAAC,CAAC,EAAE;MACzB1J,CAAC,IAAI8D,MAAM,CAAC8N,qBAAqB,CAAC,CAAC;IACrC,CAAC,MAAM;MACLxR,CAAC,IAAI0D,MAAM,CAAC8N,qBAAqB,CAAC,CAAC;IACrC;IACAxK,SAAS,CAACxG,KAAK,CAAC+M,SAAS,GAAG,eAAe3N,CAAC,OAAOI,CAAC,OAAO8X,CAAC,KAAK;EACnE;;EAEA;EACA,IAAIE,WAAW;EACf,MAAMlF,cAAc,GAAGpP,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAGrP,MAAM,CAACwO,YAAY,CAAC,CAAC;EACpE,IAAIY,cAAc,KAAK,CAAC,EAAE;IACxBkF,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACnG,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,IAAIY,cAAc;EACpE;EACA,IAAIkF,WAAW,KAAKtF,QAAQ,EAAE;IAC5BhP,MAAM,CAACkP,cAAc,CAACf,SAAS,CAAC;EAClC;EACAnO,MAAM,CAACE,IAAI,CAAC,cAAc,EAAEF,MAAM,CAACmO,SAAS,EAAEgG,YAAY,CAAC;AAC7D;AAEA,SAAS3F,YAAYA,CAAA,EAAG;EACtB,OAAO,CAAC,IAAI,CAAClH,QAAQ,CAAC,CAAC,CAAC;AAC1B;AAEA,SAAS+H,YAAYA,CAAA,EAAG;EACtB,OAAO,CAAC,IAAI,CAAC/H,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC7E,MAAM,GAAG,CAAC,CAAC;AACjD;AAEA,SAAS8R,WAAWA,CAACpG,SAAS,EAAEjB,KAAK,EAAEsH,YAAY,EAAEC,eAAe,EAAEC,QAAQ,EAAE;EAC9E,IAAIvG,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC;EACf;EACA,IAAIjB,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,IAAI,CAACvL,MAAM,CAACuL,KAAK;EAC3B;EACA,IAAIsH,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,MAAMzU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN2B;EACF,CAAC,GAAGtD,MAAM;EACV,IAAIA,MAAM,CAAC2U,SAAS,IAAIhT,MAAM,CAACiT,8BAA8B,EAAE;IAC7D,OAAO,KAAK;EACd;EACA,MAAMpG,YAAY,GAAGxO,MAAM,CAACwO,YAAY,CAAC,CAAC;EAC1C,MAAMa,YAAY,GAAGrP,MAAM,CAACqP,YAAY,CAAC,CAAC;EAC1C,IAAIwF,YAAY;EAChB,IAAIJ,eAAe,IAAItG,SAAS,GAAGK,YAAY,EAAEqG,YAAY,GAAGrG,YAAY,CAAC,KAAK,IAAIiG,eAAe,IAAItG,SAAS,GAAGkB,YAAY,EAAEwF,YAAY,GAAGxF,YAAY,CAAC,KAAKwF,YAAY,GAAG1G,SAAS;;EAE5L;EACAnO,MAAM,CAACkP,cAAc,CAAC2F,YAAY,CAAC;EACnC,IAAIlT,MAAM,CAACgH,OAAO,EAAE;IAClB,MAAMmM,GAAG,GAAG9U,MAAM,CAAC4F,YAAY,CAAC,CAAC;IACjC,IAAIsH,KAAK,KAAK,CAAC,EAAE;MACf5J,SAAS,CAACwR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG,CAACD,YAAY;IAC7D,CAAC,MAAM;MACL,IAAI,CAAC7U,MAAM,CAACxD,OAAO,CAACI,YAAY,EAAE;QAChCrB,oBAAoB,CAAC;UACnByE,MAAM;UACN+U,cAAc,EAAE,CAACF,YAAY;UAC7BG,IAAI,EAAEF,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAxR,SAAS,CAAC2R,QAAQ,CAAC;QACjB,CAACH,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,CAACD,YAAY;QACrCK,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA,IAAIhI,KAAK,KAAK,CAAC,EAAE;IACflN,MAAM,CAACoN,aAAa,CAAC,CAAC,CAAC;IACvBpN,MAAM,CAACkU,YAAY,CAACW,YAAY,CAAC;IACjC,IAAIL,YAAY,EAAE;MAChBxU,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEgN,KAAK,EAAEwH,QAAQ,CAAC;MACrD1U,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;IAC9B;EACF,CAAC,MAAM;IACLF,MAAM,CAACoN,aAAa,CAACF,KAAK,CAAC;IAC3BlN,MAAM,CAACkU,YAAY,CAACW,YAAY,CAAC;IACjC,IAAIL,YAAY,EAAE;MAChBxU,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEgN,KAAK,EAAEwH,QAAQ,CAAC;MACrD1U,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAChC;IACA,IAAI,CAACF,MAAM,CAAC2U,SAAS,EAAE;MACrB3U,MAAM,CAAC2U,SAAS,GAAG,IAAI;MACvB,IAAI,CAAC3U,MAAM,CAACmV,iCAAiC,EAAE;QAC7CnV,MAAM,CAACmV,iCAAiC,GAAG,SAASC,aAAaA,CAACza,CAAC,EAAE;UACnE,IAAI,CAACqF,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;UACjC,IAAI3F,CAAC,CAACuG,MAAM,KAAK,IAAI,EAAE;UACvBlB,MAAM,CAACsD,SAAS,CAACxB,mBAAmB,CAAC,eAAe,EAAE9B,MAAM,CAACmV,iCAAiC,CAAC;UAC/FnV,MAAM,CAACmV,iCAAiC,GAAG,IAAI;UAC/C,OAAOnV,MAAM,CAACmV,iCAAiC;UAC/CnV,MAAM,CAAC2U,SAAS,GAAG,KAAK;UACxB,IAAIH,YAAY,EAAE;YAChBxU,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;UAC9B;QACF,CAAC;MACH;MACAF,MAAM,CAACsD,SAAS,CAACzB,gBAAgB,CAAC,eAAe,EAAE7B,MAAM,CAACmV,iCAAiC,CAAC;IAC9F;EACF;EACA,OAAO,IAAI;AACb;AAEA,IAAIhH,SAAS,GAAG;EACd9S,YAAY,EAAEyY,kBAAkB;EAChCI,YAAY;EACZ1F,YAAY;EACZa,YAAY;EACZkF;AACF,CAAC;AAED,SAASnH,aAAaA,CAACiI,QAAQ,EAAElB,YAAY,EAAE;EAC7C,MAAMnU,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACgH,OAAO,EAAE;IAC1B3I,MAAM,CAACsD,SAAS,CAACxG,KAAK,CAACwY,kBAAkB,GAAG,GAAGD,QAAQ,IAAI;IAC3DrV,MAAM,CAACsD,SAAS,CAACxG,KAAK,CAACyY,eAAe,GAAGF,QAAQ,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;EACtE;EACArV,MAAM,CAACE,IAAI,CAAC,eAAe,EAAEmV,QAAQ,EAAElB,YAAY,CAAC;AACtD;AAEA,SAASqB,cAAcA,CAACzV,IAAI,EAAE;EAC5B,IAAI;IACFC,MAAM;IACNwU,YAAY;IACZiB,SAAS;IACTC;EACF,CAAC,GAAG3V,IAAI;EACR,MAAM;IACJyN,WAAW;IACXkF;EACF,CAAC,GAAG1S,MAAM;EACV,IAAI2V,GAAG,GAAGF,SAAS;EACnB,IAAI,CAACE,GAAG,EAAE;IACR,IAAInI,WAAW,GAAGkF,aAAa,EAAEiD,GAAG,GAAG,MAAM,CAAC,KAAK,IAAInI,WAAW,GAAGkF,aAAa,EAAEiD,GAAG,GAAG,MAAM,CAAC,KAAKA,GAAG,GAAG,OAAO;EACrH;EACA3V,MAAM,CAACE,IAAI,CAAC,aAAawV,IAAI,EAAE,CAAC;EAChC,IAAIlB,YAAY,IAAImB,GAAG,KAAK,OAAO,EAAE;IACnC3V,MAAM,CAACE,IAAI,CAAC,uBAAuBwV,IAAI,EAAE,CAAC;EAC5C,CAAC,MAAM,IAAIlB,YAAY,IAAIhH,WAAW,KAAKkF,aAAa,EAAE;IACxD1S,MAAM,CAACE,IAAI,CAAC,wBAAwBwV,IAAI,EAAE,CAAC;IAC3C,IAAIC,GAAG,KAAK,MAAM,EAAE;MAClB3V,MAAM,CAACE,IAAI,CAAC,sBAAsBwV,IAAI,EAAE,CAAC;IAC3C,CAAC,MAAM;MACL1V,MAAM,CAACE,IAAI,CAAC,sBAAsBwV,IAAI,EAAE,CAAC;IAC3C;EACF;AACF;AAEA,SAASE,eAAeA,CAACpB,YAAY,EAAEiB,SAAS,EAAE;EAChD,IAAIjB,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAMxU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACV,IAAI2B,MAAM,CAACgH,OAAO,EAAE;EACpB,IAAIhH,MAAM,CAACwO,UAAU,EAAE;IACrBnQ,MAAM,CAACiN,gBAAgB,CAAC,CAAC;EAC3B;EACAuI,cAAc,CAAC;IACbxV,MAAM;IACNwU,YAAY;IACZiB,SAAS;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEA,SAASN,aAAaA,CAACZ,YAAY,EAAEiB,SAAS,EAAE;EAC9C,IAAIjB,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAMxU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACVA,MAAM,CAAC2U,SAAS,GAAG,KAAK;EACxB,IAAIhT,MAAM,CAACgH,OAAO,EAAE;EACpB3I,MAAM,CAACoN,aAAa,CAAC,CAAC,CAAC;EACvBoI,cAAc,CAAC;IACbxV,MAAM;IACNwU,YAAY;IACZiB,SAAS;IACTC,IAAI,EAAE;EACR,CAAC,CAAC;AACJ;AAEA,IAAIG,UAAU,GAAG;EACfzI,aAAa;EACbwI,eAAe;EACfR;AACF,CAAC;AAED,SAASU,OAAOA,CAAC/Q,KAAK,EAAEmI,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAEqB,OAAO,EAAE;EAC9D,IAAIhR,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAIyP,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI,OAAOzP,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGe,QAAQ,CAACf,KAAK,EAAE,EAAE,CAAC;EAC7B;EACA,MAAM/E,MAAM,GAAG,IAAI;EACnB,IAAIyL,UAAU,GAAG1G,KAAK;EACtB,IAAI0G,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAG,CAAC;EAClC,MAAM;IACJ9J,MAAM;IACN2F,QAAQ;IACRC,UAAU;IACVmL,aAAa;IACblF,WAAW;IACX5G,YAAY,EAAEC,GAAG;IACjBvD,SAAS;IACT2D;EACF,CAAC,GAAGjH,MAAM;EACV,IAAI,CAACiH,OAAO,IAAI,CAACyN,QAAQ,IAAI,CAACqB,OAAO,IAAI/V,MAAM,CAACM,SAAS,IAAIN,MAAM,CAAC2U,SAAS,IAAIhT,MAAM,CAACiT,8BAA8B,EAAE;IACtH,OAAO,KAAK;EACd;EACA,IAAI,OAAO1H,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,MAAM6F,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAC1K,MAAM,CAAC2B,MAAM,CAACgJ,kBAAkB,EAAEc,UAAU,CAAC;EACnE,IAAIS,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACmB,UAAU,GAAGsH,IAAI,IAAI/S,MAAM,CAAC2B,MAAM,CAAC8I,cAAc,CAAC;EACrF,IAAIyB,SAAS,IAAI5E,QAAQ,CAAC7E,MAAM,EAAEyJ,SAAS,GAAG5E,QAAQ,CAAC7E,MAAM,GAAG,CAAC;EACjE,MAAM0L,SAAS,GAAG,CAAC7G,QAAQ,CAAC4E,SAAS,CAAC;EACtC;EACA,IAAIvK,MAAM,CAAC4Q,mBAAmB,EAAE;IAC9B,KAAK,IAAInW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,UAAU,CAAC9E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;MAC7C,MAAM4Z,mBAAmB,GAAG,CAAC3L,IAAI,CAACC,KAAK,CAAC6D,SAAS,GAAG,GAAG,CAAC;MACxD,MAAM8H,cAAc,GAAG5L,IAAI,CAACC,KAAK,CAAC/C,UAAU,CAACnL,CAAC,CAAC,GAAG,GAAG,CAAC;MACtD,MAAM8Z,kBAAkB,GAAG7L,IAAI,CAACC,KAAK,CAAC/C,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;MAC9D,IAAI,OAAOmL,UAAU,CAACnL,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;QAC5C,IAAI4Z,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,GAAG,CAACA,kBAAkB,GAAGD,cAAc,IAAI,CAAC,EAAE;UACjIxK,UAAU,GAAGrP,CAAC;QAChB,CAAC,MAAM,IAAI4Z,mBAAmB,IAAIC,cAAc,IAAID,mBAAmB,GAAGE,kBAAkB,EAAE;UAC5FzK,UAAU,GAAGrP,CAAC,GAAG,CAAC;QACpB;MACF,CAAC,MAAM,IAAI4Z,mBAAmB,IAAIC,cAAc,EAAE;QAChDxK,UAAU,GAAGrP,CAAC;MAChB;IACF;EACF;EACA;EACA,IAAI4D,MAAM,CAACO,WAAW,IAAIkL,UAAU,KAAK+B,WAAW,EAAE;IACpD,IAAI,CAACxN,MAAM,CAACmW,cAAc,KAAKtP,GAAG,GAAGsH,SAAS,GAAGnO,MAAM,CAACmO,SAAS,IAAIA,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAGL,SAAS,GAAGnO,MAAM,CAACmO,SAAS,IAAIA,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC3K,OAAO,KAAK;IACd;IACA,IAAI,CAACxO,MAAM,CAACoW,cAAc,IAAIjI,SAAS,GAAGnO,MAAM,CAACmO,SAAS,IAAIA,SAAS,GAAGnO,MAAM,CAACqP,YAAY,CAAC,CAAC,EAAE;MAC/F,IAAI,CAAC7B,WAAW,IAAI,CAAC,MAAM/B,UAAU,EAAE;QACrC,OAAO,KAAK;MACd;IACF;EACF;EACA,IAAIA,UAAU,MAAMiH,aAAa,IAAI,CAAC,CAAC,IAAI8B,YAAY,EAAE;IACvDxU,MAAM,CAACE,IAAI,CAAC,wBAAwB,CAAC;EACvC;;EAEA;EACAF,MAAM,CAACkP,cAAc,CAACf,SAAS,CAAC;EAChC,IAAIsH,SAAS;EACb,IAAIhK,UAAU,GAAG+B,WAAW,EAAEiI,SAAS,GAAG,MAAM,CAAC,KAAK,IAAIhK,UAAU,GAAG+B,WAAW,EAAEiI,SAAS,GAAG,MAAM,CAAC,KAAKA,SAAS,GAAG,OAAO;;EAE/H;EACA,MAAM1O,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO;EACjE,MAAMoP,gBAAgB,GAAGtP,SAAS,IAAIgP,OAAO;EAC7C;EACA,IAAI,CAACM,gBAAgB,KAAKxP,GAAG,IAAI,CAACsH,SAAS,KAAKnO,MAAM,CAACmO,SAAS,IAAI,CAACtH,GAAG,IAAIsH,SAAS,KAAKnO,MAAM,CAACmO,SAAS,CAAC,EAAE;IAC3GnO,MAAM,CAACwS,iBAAiB,CAAC/G,UAAU,CAAC;IACpC;IACA,IAAI9J,MAAM,CAACwO,UAAU,EAAE;MACrBnQ,MAAM,CAACiN,gBAAgB,CAAC,CAAC;IAC3B;IACAjN,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;IAC5B,IAAI1O,MAAM,CAACkJ,MAAM,KAAK,OAAO,EAAE;MAC7B7K,MAAM,CAACkU,YAAY,CAAC/F,SAAS,CAAC;IAChC;IACA,IAAIsH,SAAS,KAAK,OAAO,EAAE;MACzBzV,MAAM,CAAC4V,eAAe,CAACpB,YAAY,EAAEiB,SAAS,CAAC;MAC/CzV,MAAM,CAACoV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;IAC/C;IACA,OAAO,KAAK;EACd;EACA,IAAI9T,MAAM,CAACgH,OAAO,EAAE;IAClB,MAAMmM,GAAG,GAAG9U,MAAM,CAAC4F,YAAY,CAAC,CAAC;IACjC,MAAM1K,CAAC,GAAG2L,GAAG,GAAGsH,SAAS,GAAG,CAACA,SAAS;IACtC,IAAIjB,KAAK,KAAK,CAAC,EAAE;MACf,IAAInG,SAAS,EAAE;QACb/G,MAAM,CAACsD,SAAS,CAACxG,KAAK,CAACwZ,cAAc,GAAG,MAAM;QAC9CtW,MAAM,CAACuW,iBAAiB,GAAG,IAAI;MACjC;MACA,IAAIxP,SAAS,IAAI,CAAC/G,MAAM,CAACwW,yBAAyB,IAAIxW,MAAM,CAAC2B,MAAM,CAAC8U,YAAY,GAAG,CAAC,EAAE;QACpFzW,MAAM,CAACwW,yBAAyB,GAAG,IAAI;QACvC7V,qBAAqB,CAAC,MAAM;UAC1B2C,SAAS,CAACwR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG5Z,CAAC;QACjD,CAAC,CAAC;MACJ,CAAC,MAAM;QACLoI,SAAS,CAACwR,GAAG,GAAG,YAAY,GAAG,WAAW,CAAC,GAAG5Z,CAAC;MACjD;MACA,IAAI6L,SAAS,EAAE;QACbpG,qBAAqB,CAAC,MAAM;UAC1BX,MAAM,CAACsD,SAAS,CAACxG,KAAK,CAACwZ,cAAc,GAAG,EAAE;UAC1CtW,MAAM,CAACuW,iBAAiB,GAAG,KAAK;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL,IAAI,CAACvW,MAAM,CAACxD,OAAO,CAACI,YAAY,EAAE;QAChCrB,oBAAoB,CAAC;UACnByE,MAAM;UACN+U,cAAc,EAAE7Z,CAAC;UACjB8Z,IAAI,EAAEF,GAAG,GAAG,MAAM,GAAG;QACvB,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MACAxR,SAAS,CAAC2R,QAAQ,CAAC;QACjB,CAACH,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG5Z,CAAC;QACzBga,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IACA,OAAO,IAAI;EACb;EACA,MAAMtW,OAAO,GAAGiB,UAAU,CAAC,CAAC;EAC5B,MAAMd,QAAQ,GAAGH,OAAO,CAACG,QAAQ;EACjC,IAAIgI,SAAS,IAAI,CAACgP,OAAO,IAAIhX,QAAQ,IAAIiB,MAAM,CAAC8C,SAAS,EAAE;IACzD9C,MAAM,CAACgH,OAAO,CAAC6M,MAAM,CAAC,KAAK,EAAE,KAAK,EAAEpI,UAAU,CAAC;EACjD;EACAzL,MAAM,CAACoN,aAAa,CAACF,KAAK,CAAC;EAC3BlN,MAAM,CAACkU,YAAY,CAAC/F,SAAS,CAAC;EAC9BnO,MAAM,CAACwS,iBAAiB,CAAC/G,UAAU,CAAC;EACpCzL,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;EAC5BrQ,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEgN,KAAK,EAAEwH,QAAQ,CAAC;EACrD1U,MAAM,CAAC4V,eAAe,CAACpB,YAAY,EAAEiB,SAAS,CAAC;EAC/C,IAAIvI,KAAK,KAAK,CAAC,EAAE;IACflN,MAAM,CAACoV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;EAC/C,CAAC,MAAM,IAAI,CAACzV,MAAM,CAAC2U,SAAS,EAAE;IAC5B3U,MAAM,CAAC2U,SAAS,GAAG,IAAI;IACvB,IAAI,CAAC3U,MAAM,CAAC0W,6BAA6B,EAAE;MACzC1W,MAAM,CAAC0W,6BAA6B,GAAG,SAAStB,aAAaA,CAACza,CAAC,EAAE;QAC/D,IAAI,CAACqF,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;QACjC,IAAI3F,CAAC,CAACuG,MAAM,KAAK,IAAI,EAAE;QACvBlB,MAAM,CAACsD,SAAS,CAACxB,mBAAmB,CAAC,eAAe,EAAE9B,MAAM,CAAC0W,6BAA6B,CAAC;QAC3F1W,MAAM,CAAC0W,6BAA6B,GAAG,IAAI;QAC3C,OAAO1W,MAAM,CAAC0W,6BAA6B;QAC3C1W,MAAM,CAACoV,aAAa,CAACZ,YAAY,EAAEiB,SAAS,CAAC;MAC/C,CAAC;IACH;IACAzV,MAAM,CAACsD,SAAS,CAACzB,gBAAgB,CAAC,eAAe,EAAE7B,MAAM,CAAC0W,6BAA6B,CAAC;EAC1F;EACA,OAAO,IAAI;AACb;AAEA,SAASC,WAAWA,CAAC5R,KAAK,EAAEmI,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAE;EACzD,IAAI3P,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAIyP,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI,OAAOzP,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM6R,aAAa,GAAG9Q,QAAQ,CAACf,KAAK,EAAE,EAAE,CAAC;IACzCA,KAAK,GAAG6R,aAAa;EACvB;EACA,MAAM5W,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO4M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,MAAMtE,WAAW,GAAG5I,MAAM,CAAC6I,IAAI,IAAI7I,MAAM,CAAC2B,MAAM,CAACkH,IAAI,IAAI7I,MAAM,CAAC2B,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;EACpF,IAAI+N,QAAQ,GAAG9R,KAAK;EACpB,IAAI/E,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,EAAE;IACtB,IAAIlL,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;MACnD;MACA4P,QAAQ,GAAGA,QAAQ,GAAG7W,MAAM,CAACgH,OAAO,CAACqE,YAAY;IACnD,CAAC,MAAM;MACL,IAAIyL,gBAAgB;MACpB,IAAIlO,WAAW,EAAE;QACf,MAAM6C,UAAU,GAAGoL,QAAQ,GAAG7W,MAAM,CAAC2B,MAAM,CAACkH,IAAI,CAACC,IAAI;QACrDgO,gBAAgB,GAAG9W,MAAM,CAACmH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKzH,UAAU,CAAC,CAACmF,MAAM;MAC7H,CAAC,MAAM;QACLkG,gBAAgB,GAAG9W,MAAM,CAACsN,mBAAmB,CAACuJ,QAAQ,CAAC;MACzD;MACA,MAAME,IAAI,GAAGnO,WAAW,GAAGyB,IAAI,CAACe,IAAI,CAACpL,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGzC,MAAM,CAAC2B,MAAM,CAACkH,IAAI,CAACC,IAAI,CAAC,GAAG9I,MAAM,CAACmH,MAAM,CAAC1E,MAAM;MAC3G,MAAM;QACJiG;MACF,CAAC,GAAG1I,MAAM,CAAC2B,MAAM;MACjB,IAAIwH,aAAa,GAAGnJ,MAAM,CAAC2B,MAAM,CAACwH,aAAa;MAC/C,IAAIA,aAAa,KAAK,MAAM,EAAE;QAC5BA,aAAa,GAAGnJ,MAAM,CAAC+R,oBAAoB,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL5I,aAAa,GAAGkB,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAACvG,MAAM,CAAC2B,MAAM,CAACwH,aAAa,EAAE,EAAE,CAAC,CAAC;QACtE,IAAIT,cAAc,IAAIS,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;UAC7CA,aAAa,GAAGA,aAAa,GAAG,CAAC;QACnC;MACF;MACA,IAAI6N,WAAW,GAAGD,IAAI,GAAGD,gBAAgB,GAAG3N,aAAa;MACzD,IAAIT,cAAc,EAAE;QAClBsO,WAAW,GAAGA,WAAW,IAAIF,gBAAgB,GAAGzM,IAAI,CAACe,IAAI,CAACjC,aAAa,GAAG,CAAC,CAAC;MAC9E;MACA,IAAIuL,QAAQ,IAAIhM,cAAc,IAAI1I,MAAM,CAAC2B,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAI,CAACP,WAAW,EAAE;QACxFoO,WAAW,GAAG,KAAK;MACrB;MACA,IAAIA,WAAW,EAAE;QACf,MAAMvB,SAAS,GAAG/M,cAAc,GAAGoO,gBAAgB,GAAG9W,MAAM,CAACwN,WAAW,GAAG,MAAM,GAAG,MAAM,GAAGsJ,gBAAgB,GAAG9W,MAAM,CAACwN,WAAW,GAAG,CAAC,GAAGxN,MAAM,CAAC2B,MAAM,CAACwH,aAAa,GAAG,MAAM,GAAG,MAAM;QACtLnJ,MAAM,CAACiX,OAAO,CAAC;UACbxB,SAAS;UACTK,OAAO,EAAE,IAAI;UACb7C,gBAAgB,EAAEwC,SAAS,KAAK,MAAM,GAAGqB,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGC,IAAI,GAAG,CAAC;UAC3FG,cAAc,EAAEzB,SAAS,KAAK,MAAM,GAAGzV,MAAM,CAACqS,SAAS,GAAGsB;QAC5D,CAAC,CAAC;MACJ;MACA,IAAI/K,WAAW,EAAE;QACf,MAAM6C,UAAU,GAAGoL,QAAQ,GAAG7W,MAAM,CAAC2B,MAAM,CAACkH,IAAI,CAACC,IAAI;QACrD+N,QAAQ,GAAG7W,MAAM,CAACmH,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKzH,UAAU,CAAC,CAACmF,MAAM;MACrH,CAAC,MAAM;QACLiG,QAAQ,GAAG7W,MAAM,CAACsN,mBAAmB,CAACuJ,QAAQ,CAAC;MACjD;IACF;EACF;EACAlW,qBAAqB,CAAC,MAAM;IAC1BX,MAAM,CAAC8V,OAAO,CAACe,QAAQ,EAAE3J,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;EACzD,CAAC,CAAC;EACF,OAAO1U,MAAM;AACf;;AAEA;AACA,SAASmX,SAASA,CAACjK,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAE;EAChD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAMxU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJiH,OAAO;IACPtF,MAAM;IACNgT;EACF,CAAC,GAAG3U,MAAM;EACV,IAAI,CAACiH,OAAO,IAAIjH,MAAM,CAACM,SAAS,EAAE,OAAON,MAAM;EAC/C,IAAI,OAAOkN,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,IAAIkK,QAAQ,GAAGzV,MAAM,CAAC8I,cAAc;EACpC,IAAI9I,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAIxH,MAAM,CAAC8I,cAAc,KAAK,CAAC,IAAI9I,MAAM,CAAC0V,kBAAkB,EAAE;IAC/FD,QAAQ,GAAG/M,IAAI,CAACO,GAAG,CAAC5K,MAAM,CAAC+R,oBAAoB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;EACtE;EACA,MAAMuF,SAAS,GAAGtX,MAAM,CAACwN,WAAW,GAAG7L,MAAM,CAACgJ,kBAAkB,GAAG,CAAC,GAAGyM,QAAQ;EAC/E,MAAMrQ,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO;EAC1D,IAAItF,MAAM,CAACuJ,IAAI,EAAE;IACf,IAAIyJ,SAAS,IAAI,CAAC5N,SAAS,IAAIpF,MAAM,CAAC4V,mBAAmB,EAAE,OAAO,KAAK;IACvEvX,MAAM,CAACiX,OAAO,CAAC;MACbxB,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAzV,MAAM,CAACwX,WAAW,GAAGxX,MAAM,CAACsD,SAAS,CAACmU,UAAU;IAChD,IAAIzX,MAAM,CAACwN,WAAW,KAAKxN,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC,IAAId,MAAM,CAACgH,OAAO,EAAE;MACrEhI,qBAAqB,CAAC,MAAM;QAC1BX,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,GAAG8J,SAAS,EAAEpK,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;MAC/E,CAAC,CAAC;MACF,OAAO,IAAI;IACb;EACF;EACA,IAAI/S,MAAM,CAACyQ,MAAM,IAAIpS,MAAM,CAACuP,KAAK,EAAE;IACjC,OAAOvP,MAAM,CAAC8V,OAAO,CAAC,CAAC,EAAE5I,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;EACzD;EACA,OAAO1U,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,GAAG8J,SAAS,EAAEpK,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;AACtF;;AAEA;AACA,SAASgD,SAASA,CAACxK,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAE;EAChD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAMxU,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN2F,QAAQ;IACRC,UAAU;IACVX,YAAY;IACZK,OAAO;IACP0N;EACF,CAAC,GAAG3U,MAAM;EACV,IAAI,CAACiH,OAAO,IAAIjH,MAAM,CAACM,SAAS,EAAE,OAAON,MAAM;EAC/C,IAAI,OAAOkN,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,MAAMnG,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO;EAC1D,IAAItF,MAAM,CAACuJ,IAAI,EAAE;IACf,IAAIyJ,SAAS,IAAI,CAAC5N,SAAS,IAAIpF,MAAM,CAAC4V,mBAAmB,EAAE,OAAO,KAAK;IACvEvX,MAAM,CAACiX,OAAO,CAAC;MACbxB,SAAS,EAAE;IACb,CAAC,CAAC;IACF;IACAzV,MAAM,CAACwX,WAAW,GAAGxX,MAAM,CAACsD,SAAS,CAACmU,UAAU;EAClD;EACA,MAAMtJ,SAAS,GAAGvH,YAAY,GAAG5G,MAAM,CAACmO,SAAS,GAAG,CAACnO,MAAM,CAACmO,SAAS;EACrE,SAASwJ,SAASA,CAACC,GAAG,EAAE;IACtB,IAAIA,GAAG,GAAG,CAAC,EAAE,OAAO,CAACvN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACG,GAAG,CAACoN,GAAG,CAAC,CAAC;IAC9C,OAAOvN,IAAI,CAACC,KAAK,CAACsN,GAAG,CAAC;EACxB;EACA,MAAM5B,mBAAmB,GAAG2B,SAAS,CAACxJ,SAAS,CAAC;EAChD,MAAM0J,kBAAkB,GAAGvQ,QAAQ,CAAChI,GAAG,CAACsY,GAAG,IAAID,SAAS,CAACC,GAAG,CAAC,CAAC;EAC9D,MAAME,UAAU,GAAGnW,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO;EAC7D,IAAI+Q,QAAQ,GAAG1Q,QAAQ,CAACuQ,kBAAkB,CAACrZ,OAAO,CAACwX,mBAAmB,CAAC,GAAG,CAAC,CAAC;EAC5E,IAAI,OAAOgC,QAAQ,KAAK,WAAW,KAAKrW,MAAM,CAACgH,OAAO,IAAImP,UAAU,CAAC,EAAE;IACrE,IAAIG,aAAa;IACjB3Q,QAAQ,CAACxG,OAAO,CAAC,CAACgL,IAAI,EAAEI,SAAS,KAAK;MACpC,IAAI8J,mBAAmB,IAAIlK,IAAI,EAAE;QAC/B;QACAmM,aAAa,GAAG/L,SAAS;MAC3B;IACF,CAAC,CAAC;IACF,IAAI,OAAO+L,aAAa,KAAK,WAAW,EAAE;MACxCD,QAAQ,GAAGF,UAAU,GAAGxQ,QAAQ,CAAC2Q,aAAa,CAAC,GAAG3Q,QAAQ,CAAC2Q,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAGA,aAAa,CAAC;IACnH;EACF;EACA,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAI,OAAOF,QAAQ,KAAK,WAAW,EAAE;IACnCE,SAAS,GAAG3Q,UAAU,CAAC/I,OAAO,CAACwZ,QAAQ,CAAC;IACxC,IAAIE,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGlY,MAAM,CAACwN,WAAW,GAAG,CAAC;IACrD,IAAI7L,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAIxH,MAAM,CAAC8I,cAAc,KAAK,CAAC,IAAI9I,MAAM,CAAC0V,kBAAkB,EAAE;MAC/Fa,SAAS,GAAGA,SAAS,GAAGlY,MAAM,CAAC+R,oBAAoB,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC;MACzEmG,SAAS,GAAG7N,IAAI,CAACO,GAAG,CAACsN,SAAS,EAAE,CAAC,CAAC;IACpC;EACF;EACA,IAAIvW,MAAM,CAACyQ,MAAM,IAAIpS,MAAM,CAACsP,WAAW,EAAE;IACvC,MAAM6I,SAAS,GAAGnY,MAAM,CAAC2B,MAAM,CAACqF,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,IAAIjH,MAAM,CAACgH,OAAO,GAAGhH,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAG,CAAC,GAAGzC,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC;IACxJ,OAAOzC,MAAM,CAAC8V,OAAO,CAACqC,SAAS,EAAEjL,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;EACjE,CAAC,MAAM,IAAI/S,MAAM,CAACuJ,IAAI,IAAIlL,MAAM,CAACwN,WAAW,KAAK,CAAC,IAAI7L,MAAM,CAACgH,OAAO,EAAE;IACpEhI,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAAC8V,OAAO,CAACoC,SAAS,EAAEhL,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;IAC1D,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACA,OAAO1U,MAAM,CAAC8V,OAAO,CAACoC,SAAS,EAAEhL,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;AACjE;;AAEA;AACA,SAAS0D,UAAUA,CAAClL,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAE;EACjD,IAAIF,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,MAAMxU,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO4M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,OAAOlN,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,EAAEN,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;AAC1E;;AAEA;AACA,SAAS2D,cAAcA,CAACnL,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,EAAE4D,SAAS,EAAE;EAChE,IAAI9D,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,IAAI;EACrB;EACA,IAAI8D,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,GAAG;EACjB;EACA,MAAMtY,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,IAAI,OAAO4M,KAAK,KAAK,WAAW,EAAE;IAChCA,KAAK,GAAGlN,MAAM,CAAC2B,MAAM,CAACuL,KAAK;EAC7B;EACA,IAAInI,KAAK,GAAG/E,MAAM,CAACwN,WAAW;EAC9B,MAAMuF,IAAI,GAAG1I,IAAI,CAACK,GAAG,CAAC1K,MAAM,CAAC2B,MAAM,CAACgJ,kBAAkB,EAAE5F,KAAK,CAAC;EAC9D,MAAMmH,SAAS,GAAG6G,IAAI,GAAG1I,IAAI,CAACC,KAAK,CAAC,CAACvF,KAAK,GAAGgO,IAAI,IAAI/S,MAAM,CAAC2B,MAAM,CAAC8I,cAAc,CAAC;EAClF,MAAM0D,SAAS,GAAGnO,MAAM,CAAC4G,YAAY,GAAG5G,MAAM,CAACmO,SAAS,GAAG,CAACnO,MAAM,CAACmO,SAAS;EAC5E,IAAIA,SAAS,IAAInO,MAAM,CAACsH,QAAQ,CAAC4E,SAAS,CAAC,EAAE;IAC3C;IACA;IACA,MAAMqM,WAAW,GAAGvY,MAAM,CAACsH,QAAQ,CAAC4E,SAAS,CAAC;IAC9C,MAAMsM,QAAQ,GAAGxY,MAAM,CAACsH,QAAQ,CAAC4E,SAAS,GAAG,CAAC,CAAC;IAC/C,IAAIiC,SAAS,GAAGoK,WAAW,GAAG,CAACC,QAAQ,GAAGD,WAAW,IAAID,SAAS,EAAE;MAClEvT,KAAK,IAAI/E,MAAM,CAAC2B,MAAM,CAAC8I,cAAc;IACvC;EACF,CAAC,MAAM;IACL;IACA;IACA,MAAMuN,QAAQ,GAAGhY,MAAM,CAACsH,QAAQ,CAAC4E,SAAS,GAAG,CAAC,CAAC;IAC/C,MAAMqM,WAAW,GAAGvY,MAAM,CAACsH,QAAQ,CAAC4E,SAAS,CAAC;IAC9C,IAAIiC,SAAS,GAAG6J,QAAQ,IAAI,CAACO,WAAW,GAAGP,QAAQ,IAAIM,SAAS,EAAE;MAChEvT,KAAK,IAAI/E,MAAM,CAAC2B,MAAM,CAAC8I,cAAc;IACvC;EACF;EACA1F,KAAK,GAAGsF,IAAI,CAACO,GAAG,CAAC7F,KAAK,EAAE,CAAC,CAAC;EAC1BA,KAAK,GAAGsF,IAAI,CAACK,GAAG,CAAC3F,KAAK,EAAE/E,MAAM,CAACuH,UAAU,CAAC9E,MAAM,GAAG,CAAC,CAAC;EACrD,OAAOzC,MAAM,CAAC8V,OAAO,CAAC/Q,KAAK,EAAEmI,KAAK,EAAEsH,YAAY,EAAEE,QAAQ,CAAC;AAC7D;AAEA,SAASd,mBAAmBA,CAAA,EAAG;EAC7B,MAAM5T,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACM,SAAS,EAAE;EACtB,MAAM;IACJqB,MAAM;IACN+E;EACF,CAAC,GAAG1G,MAAM;EACV,MAAMmJ,aAAa,GAAGxH,MAAM,CAACwH,aAAa,KAAK,MAAM,GAAGnJ,MAAM,CAAC+R,oBAAoB,CAAC,CAAC,GAAGpQ,MAAM,CAACwH,aAAa;EAC5G,IAAIsP,YAAY,GAAGzY,MAAM,CAAC0T,YAAY;EACtC,IAAIrB,SAAS;EACb,MAAMlB,aAAa,GAAGnR,MAAM,CAAC8C,SAAS,GAAG,cAAc,GAAG,IAAInB,MAAM,CAACyF,UAAU,EAAE;EACjF,IAAIzF,MAAM,CAACuJ,IAAI,EAAE;IACf,IAAIlL,MAAM,CAAC2U,SAAS,EAAE;IACtBtC,SAAS,GAAGvM,QAAQ,CAAC9F,MAAM,CAACyT,YAAY,CAACP,YAAY,CAAC,yBAAyB,CAAC,EAAE,EAAE,CAAC;IACrF,IAAIvR,MAAM,CAAC+G,cAAc,EAAE;MACzB,IAAI+P,YAAY,GAAGzY,MAAM,CAAC0Y,YAAY,GAAGvP,aAAa,GAAG,CAAC,IAAIsP,YAAY,GAAGzY,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGzC,MAAM,CAAC0Y,YAAY,GAAGvP,aAAa,GAAG,CAAC,EAAE;QAC3InJ,MAAM,CAACiX,OAAO,CAAC,CAAC;QAChBwB,YAAY,GAAGzY,MAAM,CAAC2Y,aAAa,CAAC/d,eAAe,CAAC8L,QAAQ,EAAE,GAAGyK,aAAa,6BAA6BkB,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7H5W,QAAQ,CAAC,MAAM;UACbuE,MAAM,CAAC8V,OAAO,CAAC2C,YAAY,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLzY,MAAM,CAAC8V,OAAO,CAAC2C,YAAY,CAAC;MAC9B;IACF,CAAC,MAAM,IAAIA,YAAY,GAAGzY,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG0G,aAAa,EAAE;MAC9DnJ,MAAM,CAACiX,OAAO,CAAC,CAAC;MAChBwB,YAAY,GAAGzY,MAAM,CAAC2Y,aAAa,CAAC/d,eAAe,CAAC8L,QAAQ,EAAE,GAAGyK,aAAa,6BAA6BkB,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7H5W,QAAQ,CAAC,MAAM;QACbuE,MAAM,CAAC8V,OAAO,CAAC2C,YAAY,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLzY,MAAM,CAAC8V,OAAO,CAAC2C,YAAY,CAAC;IAC9B;EACF,CAAC,MAAM;IACLzY,MAAM,CAAC8V,OAAO,CAAC2C,YAAY,CAAC;EAC9B;AACF;AAEA,IAAIjP,KAAK,GAAG;EACVsM,OAAO;EACPa,WAAW;EACXQ,SAAS;EACTO,SAAS;EACTU,UAAU;EACVC,cAAc;EACdzE;AACF,CAAC;AAED,SAASgF,UAAUA,CAAC1B,cAAc,EAAEnB,OAAO,EAAE;EAC3C,MAAM/V,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN+E;EACF,CAAC,GAAG1G,MAAM;EACV,IAAI,CAAC2B,MAAM,CAACuJ,IAAI,IAAIlL,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;EACrE,MAAM8B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAM5B,MAAM,GAAGvM,eAAe,CAAC8L,QAAQ,EAAE,IAAI/E,MAAM,CAACyF,UAAU,gBAAgB,CAAC;IAC/ED,MAAM,CAACrG,OAAO,CAAC,CAACK,EAAE,EAAE4D,KAAK,KAAK;MAC5B5D,EAAE,CAAC0X,YAAY,CAAC,yBAAyB,EAAE9T,KAAK,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC;EACD,MAAM6D,WAAW,GAAG5I,MAAM,CAAC6I,IAAI,IAAIlH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,MAAM2B,cAAc,GAAG9I,MAAM,CAAC8I,cAAc,IAAI7B,WAAW,GAAGjH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;EACnF,MAAMgQ,eAAe,GAAG9Y,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGgI,cAAc,KAAK,CAAC;EACnE,MAAMsO,cAAc,GAAGnQ,WAAW,IAAI5I,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGd,MAAM,CAACkH,IAAI,CAACC,IAAI,KAAK,CAAC;EACnF,MAAMkQ,cAAc,GAAGC,cAAc,IAAI;IACvC,KAAK,IAAI7c,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6c,cAAc,EAAE7c,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMiM,OAAO,GAAGrI,MAAM,CAAC8C,SAAS,GAAGjH,aAAa,CAAC,cAAc,EAAE,CAAC8F,MAAM,CAACuX,eAAe,CAAC,CAAC,GAAGrd,aAAa,CAAC,KAAK,EAAE,CAAC8F,MAAM,CAACyF,UAAU,EAAEzF,MAAM,CAACuX,eAAe,CAAC,CAAC;MAC9JlZ,MAAM,CAAC0G,QAAQ,CAACyS,MAAM,CAAC9Q,OAAO,CAAC;IACjC;EACF,CAAC;EACD,IAAIyQ,eAAe,EAAE;IACnB,IAAInX,MAAM,CAACyX,kBAAkB,EAAE;MAC7B,MAAMC,WAAW,GAAG5O,cAAc,GAAGzK,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGgI,cAAc;MAC1EuO,cAAc,CAACK,WAAW,CAAC;MAC3BrZ,MAAM,CAACsZ,YAAY,CAAC,CAAC;MACrBtZ,MAAM,CAACmG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM;MACLxK,WAAW,CAAC,iLAAiL,CAAC;IAChM;IACAoN,UAAU,CAAC,CAAC;EACd,CAAC,MAAM,IAAIgQ,cAAc,EAAE;IACzB,IAAIpX,MAAM,CAACyX,kBAAkB,EAAE;MAC7B,MAAMC,WAAW,GAAG1X,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG9I,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGd,MAAM,CAACkH,IAAI,CAACC,IAAI;MAC9EkQ,cAAc,CAACK,WAAW,CAAC;MAC3BrZ,MAAM,CAACsZ,YAAY,CAAC,CAAC;MACrBtZ,MAAM,CAACmG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM;MACLxK,WAAW,CAAC,4KAA4K,CAAC;IAC3L;IACAoN,UAAU,CAAC,CAAC;EACd,CAAC,MAAM;IACLA,UAAU,CAAC,CAAC;EACd;EACA/I,MAAM,CAACiX,OAAO,CAAC;IACbC,cAAc;IACdzB,SAAS,EAAE9T,MAAM,CAAC+G,cAAc,GAAGiL,SAAS,GAAG,MAAM;IACrDoC;EACF,CAAC,CAAC;AACJ;AAEA,SAASkB,OAAOA,CAAC7Z,KAAK,EAAE;EACtB,IAAI;IACF8Z,cAAc;IACdpB,OAAO,GAAG,IAAI;IACdL,SAAS;IACTvB,YAAY;IACZjB,gBAAgB;IAChB8C,OAAO;IACP5B,YAAY;IACZoF;EACF,CAAC,GAAGnc,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;EACjC,MAAM4C,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,EAAE;EACzBlL,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;EAC5B,MAAM;IACJiH,MAAM;IACNiP,cAAc;IACdD,cAAc;IACdzP,QAAQ;IACR/E;EACF,CAAC,GAAG3B,MAAM;EACV,MAAM;IACJ0I,cAAc;IACd+N;EACF,CAAC,GAAG9U,MAAM;EACV3B,MAAM,CAACoW,cAAc,GAAG,IAAI;EAC5BpW,MAAM,CAACmW,cAAc,GAAG,IAAI;EAC5B,IAAInW,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;IAC5C,IAAI6O,OAAO,EAAE;MACX,IAAI,CAACnU,MAAM,CAAC+G,cAAc,IAAI1I,MAAM,CAACkM,SAAS,KAAK,CAAC,EAAE;QACpDlM,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC9D,CAAC,MAAM,IAAId,MAAM,CAAC+G,cAAc,IAAI1I,MAAM,CAACkM,SAAS,GAAGvK,MAAM,CAACwH,aAAa,EAAE;QAC3EnJ,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAGzC,MAAM,CAACkM,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjF,CAAC,MAAM,IAAIlM,MAAM,CAACkM,SAAS,KAAKlM,MAAM,CAACsH,QAAQ,CAAC7E,MAAM,GAAG,CAAC,EAAE;QAC1DzC,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACgH,OAAO,CAACqE,YAAY,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC7D;IACF;IACArL,MAAM,CAACoW,cAAc,GAAGA,cAAc;IACtCpW,MAAM,CAACmW,cAAc,GAAGA,cAAc;IACtCnW,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;IACtB;EACF;EACA,IAAIiJ,aAAa,GAAGxH,MAAM,CAACwH,aAAa;EACxC,IAAIA,aAAa,KAAK,MAAM,EAAE;IAC5BA,aAAa,GAAGnJ,MAAM,CAAC+R,oBAAoB,CAAC,CAAC;EAC/C,CAAC,MAAM;IACL5I,aAAa,GAAGkB,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAAC5E,MAAM,CAACwH,aAAa,EAAE,EAAE,CAAC,CAAC;IAC/D,IAAIT,cAAc,IAAIS,aAAa,GAAG,CAAC,KAAK,CAAC,EAAE;MAC7CA,aAAa,GAAGA,aAAa,GAAG,CAAC;IACnC;EACF;EACA,MAAMsB,cAAc,GAAG9I,MAAM,CAAC0V,kBAAkB,GAAGlO,aAAa,GAAGxH,MAAM,CAAC8I,cAAc;EACxF,IAAIiO,YAAY,GAAGjO,cAAc;EACjC,IAAIiO,YAAY,GAAGjO,cAAc,KAAK,CAAC,EAAE;IACvCiO,YAAY,IAAIjO,cAAc,GAAGiO,YAAY,GAAGjO,cAAc;EAChE;EACAiO,YAAY,IAAI/W,MAAM,CAAC6X,oBAAoB;EAC3CxZ,MAAM,CAAC0Y,YAAY,GAAGA,YAAY;EAClC,MAAM9P,WAAW,GAAG5I,MAAM,CAAC6I,IAAI,IAAIlH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;EACtE,IAAI3B,MAAM,CAAC1E,MAAM,GAAG0G,aAAa,GAAGuP,YAAY,IAAI1Y,MAAM,CAAC2B,MAAM,CAACkJ,MAAM,KAAK,OAAO,IAAI1D,MAAM,CAAC1E,MAAM,GAAG0G,aAAa,GAAGuP,YAAY,GAAG,CAAC,EAAE;IACxI/c,WAAW,CAAC,0OAA0O,CAAC;EACzP,CAAC,MAAM,IAAIiN,WAAW,IAAIjH,MAAM,CAACkH,IAAI,CAAC4Q,IAAI,KAAK,KAAK,EAAE;IACpD9d,WAAW,CAAC,yEAAyE,CAAC;EACxF;EACA,MAAM+d,oBAAoB,GAAG,EAAE;EAC/B,MAAMC,mBAAmB,GAAG,EAAE;EAC9B,MAAM5C,IAAI,GAAGnO,WAAW,GAAGyB,IAAI,CAACe,IAAI,CAACjE,MAAM,CAAC1E,MAAM,GAAGd,MAAM,CAACkH,IAAI,CAACC,IAAI,CAAC,GAAG3B,MAAM,CAAC1E,MAAM;EACtF,MAAMmX,iBAAiB,GAAG7D,OAAO,IAAIgB,IAAI,GAAGN,YAAY,GAAGtN,aAAa,IAAI,CAACT,cAAc;EAC3F,IAAI8E,WAAW,GAAGoM,iBAAiB,GAAGnD,YAAY,GAAGzW,MAAM,CAACwN,WAAW;EACvE,IAAI,OAAOyF,gBAAgB,KAAK,WAAW,EAAE;IAC3CA,gBAAgB,GAAGjT,MAAM,CAAC2Y,aAAa,CAACxR,MAAM,CAACwJ,IAAI,CAACxP,EAAE,IAAIA,EAAE,CAACyL,SAAS,CAACC,QAAQ,CAAClL,MAAM,CAACkP,gBAAgB,CAAC,CAAC,CAAC;EAC5G,CAAC,MAAM;IACLrD,WAAW,GAAGyF,gBAAgB;EAChC;EACA,MAAM4G,MAAM,GAAGpE,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,MAAMqE,MAAM,GAAGrE,SAAS,KAAK,MAAM,IAAI,CAACA,SAAS;EACjD,IAAIsE,eAAe,GAAG,CAAC;EACvB,IAAIC,cAAc,GAAG,CAAC;EACtB,MAAMC,cAAc,GAAGrR,WAAW,GAAGzB,MAAM,CAAC8L,gBAAgB,CAAC,CAACrC,MAAM,GAAGqC,gBAAgB;EACvF,MAAMiH,uBAAuB,GAAGD,cAAc,IAAIvR,cAAc,IAAI,OAAOwL,YAAY,KAAK,WAAW,GAAG,CAAC/K,aAAa,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;EACvI;EACA,IAAI+Q,uBAAuB,GAAGxB,YAAY,EAAE;IAC1CqB,eAAe,GAAG1P,IAAI,CAACO,GAAG,CAAC8N,YAAY,GAAGwB,uBAAuB,EAAEzP,cAAc,CAAC;IAClF,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsc,YAAY,GAAGwB,uBAAuB,EAAE9d,CAAC,IAAI,CAAC,EAAE;MAClE,MAAM2I,KAAK,GAAG3I,CAAC,GAAGiO,IAAI,CAACC,KAAK,CAAClO,CAAC,GAAG2a,IAAI,CAAC,GAAGA,IAAI;MAC7C,IAAInO,WAAW,EAAE;QACf,MAAMuR,iBAAiB,GAAGpD,IAAI,GAAGhS,KAAK,GAAG,CAAC;QAC1C,KAAK,IAAI3I,CAAC,GAAG+K,MAAM,CAAC1E,MAAM,GAAG,CAAC,EAAErG,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC9C,IAAI+K,MAAM,CAAC/K,CAAC,CAAC,CAACwU,MAAM,KAAKuJ,iBAAiB,EAAET,oBAAoB,CAAC1W,IAAI,CAAC5G,CAAC,CAAC;QAC1E;QACA;QACA;QACA;MACF,CAAC,MAAM;QACLsd,oBAAoB,CAAC1W,IAAI,CAAC+T,IAAI,GAAGhS,KAAK,GAAG,CAAC,CAAC;MAC7C;IACF;EACF,CAAC,MAAM,IAAImV,uBAAuB,GAAG/Q,aAAa,GAAG4N,IAAI,GAAG2B,YAAY,EAAE;IACxEsB,cAAc,GAAG3P,IAAI,CAACO,GAAG,CAACsP,uBAAuB,IAAInD,IAAI,GAAG2B,YAAY,GAAG,CAAC,CAAC,EAAEjO,cAAc,CAAC;IAC9F,IAAImP,iBAAiB,EAAE;MACrBI,cAAc,GAAG3P,IAAI,CAACO,GAAG,CAACoP,cAAc,EAAE7Q,aAAa,GAAG4N,IAAI,GAAGN,YAAY,GAAG,CAAC,CAAC;IACpF;IACA,KAAK,IAAIra,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4d,cAAc,EAAE5d,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAM2I,KAAK,GAAG3I,CAAC,GAAGiO,IAAI,CAACC,KAAK,CAAClO,CAAC,GAAG2a,IAAI,CAAC,GAAGA,IAAI;MAC7C,IAAInO,WAAW,EAAE;QACfzB,MAAM,CAACrG,OAAO,CAAC,CAAC0I,KAAK,EAAEiC,UAAU,KAAK;UACpC,IAAIjC,KAAK,CAACoH,MAAM,KAAK7L,KAAK,EAAE4U,mBAAmB,CAAC3W,IAAI,CAACyI,UAAU,CAAC;QAClE,CAAC,CAAC;MACJ,CAAC,MAAM;QACLkO,mBAAmB,CAAC3W,IAAI,CAAC+B,KAAK,CAAC;MACjC;IACF;EACF;EACA/E,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACjC7B,qBAAqB,CAAC,MAAM;IAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;EACpC,CAAC,CAAC;EACF,IAAIxC,MAAM,CAAC2B,MAAM,CAACkJ,MAAM,KAAK,OAAO,IAAI1D,MAAM,CAAC1E,MAAM,GAAG0G,aAAa,GAAGuP,YAAY,GAAG,CAAC,EAAE;IACxF,IAAIiB,mBAAmB,CAACza,QAAQ,CAAC+T,gBAAgB,CAAC,EAAE;MAClD0G,mBAAmB,CAAClW,MAAM,CAACkW,mBAAmB,CAACnb,OAAO,CAACyU,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC9E;IACA,IAAIyG,oBAAoB,CAACxa,QAAQ,CAAC+T,gBAAgB,CAAC,EAAE;MACnDyG,oBAAoB,CAACjW,MAAM,CAACiW,oBAAoB,CAAClb,OAAO,CAACyU,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAChF;EACF;EACA,IAAI6G,MAAM,EAAE;IACVJ,oBAAoB,CAAC5Y,OAAO,CAACiE,KAAK,IAAI;MACpCoC,MAAM,CAACpC,KAAK,CAAC,CAACqV,iBAAiB,GAAG,IAAI;MACtC1T,QAAQ,CAAC2T,OAAO,CAAClT,MAAM,CAACpC,KAAK,CAAC,CAAC;MAC/BoC,MAAM,CAACpC,KAAK,CAAC,CAACqV,iBAAiB,GAAG,KAAK;IACzC,CAAC,CAAC;EACJ;EACA,IAAIP,MAAM,EAAE;IACVF,mBAAmB,CAAC7Y,OAAO,CAACiE,KAAK,IAAI;MACnCoC,MAAM,CAACpC,KAAK,CAAC,CAACqV,iBAAiB,GAAG,IAAI;MACtC1T,QAAQ,CAACyS,MAAM,CAAChS,MAAM,CAACpC,KAAK,CAAC,CAAC;MAC9BoC,MAAM,CAACpC,KAAK,CAAC,CAACqV,iBAAiB,GAAG,KAAK;IACzC,CAAC,CAAC;EACJ;EACApa,MAAM,CAACsZ,YAAY,CAAC,CAAC;EACrB,IAAI3X,MAAM,CAACwH,aAAa,KAAK,MAAM,EAAE;IACnCnJ,MAAM,CAACmG,YAAY,CAAC,CAAC;EACvB,CAAC,MAAM,IAAIyC,WAAW,KAAK8Q,oBAAoB,CAACjX,MAAM,GAAG,CAAC,IAAIqX,MAAM,IAAIH,mBAAmB,CAAClX,MAAM,GAAG,CAAC,IAAIoX,MAAM,CAAC,EAAE;IACjH7Z,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAAC,CAAC0I,KAAK,EAAEiC,UAAU,KAAK;MAC3CzL,MAAM,CAAC6I,IAAI,CAACY,WAAW,CAACgC,UAAU,EAAEjC,KAAK,EAAExJ,MAAM,CAACmH,MAAM,CAAC;IAC3D,CAAC,CAAC;EACJ;EACA,IAAIxF,MAAM,CAAC4K,mBAAmB,EAAE;IAC9BvM,MAAM,CAACwM,kBAAkB,CAAC,CAAC;EAC7B;EACA,IAAIsJ,OAAO,EAAE;IACX,IAAI4D,oBAAoB,CAACjX,MAAM,GAAG,CAAC,IAAIqX,MAAM,EAAE;MAC7C,IAAI,OAAO5C,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMoD,qBAAqB,GAAGta,MAAM,CAACuH,UAAU,CAACiG,WAAW,CAAC;QAC5D,MAAM+M,iBAAiB,GAAGva,MAAM,CAACuH,UAAU,CAACiG,WAAW,GAAGuM,eAAe,CAAC;QAC1E,MAAMS,IAAI,GAAGD,iBAAiB,GAAGD,qBAAqB;QACtD,IAAIf,YAAY,EAAE;UAChBvZ,MAAM,CAACkU,YAAY,CAAClU,MAAM,CAACmO,SAAS,GAAGqM,IAAI,CAAC;QAC9C,CAAC,MAAM;UACLxa,MAAM,CAAC8V,OAAO,CAACtI,WAAW,GAAGnD,IAAI,CAACe,IAAI,CAAC2O,eAAe,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UACxE,IAAI7F,YAAY,EAAE;YAChBlU,MAAM,CAACya,eAAe,CAACC,cAAc,GAAG1a,MAAM,CAACya,eAAe,CAACC,cAAc,GAAGF,IAAI;YACpFxa,MAAM,CAACya,eAAe,CAACxG,gBAAgB,GAAGjU,MAAM,CAACya,eAAe,CAACxG,gBAAgB,GAAGuG,IAAI;UAC1F;QACF;MACF,CAAC,MAAM;QACL,IAAItG,YAAY,EAAE;UAChB,MAAMyG,KAAK,GAAG/R,WAAW,GAAG8Q,oBAAoB,CAACjX,MAAM,GAAGd,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG4Q,oBAAoB,CAACjX,MAAM;UACxGzC,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,GAAGmN,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC1D3a,MAAM,CAACya,eAAe,CAACxG,gBAAgB,GAAGjU,MAAM,CAACmO,SAAS;QAC5D;MACF;IACF,CAAC,MAAM,IAAIwL,mBAAmB,CAAClX,MAAM,GAAG,CAAC,IAAIoX,MAAM,EAAE;MACnD,IAAI,OAAO3C,cAAc,KAAK,WAAW,EAAE;QACzC,MAAMoD,qBAAqB,GAAGta,MAAM,CAACuH,UAAU,CAACiG,WAAW,CAAC;QAC5D,MAAM+M,iBAAiB,GAAGva,MAAM,CAACuH,UAAU,CAACiG,WAAW,GAAGwM,cAAc,CAAC;QACzE,MAAMQ,IAAI,GAAGD,iBAAiB,GAAGD,qBAAqB;QACtD,IAAIf,YAAY,EAAE;UAChBvZ,MAAM,CAACkU,YAAY,CAAClU,MAAM,CAACmO,SAAS,GAAGqM,IAAI,CAAC;QAC9C,CAAC,MAAM;UACLxa,MAAM,CAAC8V,OAAO,CAACtI,WAAW,GAAGwM,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;UAC5D,IAAI9F,YAAY,EAAE;YAChBlU,MAAM,CAACya,eAAe,CAACC,cAAc,GAAG1a,MAAM,CAACya,eAAe,CAACC,cAAc,GAAGF,IAAI;YACpFxa,MAAM,CAACya,eAAe,CAACxG,gBAAgB,GAAGjU,MAAM,CAACya,eAAe,CAACxG,gBAAgB,GAAGuG,IAAI;UAC1F;QACF;MACF,CAAC,MAAM;QACL,MAAMG,KAAK,GAAG/R,WAAW,GAAG+Q,mBAAmB,CAAClX,MAAM,GAAGd,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG6Q,mBAAmB,CAAClX,MAAM;QACtGzC,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,GAAGmN,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAC5D;IACF;EACF;EACA3a,MAAM,CAACoW,cAAc,GAAGA,cAAc;EACtCpW,MAAM,CAACmW,cAAc,GAAGA,cAAc;EACtC,IAAInW,MAAM,CAAC4a,UAAU,IAAI5a,MAAM,CAAC4a,UAAU,CAACC,OAAO,IAAI,CAAC1G,YAAY,EAAE;IACnE,MAAM2G,UAAU,GAAG;MACjB5D,cAAc;MACdzB,SAAS;MACTvB,YAAY;MACZjB,gBAAgB;MAChBkB,YAAY,EAAE;IAChB,CAAC;IACD,IAAI1P,KAAK,CAACY,OAAO,CAACrF,MAAM,CAAC4a,UAAU,CAACC,OAAO,CAAC,EAAE;MAC5C7a,MAAM,CAAC4a,UAAU,CAACC,OAAO,CAAC/Z,OAAO,CAAClF,CAAC,IAAI;QACrC,IAAI,CAACA,CAAC,CAAC0E,SAAS,IAAI1E,CAAC,CAAC+F,MAAM,CAACuJ,IAAI,EAAEtP,CAAC,CAACqb,OAAO,CAAC;UAC3C,GAAG6D,UAAU;UACbhF,OAAO,EAAEla,CAAC,CAAC+F,MAAM,CAACwH,aAAa,KAAKxH,MAAM,CAACwH,aAAa,GAAG2M,OAAO,GAAG;QACvE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI9V,MAAM,CAAC4a,UAAU,CAACC,OAAO,YAAY7a,MAAM,CAAC+a,WAAW,IAAI/a,MAAM,CAAC4a,UAAU,CAACC,OAAO,CAAClZ,MAAM,CAACuJ,IAAI,EAAE;MAC3GlL,MAAM,CAAC4a,UAAU,CAACC,OAAO,CAAC5D,OAAO,CAAC;QAChC,GAAG6D,UAAU;QACbhF,OAAO,EAAE9V,MAAM,CAAC4a,UAAU,CAACC,OAAO,CAAClZ,MAAM,CAACwH,aAAa,KAAKxH,MAAM,CAACwH,aAAa,GAAG2M,OAAO,GAAG;MAC/F,CAAC,CAAC;IACJ;EACF;EACA9V,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;AACxB;AAEA,SAAS8a,WAAWA,CAAA,EAAG;EACrB,MAAMhb,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACN+E;EACF,CAAC,GAAG1G,MAAM;EACV,IAAI,CAAC2B,MAAM,CAACuJ,IAAI,IAAI,CAACxE,QAAQ,IAAI1G,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;EAClFjH,MAAM,CAACsZ,YAAY,CAAC,CAAC;EACrB,MAAM2B,cAAc,GAAG,EAAE;EACzBjb,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;IAC/B,MAAMtD,KAAK,GAAG,OAAOsD,OAAO,CAAC6S,gBAAgB,KAAK,WAAW,GAAG7S,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,GAAG7K,OAAO,CAAC6S,gBAAgB;IAC9ID,cAAc,CAAClW,KAAK,CAAC,GAAGsD,OAAO;EACjC,CAAC,CAAC;EACFrI,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;IAC/BA,OAAO,CAACqJ,eAAe,CAAC,yBAAyB,CAAC;EACpD,CAAC,CAAC;EACFuJ,cAAc,CAACna,OAAO,CAACuH,OAAO,IAAI;IAChC3B,QAAQ,CAACyS,MAAM,CAAC9Q,OAAO,CAAC;EAC1B,CAAC,CAAC;EACFrI,MAAM,CAACsZ,YAAY,CAAC,CAAC;EACrBtZ,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACqS,SAAS,EAAE,CAAC,CAAC;AACrC;AAEA,IAAInH,IAAI,GAAG;EACT0N,UAAU;EACV3B,OAAO;EACP+D;AACF,CAAC;AAED,SAASG,aAAaA,CAACC,MAAM,EAAE;EAC7B,MAAMpb,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAAC0Z,aAAa,IAAIrb,MAAM,CAAC2B,MAAM,CAAC0K,aAAa,IAAIrM,MAAM,CAACsb,QAAQ,IAAItb,MAAM,CAAC2B,MAAM,CAACgH,OAAO,EAAE;EAC7G,MAAMxH,EAAE,GAAGnB,MAAM,CAAC2B,MAAM,CAAC4Z,iBAAiB,KAAK,WAAW,GAAGvb,MAAM,CAACmB,EAAE,GAAGnB,MAAM,CAACsD,SAAS;EACzF,IAAItD,MAAM,CAAC8C,SAAS,EAAE;IACpB9C,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACnC;EACArB,EAAE,CAACrE,KAAK,CAAC0e,MAAM,GAAG,MAAM;EACxBra,EAAE,CAACrE,KAAK,CAAC0e,MAAM,GAAGJ,MAAM,GAAG,UAAU,GAAG,MAAM;EAC9C,IAAIpb,MAAM,CAAC8C,SAAS,EAAE;IACpBnC,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF;AAEA,SAASiZ,eAAeA,CAAA,EAAG;EACzB,MAAMzb,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAAC2B,MAAM,CAAC0K,aAAa,IAAIrM,MAAM,CAACsb,QAAQ,IAAItb,MAAM,CAAC2B,MAAM,CAACgH,OAAO,EAAE;IAC3E;EACF;EACA,IAAI3I,MAAM,CAAC8C,SAAS,EAAE;IACpB9C,MAAM,CAACwC,mBAAmB,GAAG,IAAI;EACnC;EACAxC,MAAM,CAACA,MAAM,CAAC2B,MAAM,CAAC4Z,iBAAiB,KAAK,WAAW,GAAG,IAAI,GAAG,WAAW,CAAC,CAACze,KAAK,CAAC0e,MAAM,GAAG,EAAE;EAC9F,IAAIxb,MAAM,CAAC8C,SAAS,EAAE;IACpBnC,qBAAqB,CAAC,MAAM;MAC1BX,MAAM,CAACwC,mBAAmB,GAAG,KAAK;IACpC,CAAC,CAAC;EACJ;AACF;AAEA,IAAIkZ,UAAU,GAAG;EACfP,aAAa;EACbM;AACF,CAAC;;AAED;AACA,SAASE,cAAcA,CAACpL,QAAQ,EAAEqL,IAAI,EAAE;EACtC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,IAAI;EACb;EACA,SAASC,aAAaA,CAAC1a,EAAE,EAAE;IACzB,IAAI,CAACA,EAAE,IAAIA,EAAE,KAAK7G,WAAW,CAAC,CAAC,IAAI6G,EAAE,KAAK/G,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI;IAClE,IAAI+G,EAAE,CAAC2a,YAAY,EAAE3a,EAAE,GAAGA,EAAE,CAAC2a,YAAY;IACzC,MAAMC,KAAK,GAAG5a,EAAE,CAACiQ,OAAO,CAACb,QAAQ,CAAC;IAClC,IAAI,CAACwL,KAAK,IAAI,CAAC5a,EAAE,CAAC6a,WAAW,EAAE;MAC7B,OAAO,IAAI;IACb;IACA,OAAOD,KAAK,IAAIF,aAAa,CAAC1a,EAAE,CAAC6a,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC;EACtD;EACA,OAAOJ,aAAa,CAACD,IAAI,CAAC;AAC5B;AACA,SAASM,gBAAgBA,CAAClc,MAAM,EAAEiE,KAAK,EAAEkY,MAAM,EAAE;EAC/C,MAAMzf,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAM;IACJuH;EACF,CAAC,GAAG3B,MAAM;EACV,MAAMoc,kBAAkB,GAAGza,MAAM,CAACya,kBAAkB;EACpD,MAAMC,kBAAkB,GAAG1a,MAAM,CAAC0a,kBAAkB;EACpD,IAAID,kBAAkB,KAAKD,MAAM,IAAIE,kBAAkB,IAAIF,MAAM,IAAIzf,MAAM,CAAC4f,UAAU,GAAGD,kBAAkB,CAAC,EAAE;IAC5G,IAAID,kBAAkB,KAAK,SAAS,EAAE;MACpCnY,KAAK,CAACsY,cAAc,CAAC,CAAC;MACtB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AACA,SAASC,YAAYA,CAACvY,KAAK,EAAE;EAC3B,MAAMjE,MAAM,GAAG,IAAI;EACnB,MAAMrD,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,IAAIK,CAAC,GAAGsJ,KAAK;EACb,IAAItJ,CAAC,CAAC8hB,aAAa,EAAE9hB,CAAC,GAAGA,CAAC,CAAC8hB,aAAa;EACxC,MAAMxX,IAAI,GAAGjF,MAAM,CAACya,eAAe;EACnC,IAAI9f,CAAC,CAAC+hB,IAAI,KAAK,aAAa,EAAE;IAC5B,IAAIzX,IAAI,CAAC0X,SAAS,KAAK,IAAI,IAAI1X,IAAI,CAAC0X,SAAS,KAAKhiB,CAAC,CAACgiB,SAAS,EAAE;MAC7D;IACF;IACA1X,IAAI,CAAC0X,SAAS,GAAGhiB,CAAC,CAACgiB,SAAS;EAC9B,CAAC,MAAM,IAAIhiB,CAAC,CAAC+hB,IAAI,KAAK,YAAY,IAAI/hB,CAAC,CAACiiB,aAAa,CAACna,MAAM,KAAK,CAAC,EAAE;IAClEwC,IAAI,CAAC4X,OAAO,GAAGliB,CAAC,CAACiiB,aAAa,CAAC,CAAC,CAAC,CAACE,UAAU;EAC9C;EACA,IAAIniB,CAAC,CAAC+hB,IAAI,KAAK,YAAY,EAAE;IAC3B;IACAR,gBAAgB,CAAClc,MAAM,EAAErF,CAAC,EAAEA,CAAC,CAACiiB,aAAa,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC;IACrD;EACF;EACA,MAAM;IACJpb,MAAM;IACNqb,OAAO;IACP/V;EACF,CAAC,GAAGjH,MAAM;EACV,IAAI,CAACiH,OAAO,EAAE;EACd,IAAI,CAACtF,MAAM,CAAC0Z,aAAa,IAAI1gB,CAAC,CAACsiB,WAAW,KAAK,OAAO,EAAE;EACxD,IAAIjd,MAAM,CAAC2U,SAAS,IAAIhT,MAAM,CAACiT,8BAA8B,EAAE;IAC7D;EACF;EACA,IAAI,CAAC5U,MAAM,CAAC2U,SAAS,IAAIhT,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAACuJ,IAAI,EAAE;IACtDlL,MAAM,CAACiX,OAAO,CAAC,CAAC;EAClB;EACA,IAAIiG,QAAQ,GAAGviB,CAAC,CAACuG,MAAM;EACvB,IAAIS,MAAM,CAAC4Z,iBAAiB,KAAK,SAAS,EAAE;IAC1C,IAAI,CAACxf,gBAAgB,CAACmhB,QAAQ,EAAEld,MAAM,CAACsD,SAAS,CAAC,EAAE;EACrD;EACA,IAAI,OAAO,IAAI3I,CAAC,IAAIA,CAAC,CAACwiB,KAAK,KAAK,CAAC,EAAE;EACnC,IAAI,QAAQ,IAAIxiB,CAAC,IAAIA,CAAC,CAACyiB,MAAM,GAAG,CAAC,EAAE;EACnC,IAAInY,IAAI,CAACoY,SAAS,IAAIpY,IAAI,CAACqY,OAAO,EAAE;;EAEpC;EACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC5b,MAAM,CAAC6b,cAAc,IAAI7b,MAAM,CAAC6b,cAAc,KAAK,EAAE;EACpF;EACA,MAAMC,SAAS,GAAG9iB,CAAC,CAAC+iB,YAAY,GAAG/iB,CAAC,CAAC+iB,YAAY,CAAC,CAAC,GAAG/iB,CAAC,CAAC0Y,IAAI;EAC5D,IAAIkK,oBAAoB,IAAI5iB,CAAC,CAACuG,MAAM,IAAIvG,CAAC,CAACuG,MAAM,CAACsQ,UAAU,IAAIiM,SAAS,EAAE;IACxEP,QAAQ,GAAGO,SAAS,CAAC,CAAC,CAAC;EACzB;EACA,MAAME,iBAAiB,GAAGhc,MAAM,CAACgc,iBAAiB,GAAGhc,MAAM,CAACgc,iBAAiB,GAAG,IAAIhc,MAAM,CAAC6b,cAAc,EAAE;EAC3G,MAAMI,cAAc,GAAG,CAAC,EAAEjjB,CAAC,CAACuG,MAAM,IAAIvG,CAAC,CAACuG,MAAM,CAACsQ,UAAU,CAAC;;EAE1D;EACA,IAAI7P,MAAM,CAACkc,SAAS,KAAKD,cAAc,GAAGjC,cAAc,CAACgC,iBAAiB,EAAET,QAAQ,CAAC,GAAGA,QAAQ,CAAC9L,OAAO,CAACuM,iBAAiB,CAAC,CAAC,EAAE;IAC5H3d,MAAM,CAAC8d,UAAU,GAAG,IAAI;IACxB;EACF;EACA,IAAInc,MAAM,CAACoc,YAAY,EAAE;IACvB,IAAI,CAACb,QAAQ,CAAC9L,OAAO,CAACzP,MAAM,CAACoc,YAAY,CAAC,EAAE;EAC9C;EACAf,OAAO,CAACgB,QAAQ,GAAGrjB,CAAC,CAACoiB,KAAK;EAC1BC,OAAO,CAACiB,QAAQ,GAAGtjB,CAAC,CAACujB,KAAK;EAC1B,MAAM/B,MAAM,GAAGa,OAAO,CAACgB,QAAQ;EAC/B,MAAMG,MAAM,GAAGnB,OAAO,CAACiB,QAAQ;;EAE/B;;EAEA,IAAI,CAAC/B,gBAAgB,CAAClc,MAAM,EAAErF,CAAC,EAAEwhB,MAAM,CAAC,EAAE;IACxC;EACF;EACAnW,MAAM,CAACC,MAAM,CAAChB,IAAI,EAAE;IAClBoY,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,KAAK;IACdc,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAE1K,SAAS;IACtB2K,WAAW,EAAE3K;EACf,CAAC,CAAC;EACFqJ,OAAO,CAACb,MAAM,GAAGA,MAAM;EACvBa,OAAO,CAACmB,MAAM,GAAGA,MAAM;EACvBlZ,IAAI,CAACsZ,cAAc,GAAGtiB,GAAG,CAAC,CAAC;EAC3B+D,MAAM,CAAC8d,UAAU,GAAG,IAAI;EACxB9d,MAAM,CAACyF,UAAU,CAAC,CAAC;EACnBzF,MAAM,CAACwe,cAAc,GAAG7K,SAAS;EACjC,IAAIhS,MAAM,CAAC2W,SAAS,GAAG,CAAC,EAAErT,IAAI,CAACwZ,kBAAkB,GAAG,KAAK;EACzD,IAAIlC,cAAc,GAAG,IAAI;EACzB,IAAIW,QAAQ,CAAC3J,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,EAAE;IAC5CnC,cAAc,GAAG,KAAK;IACtB,IAAIW,QAAQ,CAACyB,QAAQ,KAAK,QAAQ,EAAE;MAClC1Z,IAAI,CAACoY,SAAS,GAAG,KAAK;IACxB;EACF;EACA,IAAI1gB,QAAQ,CAACiiB,aAAa,IAAIjiB,QAAQ,CAACiiB,aAAa,CAACrL,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,IAAI/hB,QAAQ,CAACiiB,aAAa,KAAK1B,QAAQ,KAAKviB,CAAC,CAACsiB,WAAW,KAAK,OAAO,IAAItiB,CAAC,CAACsiB,WAAW,KAAK,OAAO,IAAI,CAACC,QAAQ,CAAC3J,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,CAAC,EAAE;IACpO/hB,QAAQ,CAACiiB,aAAa,CAACC,IAAI,CAAC,CAAC;EAC/B;EACA,MAAMC,oBAAoB,GAAGvC,cAAc,IAAIvc,MAAM,CAAC+e,cAAc,IAAIpd,MAAM,CAACqd,wBAAwB;EACvG,IAAI,CAACrd,MAAM,CAACsd,6BAA6B,IAAIH,oBAAoB,KAAK,CAAC5B,QAAQ,CAACgC,iBAAiB,EAAE;IACjGvkB,CAAC,CAAC4hB,cAAc,CAAC,CAAC;EACpB;EACA,IAAI5a,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO,IAAIjH,MAAM,CAAC+X,QAAQ,IAAI/X,MAAM,CAAC2U,SAAS,IAAI,CAAChT,MAAM,CAACgH,OAAO,EAAE;IACxG3I,MAAM,CAAC+X,QAAQ,CAACyE,YAAY,CAAC,CAAC;EAChC;EACAxc,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEvF,CAAC,CAAC;AAC9B;AAEA,SAASwkB,WAAWA,CAAClb,KAAK,EAAE;EAC1B,MAAMtH,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM0F,MAAM,GAAG,IAAI;EACnB,MAAMiF,IAAI,GAAGjF,MAAM,CAACya,eAAe;EACnC,MAAM;IACJ9Y,MAAM;IACNqb,OAAO;IACPpW,YAAY,EAAEC,GAAG;IACjBI;EACF,CAAC,GAAGjH,MAAM;EACV,IAAI,CAACiH,OAAO,EAAE;EACd,IAAI,CAACtF,MAAM,CAAC0Z,aAAa,IAAIpX,KAAK,CAACgZ,WAAW,KAAK,OAAO,EAAE;EAC5D,IAAItiB,CAAC,GAAGsJ,KAAK;EACb,IAAItJ,CAAC,CAAC8hB,aAAa,EAAE9hB,CAAC,GAAGA,CAAC,CAAC8hB,aAAa;EACxC,IAAI9hB,CAAC,CAAC+hB,IAAI,KAAK,aAAa,EAAE;IAC5B,IAAIzX,IAAI,CAAC4X,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;IACnC,MAAMuC,EAAE,GAAGzkB,CAAC,CAACgiB,SAAS;IACtB,IAAIyC,EAAE,KAAKna,IAAI,CAAC0X,SAAS,EAAE;EAC7B;EACA,IAAI0C,WAAW;EACf,IAAI1kB,CAAC,CAAC+hB,IAAI,KAAK,WAAW,EAAE;IAC1B2C,WAAW,GAAG,CAAC,GAAG1kB,CAAC,CAAC2kB,cAAc,CAAC,CAAC3O,IAAI,CAACzV,CAAC,IAAIA,CAAC,CAAC4hB,UAAU,KAAK7X,IAAI,CAAC4X,OAAO,CAAC;IAC5E,IAAI,CAACwC,WAAW,IAAIA,WAAW,CAACvC,UAAU,KAAK7X,IAAI,CAAC4X,OAAO,EAAE;EAC/D,CAAC,MAAM;IACLwC,WAAW,GAAG1kB,CAAC;EACjB;EACA,IAAI,CAACsK,IAAI,CAACoY,SAAS,EAAE;IACnB,IAAIpY,IAAI,CAACqZ,WAAW,IAAIrZ,IAAI,CAACoZ,WAAW,EAAE;MACxCre,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEvF,CAAC,CAAC;IACrC;IACA;EACF;EACA,MAAMoiB,KAAK,GAAGsC,WAAW,CAACtC,KAAK;EAC/B,MAAMmB,KAAK,GAAGmB,WAAW,CAACnB,KAAK;EAC/B,IAAIvjB,CAAC,CAAC4kB,uBAAuB,EAAE;IAC7BvC,OAAO,CAACb,MAAM,GAAGY,KAAK;IACtBC,OAAO,CAACmB,MAAM,GAAGD,KAAK;IACtB;EACF;EACA,IAAI,CAACle,MAAM,CAAC+e,cAAc,EAAE;IAC1B,IAAI,CAACpkB,CAAC,CAACuG,MAAM,CAACqS,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,EAAE;MAC7C1e,MAAM,CAAC8d,UAAU,GAAG,KAAK;IAC3B;IACA,IAAI7Y,IAAI,CAACoY,SAAS,EAAE;MAClBrX,MAAM,CAACC,MAAM,CAAC+W,OAAO,EAAE;QACrBb,MAAM,EAAEY,KAAK;QACboB,MAAM,EAAED,KAAK;QACbF,QAAQ,EAAEjB,KAAK;QACfkB,QAAQ,EAAEC;MACZ,CAAC,CAAC;MACFjZ,IAAI,CAACsZ,cAAc,GAAGtiB,GAAG,CAAC,CAAC;IAC7B;IACA;EACF;EACA,IAAI0F,MAAM,CAAC6d,mBAAmB,IAAI,CAAC7d,MAAM,CAACuJ,IAAI,EAAE;IAC9C,IAAIlL,MAAM,CAAC6F,UAAU,CAAC,CAAC,EAAE;MACvB;MACA,IAAIqY,KAAK,GAAGlB,OAAO,CAACmB,MAAM,IAAIne,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACqP,YAAY,CAAC,CAAC,IAAI6O,KAAK,GAAGlB,OAAO,CAACmB,MAAM,IAAIne,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACwO,YAAY,CAAC,CAAC,EAAE;QAC9IvJ,IAAI,CAACoY,SAAS,GAAG,KAAK;QACtBpY,IAAI,CAACqY,OAAO,GAAG,KAAK;QACpB;MACF;IACF,CAAC,MAAM,IAAIzW,GAAG,KAAKkW,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAI,CAACnc,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACqP,YAAY,CAAC,CAAC,IAAI0N,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAI,CAACnc,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC,EAAE;MAChK;IACF,CAAC,MAAM,IAAI,CAAC3H,GAAG,KAAKkW,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAInc,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACqP,YAAY,CAAC,CAAC,IAAI0N,KAAK,GAAGC,OAAO,CAACb,MAAM,IAAInc,MAAM,CAACmO,SAAS,IAAInO,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC/J;IACF;EACF;EACA,IAAI7R,QAAQ,CAACiiB,aAAa,IAAIjiB,QAAQ,CAACiiB,aAAa,CAACrL,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,IAAI/hB,QAAQ,CAACiiB,aAAa,KAAKjkB,CAAC,CAACuG,MAAM,IAAIvG,CAAC,CAACsiB,WAAW,KAAK,OAAO,EAAE;IACxJtgB,QAAQ,CAACiiB,aAAa,CAACC,IAAI,CAAC,CAAC;EAC/B;EACA,IAAIliB,QAAQ,CAACiiB,aAAa,EAAE;IAC1B,IAAIjkB,CAAC,CAACuG,MAAM,KAAKvE,QAAQ,CAACiiB,aAAa,IAAIjkB,CAAC,CAACuG,MAAM,CAACqS,OAAO,CAACtO,IAAI,CAACyZ,iBAAiB,CAAC,EAAE;MACnFzZ,IAAI,CAACqY,OAAO,GAAG,IAAI;MACnBtd,MAAM,CAAC8d,UAAU,GAAG,KAAK;MACzB;IACF;EACF;EACA,IAAI7Y,IAAI,CAACmZ,mBAAmB,EAAE;IAC5Bpe,MAAM,CAACE,IAAI,CAAC,WAAW,EAAEvF,CAAC,CAAC;EAC7B;EACAqiB,OAAO,CAACyC,SAAS,GAAGzC,OAAO,CAACgB,QAAQ;EACpChB,OAAO,CAAC0C,SAAS,GAAG1C,OAAO,CAACiB,QAAQ;EACpCjB,OAAO,CAACgB,QAAQ,GAAGjB,KAAK;EACxBC,OAAO,CAACiB,QAAQ,GAAGC,KAAK;EACxB,MAAMyB,KAAK,GAAG3C,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACb,MAAM;EAC/C,MAAMyD,KAAK,GAAG5C,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAACmB,MAAM;EAC/C,IAAIne,MAAM,CAAC2B,MAAM,CAAC2W,SAAS,IAAIjO,IAAI,CAACwV,IAAI,CAACF,KAAK,IAAI,CAAC,GAAGC,KAAK,IAAI,CAAC,CAAC,GAAG5f,MAAM,CAAC2B,MAAM,CAAC2W,SAAS,EAAE;EAC7F,IAAI,OAAOrT,IAAI,CAACoZ,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAIyB,UAAU;IACd,IAAI9f,MAAM,CAAC4F,YAAY,CAAC,CAAC,IAAIoX,OAAO,CAACiB,QAAQ,KAAKjB,OAAO,CAACmB,MAAM,IAAIne,MAAM,CAAC6F,UAAU,CAAC,CAAC,IAAImX,OAAO,CAACgB,QAAQ,KAAKhB,OAAO,CAACb,MAAM,EAAE;MAC9HlX,IAAI,CAACoZ,WAAW,GAAG,KAAK;IAC1B,CAAC,MAAM;MACL;MACA,IAAIsB,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,IAAI,EAAE,EAAE;QACvCE,UAAU,GAAGzV,IAAI,CAAC0V,KAAK,CAAC1V,IAAI,CAACG,GAAG,CAACoV,KAAK,CAAC,EAAEvV,IAAI,CAACG,GAAG,CAACmV,KAAK,CAAC,CAAC,GAAG,GAAG,GAAGtV,IAAI,CAAC2V,EAAE;QACzE/a,IAAI,CAACoZ,WAAW,GAAGre,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAGka,UAAU,GAAGne,MAAM,CAACme,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAGne,MAAM,CAACme,UAAU;MACjH;IACF;EACF;EACA,IAAI7a,IAAI,CAACoZ,WAAW,EAAE;IACpBre,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEvF,CAAC,CAAC;EACrC;EACA,IAAI,OAAOsK,IAAI,CAACqZ,WAAW,KAAK,WAAW,EAAE;IAC3C,IAAItB,OAAO,CAACgB,QAAQ,KAAKhB,OAAO,CAACb,MAAM,IAAIa,OAAO,CAACiB,QAAQ,KAAKjB,OAAO,CAACmB,MAAM,EAAE;MAC9ElZ,IAAI,CAACqZ,WAAW,GAAG,IAAI;IACzB;EACF;EACA,IAAIrZ,IAAI,CAACoZ,WAAW,IAAI1jB,CAAC,CAAC+hB,IAAI,KAAK,WAAW,IAAIzX,IAAI,CAACgb,+BAA+B,EAAE;IACtFhb,IAAI,CAACoY,SAAS,GAAG,KAAK;IACtB;EACF;EACA,IAAI,CAACpY,IAAI,CAACqZ,WAAW,EAAE;IACrB;EACF;EACAte,MAAM,CAAC8d,UAAU,GAAG,KAAK;EACzB,IAAI,CAACnc,MAAM,CAACgH,OAAO,IAAIhO,CAAC,CAACulB,UAAU,EAAE;IACnCvlB,CAAC,CAAC4hB,cAAc,CAAC,CAAC;EACpB;EACA,IAAI5a,MAAM,CAACwe,wBAAwB,IAAI,CAACxe,MAAM,CAACye,MAAM,EAAE;IACrDzlB,CAAC,CAAC0lB,eAAe,CAAC,CAAC;EACrB;EACA,IAAI7F,IAAI,GAAGxa,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAG+Z,KAAK,GAAGC,KAAK;EAChD,IAAIU,WAAW,GAAGtgB,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAGoX,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACyC,SAAS,GAAGzC,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAAC0C,SAAS;EACrH,IAAI/d,MAAM,CAAC4e,cAAc,EAAE;IACzB/F,IAAI,GAAGnQ,IAAI,CAACG,GAAG,CAACgQ,IAAI,CAAC,IAAI3T,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACtCyZ,WAAW,GAAGjW,IAAI,CAACG,GAAG,CAAC8V,WAAW,CAAC,IAAIzZ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACtD;EACAmW,OAAO,CAACxC,IAAI,GAAGA,IAAI;EACnBA,IAAI,IAAI7Y,MAAM,CAAC6e,UAAU;EACzB,IAAI3Z,GAAG,EAAE;IACP2T,IAAI,GAAG,CAACA,IAAI;IACZ8F,WAAW,GAAG,CAACA,WAAW;EAC5B;EACA,MAAMG,oBAAoB,GAAGzgB,MAAM,CAAC0gB,gBAAgB;EACpD1gB,MAAM,CAACwe,cAAc,GAAGhE,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAClDxa,MAAM,CAAC0gB,gBAAgB,GAAGJ,WAAW,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;EAC3D,MAAMK,MAAM,GAAG3gB,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,IAAI,CAACvJ,MAAM,CAACgH,OAAO;EACpD,MAAMiY,YAAY,GAAG5gB,MAAM,CAAC0gB,gBAAgB,KAAK,MAAM,IAAI1gB,MAAM,CAACmW,cAAc,IAAInW,MAAM,CAAC0gB,gBAAgB,KAAK,MAAM,IAAI1gB,MAAM,CAACoW,cAAc;EAC/I,IAAI,CAACnR,IAAI,CAACqY,OAAO,EAAE;IACjB,IAAIqD,MAAM,IAAIC,YAAY,EAAE;MAC1B5gB,MAAM,CAACiX,OAAO,CAAC;QACbxB,SAAS,EAAEzV,MAAM,CAACwe;MACpB,CAAC,CAAC;IACJ;IACAvZ,IAAI,CAACyV,cAAc,GAAG1a,MAAM,CAAC3E,YAAY,CAAC,CAAC;IAC3C2E,MAAM,CAACoN,aAAa,CAAC,CAAC,CAAC;IACvB,IAAIpN,MAAM,CAAC2U,SAAS,EAAE;MACpB,MAAMkM,GAAG,GAAG,IAAInkB,MAAM,CAACokB,WAAW,CAAC,eAAe,EAAE;QAClDC,OAAO,EAAE,IAAI;QACbb,UAAU,EAAE,IAAI;QAChBc,MAAM,EAAE;UACNC,iBAAiB,EAAE;QACrB;MACF,CAAC,CAAC;MACFjhB,MAAM,CAACsD,SAAS,CAAC4d,aAAa,CAACL,GAAG,CAAC;IACrC;IACA5b,IAAI,CAACkc,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAIxf,MAAM,CAAC+Z,UAAU,KAAK1b,MAAM,CAACmW,cAAc,KAAK,IAAI,IAAInW,MAAM,CAACoW,cAAc,KAAK,IAAI,CAAC,EAAE;MAC3FpW,MAAM,CAACmb,aAAa,CAAC,IAAI,CAAC;IAC5B;IACAnb,MAAM,CAACE,IAAI,CAAC,iBAAiB,EAAEvF,CAAC,CAAC;EACnC;EACA,IAAIymB,SAAS;EACb,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACpB,IAAI3f,MAAM,CAAC4f,cAAc,KAAK,KAAK,IAAItc,IAAI,CAACqY,OAAO,IAAIrY,IAAI,CAACwZ,kBAAkB,IAAIgC,oBAAoB,KAAKzgB,MAAM,CAAC0gB,gBAAgB,IAAIC,MAAM,IAAIC,YAAY,IAAIvW,IAAI,CAACG,GAAG,CAACgQ,IAAI,CAAC,IAAI,CAAC,EAAE;IACnLxU,MAAM,CAACC,MAAM,CAAC+W,OAAO,EAAE;MACrBb,MAAM,EAAEY,KAAK;MACboB,MAAM,EAAED,KAAK;MACbF,QAAQ,EAAEjB,KAAK;MACfkB,QAAQ,EAAEC,KAAK;MACfxD,cAAc,EAAEzV,IAAI,CAACgP;IACvB,CAAC,CAAC;IACFhP,IAAI,CAACuc,aAAa,GAAG,IAAI;IACzBvc,IAAI,CAACyV,cAAc,GAAGzV,IAAI,CAACgP,gBAAgB;IAC3C;EACF;EACAjU,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEvF,CAAC,CAAC;EAC5BsK,IAAI,CAACqY,OAAO,GAAG,IAAI;EACnBrY,IAAI,CAACgP,gBAAgB,GAAGuG,IAAI,GAAGvV,IAAI,CAACyV,cAAc;EAClD,IAAI+G,mBAAmB,GAAG,IAAI;EAC9B,IAAIC,eAAe,GAAG/f,MAAM,CAAC+f,eAAe;EAC5C,IAAI/f,MAAM,CAAC6d,mBAAmB,EAAE;IAC9BkC,eAAe,GAAG,CAAC;EACrB;EACA,IAAIlH,IAAI,GAAG,CAAC,EAAE;IACZ,IAAImG,MAAM,IAAIC,YAAY,IAAI,CAACQ,SAAS,IAAInc,IAAI,CAACwZ,kBAAkB,IAAIxZ,IAAI,CAACgP,gBAAgB,IAAItS,MAAM,CAAC+G,cAAc,GAAG1I,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAGxO,MAAM,CAACwH,eAAe,CAACxH,MAAM,CAACwN,WAAW,GAAG,CAAC,CAAC,IAAI7L,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAInJ,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGd,MAAM,CAACwH,aAAa,IAAI,CAAC,GAAGnJ,MAAM,CAACwH,eAAe,CAACxH,MAAM,CAACwN,WAAW,GAAG,CAAC,CAAC,GAAGxN,MAAM,CAAC2B,MAAM,CAACqG,YAAY,GAAG,CAAC,CAAC,GAAGhI,MAAM,CAAC2B,MAAM,CAACqG,YAAY,GAAGhI,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC,EAAE;MAC9ZxO,MAAM,CAACiX,OAAO,CAAC;QACbxB,SAAS,EAAE,MAAM;QACjBvB,YAAY,EAAE,IAAI;QAClBjB,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IACA,IAAIhO,IAAI,CAACgP,gBAAgB,GAAGjU,MAAM,CAACwO,YAAY,CAAC,CAAC,EAAE;MACjDiT,mBAAmB,GAAG,KAAK;MAC3B,IAAI9f,MAAM,CAACggB,UAAU,EAAE;QACrB1c,IAAI,CAACgP,gBAAgB,GAAGjU,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAACxO,MAAM,CAACwO,YAAY,CAAC,CAAC,GAAGvJ,IAAI,CAACyV,cAAc,GAAGF,IAAI,KAAKkH,eAAe;MAC9H;IACF;EACF,CAAC,MAAM,IAAIlH,IAAI,GAAG,CAAC,EAAE;IACnB,IAAImG,MAAM,IAAIC,YAAY,IAAI,CAACQ,SAAS,IAAInc,IAAI,CAACwZ,kBAAkB,IAAIxZ,IAAI,CAACgP,gBAAgB,IAAItS,MAAM,CAAC+G,cAAc,GAAG1I,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAGrP,MAAM,CAACwH,eAAe,CAACxH,MAAM,CAACwH,eAAe,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAGzC,MAAM,CAAC2B,MAAM,CAACqG,YAAY,IAAIrG,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAInJ,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAGd,MAAM,CAACwH,aAAa,IAAI,CAAC,GAAGnJ,MAAM,CAACwH,eAAe,CAACxH,MAAM,CAACwH,eAAe,CAAC/E,MAAM,GAAG,CAAC,CAAC,GAAGzC,MAAM,CAAC2B,MAAM,CAACqG,YAAY,GAAG,CAAC,CAAC,GAAGhI,MAAM,CAACqP,YAAY,CAAC,CAAC,CAAC,EAAE;MACpbrP,MAAM,CAACiX,OAAO,CAAC;QACbxB,SAAS,EAAE,MAAM;QACjBvB,YAAY,EAAE,IAAI;QAClBjB,gBAAgB,EAAEjT,MAAM,CAACmH,MAAM,CAAC1E,MAAM,IAAId,MAAM,CAACwH,aAAa,KAAK,MAAM,GAAGnJ,MAAM,CAAC+R,oBAAoB,CAAC,CAAC,GAAG1H,IAAI,CAACe,IAAI,CAAC7E,UAAU,CAAC5E,MAAM,CAACwH,aAAa,EAAE,EAAE,CAAC,CAAC;MAC7J,CAAC,CAAC;IACJ;IACA,IAAIlE,IAAI,CAACgP,gBAAgB,GAAGjU,MAAM,CAACqP,YAAY,CAAC,CAAC,EAAE;MACjDoS,mBAAmB,GAAG,KAAK;MAC3B,IAAI9f,MAAM,CAACggB,UAAU,EAAE;QACrB1c,IAAI,CAACgP,gBAAgB,GAAGjU,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAACrP,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAGpK,IAAI,CAACyV,cAAc,GAAGF,IAAI,KAAKkH,eAAe;MAC7H;IACF;EACF;EACA,IAAID,mBAAmB,EAAE;IACvB9mB,CAAC,CAAC4kB,uBAAuB,GAAG,IAAI;EAClC;;EAEA;EACA,IAAI,CAACvf,MAAM,CAACmW,cAAc,IAAInW,MAAM,CAACwe,cAAc,KAAK,MAAM,IAAIvZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc,EAAE;IAC7GzV,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc;EAC7C;EACA,IAAI,CAAC1a,MAAM,CAACoW,cAAc,IAAIpW,MAAM,CAACwe,cAAc,KAAK,MAAM,IAAIvZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc,EAAE;IAC7GzV,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc;EAC7C;EACA,IAAI,CAAC1a,MAAM,CAACoW,cAAc,IAAI,CAACpW,MAAM,CAACmW,cAAc,EAAE;IACpDlR,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc;EAC7C;;EAEA;EACA,IAAI/Y,MAAM,CAAC2W,SAAS,GAAG,CAAC,EAAE;IACxB,IAAIjO,IAAI,CAACG,GAAG,CAACgQ,IAAI,CAAC,GAAG7Y,MAAM,CAAC2W,SAAS,IAAIrT,IAAI,CAACwZ,kBAAkB,EAAE;MAChE,IAAI,CAACxZ,IAAI,CAACwZ,kBAAkB,EAAE;QAC5BxZ,IAAI,CAACwZ,kBAAkB,GAAG,IAAI;QAC9BzB,OAAO,CAACb,MAAM,GAAGa,OAAO,CAACgB,QAAQ;QACjChB,OAAO,CAACmB,MAAM,GAAGnB,OAAO,CAACiB,QAAQ;QACjChZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc;QAC3CsC,OAAO,CAACxC,IAAI,GAAGxa,MAAM,CAAC4F,YAAY,CAAC,CAAC,GAAGoX,OAAO,CAACgB,QAAQ,GAAGhB,OAAO,CAACb,MAAM,GAAGa,OAAO,CAACiB,QAAQ,GAAGjB,OAAO,CAACmB,MAAM;QAC5G;MACF;IACF,CAAC,MAAM;MACLlZ,IAAI,CAACgP,gBAAgB,GAAGhP,IAAI,CAACyV,cAAc;MAC3C;IACF;EACF;EACA,IAAI,CAAC/Y,MAAM,CAACigB,YAAY,IAAIjgB,MAAM,CAACgH,OAAO,EAAE;;EAE5C;EACA,IAAIhH,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO,IAAIjH,MAAM,CAAC+X,QAAQ,IAAIpW,MAAM,CAAC4K,mBAAmB,EAAE;IAC/FvM,MAAM,CAACwS,iBAAiB,CAAC,CAAC;IAC1BxS,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;EAC9B;EACA,IAAI1O,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO,IAAIjH,MAAM,CAAC+X,QAAQ,EAAE;IACjE/X,MAAM,CAAC+X,QAAQ,CAACoH,WAAW,CAAC,CAAC;EAC/B;EACA;EACAnf,MAAM,CAACkP,cAAc,CAACjK,IAAI,CAACgP,gBAAgB,CAAC;EAC5C;EACAjU,MAAM,CAACkU,YAAY,CAACjP,IAAI,CAACgP,gBAAgB,CAAC;AAC5C;AAEA,SAAS4N,UAAUA,CAAC5d,KAAK,EAAE;EACzB,MAAMjE,MAAM,GAAG,IAAI;EACnB,MAAMiF,IAAI,GAAGjF,MAAM,CAACya,eAAe;EACnC,IAAI9f,CAAC,GAAGsJ,KAAK;EACb,IAAItJ,CAAC,CAAC8hB,aAAa,EAAE9hB,CAAC,GAAGA,CAAC,CAAC8hB,aAAa;EACxC,IAAI4C,WAAW;EACf,MAAMyC,YAAY,GAAGnnB,CAAC,CAAC+hB,IAAI,KAAK,UAAU,IAAI/hB,CAAC,CAAC+hB,IAAI,KAAK,aAAa;EACtE,IAAI,CAACoF,YAAY,EAAE;IACjB,IAAI7c,IAAI,CAAC4X,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC;IACnC,IAAIliB,CAAC,CAACgiB,SAAS,KAAK1X,IAAI,CAAC0X,SAAS,EAAE;IACpC0C,WAAW,GAAG1kB,CAAC;EACjB,CAAC,MAAM;IACL0kB,WAAW,GAAG,CAAC,GAAG1kB,CAAC,CAAC2kB,cAAc,CAAC,CAAC3O,IAAI,CAACzV,CAAC,IAAIA,CAAC,CAAC4hB,UAAU,KAAK7X,IAAI,CAAC4X,OAAO,CAAC;IAC5E,IAAI,CAACwC,WAAW,IAAIA,WAAW,CAACvC,UAAU,KAAK7X,IAAI,CAAC4X,OAAO,EAAE;EAC/D;EACA,IAAI,CAAC,eAAe,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC3d,QAAQ,CAACvE,CAAC,CAAC+hB,IAAI,CAAC,EAAE;IACnF,MAAMqF,OAAO,GAAG,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC7iB,QAAQ,CAACvE,CAAC,CAAC+hB,IAAI,CAAC,KAAK1c,MAAM,CAACpB,OAAO,CAACG,QAAQ,IAAIiB,MAAM,CAACpB,OAAO,CAACa,SAAS,CAAC;IAC1H,IAAI,CAACsiB,OAAO,EAAE;MACZ;IACF;EACF;EACA9c,IAAI,CAAC0X,SAAS,GAAG,IAAI;EACrB1X,IAAI,CAAC4X,OAAO,GAAG,IAAI;EACnB,MAAM;IACJlb,MAAM;IACNqb,OAAO;IACPpW,YAAY,EAAEC,GAAG;IACjBU,UAAU;IACVN;EACF,CAAC,GAAGjH,MAAM;EACV,IAAI,CAACiH,OAAO,EAAE;EACd,IAAI,CAACtF,MAAM,CAAC0Z,aAAa,IAAI1gB,CAAC,CAACsiB,WAAW,KAAK,OAAO,EAAE;EACxD,IAAIhY,IAAI,CAACmZ,mBAAmB,EAAE;IAC5Bpe,MAAM,CAACE,IAAI,CAAC,UAAU,EAAEvF,CAAC,CAAC;EAC5B;EACAsK,IAAI,CAACmZ,mBAAmB,GAAG,KAAK;EAChC,IAAI,CAACnZ,IAAI,CAACoY,SAAS,EAAE;IACnB,IAAIpY,IAAI,CAACqY,OAAO,IAAI3b,MAAM,CAAC+Z,UAAU,EAAE;MACrC1b,MAAM,CAACmb,aAAa,CAAC,KAAK,CAAC;IAC7B;IACAlW,IAAI,CAACqY,OAAO,GAAG,KAAK;IACpBrY,IAAI,CAACqZ,WAAW,GAAG,KAAK;IACxB;EACF;;EAEA;EACA,IAAI3c,MAAM,CAAC+Z,UAAU,IAAIzW,IAAI,CAACqY,OAAO,IAAIrY,IAAI,CAACoY,SAAS,KAAKrd,MAAM,CAACmW,cAAc,KAAK,IAAI,IAAInW,MAAM,CAACoW,cAAc,KAAK,IAAI,CAAC,EAAE;IAC7HpW,MAAM,CAACmb,aAAa,CAAC,KAAK,CAAC;EAC7B;;EAEA;EACA,MAAM6G,YAAY,GAAG/lB,GAAG,CAAC,CAAC;EAC1B,MAAMgmB,QAAQ,GAAGD,YAAY,GAAG/c,IAAI,CAACsZ,cAAc;;EAEnD;EACA,IAAIve,MAAM,CAAC8d,UAAU,EAAE;IACrB,MAAMoE,QAAQ,GAAGvnB,CAAC,CAAC0Y,IAAI,IAAI1Y,CAAC,CAAC+iB,YAAY,IAAI/iB,CAAC,CAAC+iB,YAAY,CAAC,CAAC;IAC7D1d,MAAM,CAACoT,kBAAkB,CAAC8O,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAIvnB,CAAC,CAACuG,MAAM,EAAEghB,QAAQ,CAAC;IACxEliB,MAAM,CAACE,IAAI,CAAC,WAAW,EAAEvF,CAAC,CAAC;IAC3B,IAAIsnB,QAAQ,GAAG,GAAG,IAAID,YAAY,GAAG/c,IAAI,CAACkd,aAAa,GAAG,GAAG,EAAE;MAC7DniB,MAAM,CAACE,IAAI,CAAC,uBAAuB,EAAEvF,CAAC,CAAC;IACzC;EACF;EACAsK,IAAI,CAACkd,aAAa,GAAGlmB,GAAG,CAAC,CAAC;EAC1BR,QAAQ,CAAC,MAAM;IACb,IAAI,CAACuE,MAAM,CAACM,SAAS,EAAEN,MAAM,CAAC8d,UAAU,GAAG,IAAI;EACjD,CAAC,CAAC;EACF,IAAI,CAAC7Y,IAAI,CAACoY,SAAS,IAAI,CAACpY,IAAI,CAACqY,OAAO,IAAI,CAACtd,MAAM,CAACwe,cAAc,IAAIxB,OAAO,CAACxC,IAAI,KAAK,CAAC,IAAI,CAACvV,IAAI,CAACuc,aAAa,IAAIvc,IAAI,CAACgP,gBAAgB,KAAKhP,IAAI,CAACyV,cAAc,IAAI,CAACzV,IAAI,CAACuc,aAAa,EAAE;IACnLvc,IAAI,CAACoY,SAAS,GAAG,KAAK;IACtBpY,IAAI,CAACqY,OAAO,GAAG,KAAK;IACpBrY,IAAI,CAACqZ,WAAW,GAAG,KAAK;IACxB;EACF;EACArZ,IAAI,CAACoY,SAAS,GAAG,KAAK;EACtBpY,IAAI,CAACqY,OAAO,GAAG,KAAK;EACpBrY,IAAI,CAACqZ,WAAW,GAAG,KAAK;EACxB,IAAI8D,UAAU;EACd,IAAIzgB,MAAM,CAACigB,YAAY,EAAE;IACvBQ,UAAU,GAAGvb,GAAG,GAAG7G,MAAM,CAACmO,SAAS,GAAG,CAACnO,MAAM,CAACmO,SAAS;EACzD,CAAC,MAAM;IACLiU,UAAU,GAAG,CAACnd,IAAI,CAACgP,gBAAgB;EACrC;EACA,IAAItS,MAAM,CAACgH,OAAO,EAAE;IAClB;EACF;EACA,IAAIhH,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO,EAAE;IAC9CjH,MAAM,CAAC+X,QAAQ,CAAC8J,UAAU,CAAC;MACzBO;IACF,CAAC,CAAC;IACF;EACF;;EAEA;EACA,MAAMC,WAAW,GAAGD,UAAU,IAAI,CAACpiB,MAAM,CAACqP,YAAY,CAAC,CAAC,IAAI,CAACrP,MAAM,CAAC2B,MAAM,CAACuJ,IAAI;EAC/E,IAAIoX,SAAS,GAAG,CAAC;EACjB,IAAI/W,SAAS,GAAGvL,MAAM,CAACwH,eAAe,CAAC,CAAC,CAAC;EACzC,KAAK,IAAIpL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmL,UAAU,CAAC9E,MAAM,EAAErG,CAAC,IAAIA,CAAC,GAAGuF,MAAM,CAACgJ,kBAAkB,GAAG,CAAC,GAAGhJ,MAAM,CAAC8I,cAAc,EAAE;IACrG,MAAM6M,SAAS,GAAGlb,CAAC,GAAGuF,MAAM,CAACgJ,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAGhJ,MAAM,CAAC8I,cAAc;IAC/E,IAAI,OAAOlD,UAAU,CAACnL,CAAC,GAAGkb,SAAS,CAAC,KAAK,WAAW,EAAE;MACpD,IAAI+K,WAAW,IAAID,UAAU,IAAI7a,UAAU,CAACnL,CAAC,CAAC,IAAIgmB,UAAU,GAAG7a,UAAU,CAACnL,CAAC,GAAGkb,SAAS,CAAC,EAAE;QACxFgL,SAAS,GAAGlmB,CAAC;QACbmP,SAAS,GAAGhE,UAAU,CAACnL,CAAC,GAAGkb,SAAS,CAAC,GAAG/P,UAAU,CAACnL,CAAC,CAAC;MACvD;IACF,CAAC,MAAM,IAAIimB,WAAW,IAAID,UAAU,IAAI7a,UAAU,CAACnL,CAAC,CAAC,EAAE;MACrDkmB,SAAS,GAAGlmB,CAAC;MACbmP,SAAS,GAAGhE,UAAU,CAACA,UAAU,CAAC9E,MAAM,GAAG,CAAC,CAAC,GAAG8E,UAAU,CAACA,UAAU,CAAC9E,MAAM,GAAG,CAAC,CAAC;IACnF;EACF;EACA,IAAI8f,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAI7gB,MAAM,CAACyQ,MAAM,EAAE;IACjB,IAAIpS,MAAM,CAACsP,WAAW,EAAE;MACtBkT,eAAe,GAAG7gB,MAAM,CAACqF,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO,IAAIjH,MAAM,CAACgH,OAAO,GAAGhH,MAAM,CAACgH,OAAO,CAACG,MAAM,CAAC1E,MAAM,GAAG,CAAC,GAAGzC,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC;IAC5I,CAAC,MAAM,IAAIzC,MAAM,CAACuP,KAAK,EAAE;MACvBgT,gBAAgB,GAAG,CAAC;IACtB;EACF;EACA;EACA,MAAME,KAAK,GAAG,CAACL,UAAU,GAAG7a,UAAU,CAAC+a,SAAS,CAAC,IAAI/W,SAAS;EAC9D,MAAM+L,SAAS,GAAGgL,SAAS,GAAG3gB,MAAM,CAACgJ,kBAAkB,GAAG,CAAC,GAAG,CAAC,GAAGhJ,MAAM,CAAC8I,cAAc;EACvF,IAAIwX,QAAQ,GAAGtgB,MAAM,CAAC+gB,YAAY,EAAE;IAClC;IACA,IAAI,CAAC/gB,MAAM,CAACghB,UAAU,EAAE;MACtB3iB,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,CAAC;MAClC;IACF;IACA,IAAIxN,MAAM,CAACwe,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIiE,KAAK,IAAI9gB,MAAM,CAACihB,eAAe,EAAE5iB,MAAM,CAAC8V,OAAO,CAACnU,MAAM,CAACyQ,MAAM,IAAIpS,MAAM,CAACuP,KAAK,GAAGgT,gBAAgB,GAAGD,SAAS,GAAGhL,SAAS,CAAC,CAAC,KAAKtX,MAAM,CAAC8V,OAAO,CAACwM,SAAS,CAAC;IAC9J;IACA,IAAItiB,MAAM,CAACwe,cAAc,KAAK,MAAM,EAAE;MACpC,IAAIiE,KAAK,GAAG,CAAC,GAAG9gB,MAAM,CAACihB,eAAe,EAAE;QACtC5iB,MAAM,CAAC8V,OAAO,CAACwM,SAAS,GAAGhL,SAAS,CAAC;MACvC,CAAC,MAAM,IAAIkL,eAAe,KAAK,IAAI,IAAIC,KAAK,GAAG,CAAC,IAAIpY,IAAI,CAACG,GAAG,CAACiY,KAAK,CAAC,GAAG9gB,MAAM,CAACihB,eAAe,EAAE;QAC5F5iB,MAAM,CAAC8V,OAAO,CAAC0M,eAAe,CAAC;MACjC,CAAC,MAAM;QACLxiB,MAAM,CAAC8V,OAAO,CAACwM,SAAS,CAAC;MAC3B;IACF;EACF,CAAC,MAAM;IACL;IACA,IAAI,CAAC3gB,MAAM,CAACkhB,WAAW,EAAE;MACvB7iB,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,CAAC;MAClC;IACF;IACA,MAAMsV,iBAAiB,GAAG9iB,MAAM,CAAC+iB,UAAU,KAAKpoB,CAAC,CAACuG,MAAM,KAAKlB,MAAM,CAAC+iB,UAAU,CAACC,MAAM,IAAIroB,CAAC,CAACuG,MAAM,KAAKlB,MAAM,CAAC+iB,UAAU,CAACE,MAAM,CAAC;IAC/H,IAAI,CAACH,iBAAiB,EAAE;MACtB,IAAI9iB,MAAM,CAACwe,cAAc,KAAK,MAAM,EAAE;QACpCxe,MAAM,CAAC8V,OAAO,CAACyM,gBAAgB,KAAK,IAAI,GAAGA,gBAAgB,GAAGD,SAAS,GAAGhL,SAAS,CAAC;MACtF;MACA,IAAItX,MAAM,CAACwe,cAAc,KAAK,MAAM,EAAE;QACpCxe,MAAM,CAAC8V,OAAO,CAAC0M,eAAe,KAAK,IAAI,GAAGA,eAAe,GAAGF,SAAS,CAAC;MACxE;IACF,CAAC,MAAM,IAAI3nB,CAAC,CAACuG,MAAM,KAAKlB,MAAM,CAAC+iB,UAAU,CAACC,MAAM,EAAE;MAChDhjB,MAAM,CAAC8V,OAAO,CAACwM,SAAS,GAAGhL,SAAS,CAAC;IACvC,CAAC,MAAM;MACLtX,MAAM,CAAC8V,OAAO,CAACwM,SAAS,CAAC;IAC3B;EACF;AACF;AAEA,SAASY,QAAQA,CAAA,EAAG;EAClB,MAAMljB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B,MAAM;IACNR;EACF,CAAC,GAAGnB,MAAM;EACV,IAAImB,EAAE,IAAIA,EAAE,CAACiJ,WAAW,KAAK,CAAC,EAAE;;EAEhC;EACA,IAAIzI,MAAM,CAACyH,WAAW,EAAE;IACtBpJ,MAAM,CAACmjB,aAAa,CAAC,CAAC;EACxB;;EAEA;EACA,MAAM;IACJhN,cAAc;IACdC,cAAc;IACd9O;EACF,CAAC,GAAGtH,MAAM;EACV,MAAM+G,SAAS,GAAG/G,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO;;EAEjE;EACAjH,MAAM,CAACmW,cAAc,GAAG,IAAI;EAC5BnW,MAAM,CAACoW,cAAc,GAAG,IAAI;EAC5BpW,MAAM,CAACyF,UAAU,CAAC,CAAC;EACnBzF,MAAM,CAACmG,YAAY,CAAC,CAAC;EACrBnG,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;EAC5B,MAAM+S,aAAa,GAAGrc,SAAS,IAAIpF,MAAM,CAACuJ,IAAI;EAC9C,IAAI,CAACvJ,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAIxH,MAAM,CAACwH,aAAa,GAAG,CAAC,KAAKnJ,MAAM,CAACuP,KAAK,IAAI,CAACvP,MAAM,CAACsP,WAAW,IAAI,CAACtP,MAAM,CAAC2B,MAAM,CAAC+G,cAAc,IAAI,CAAC0a,aAAa,EAAE;IAC3JpjB,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC1D,CAAC,MAAM;IACL,IAAIzC,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,IAAI,CAACnE,SAAS,EAAE;MACpC/G,MAAM,CAAC2W,WAAW,CAAC3W,MAAM,CAACqS,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACtD,CAAC,MAAM;MACLrS,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IACpD;EACF;EACA,IAAIxN,MAAM,CAACqjB,QAAQ,IAAIrjB,MAAM,CAACqjB,QAAQ,CAACC,OAAO,IAAItjB,MAAM,CAACqjB,QAAQ,CAACE,MAAM,EAAE;IACxEC,YAAY,CAACxjB,MAAM,CAACqjB,QAAQ,CAACI,aAAa,CAAC;IAC3CzjB,MAAM,CAACqjB,QAAQ,CAACI,aAAa,GAAG9gB,UAAU,CAAC,MAAM;MAC/C,IAAI3C,MAAM,CAACqjB,QAAQ,IAAIrjB,MAAM,CAACqjB,QAAQ,CAACC,OAAO,IAAItjB,MAAM,CAACqjB,QAAQ,CAACE,MAAM,EAAE;QACxEvjB,MAAM,CAACqjB,QAAQ,CAACK,MAAM,CAAC,CAAC;MAC1B;IACF,CAAC,EAAE,GAAG,CAAC;EACT;EACA;EACA1jB,MAAM,CAACoW,cAAc,GAAGA,cAAc;EACtCpW,MAAM,CAACmW,cAAc,GAAGA,cAAc;EACtC,IAAInW,MAAM,CAAC2B,MAAM,CAAC0K,aAAa,IAAI/E,QAAQ,KAAKtH,MAAM,CAACsH,QAAQ,EAAE;IAC/DtH,MAAM,CAACsM,aAAa,CAAC,CAAC;EACxB;AACF;AAEA,SAASqX,OAAOA,CAAChpB,CAAC,EAAE;EAClB,MAAMqF,MAAM,GAAG,IAAI;EACnB,IAAI,CAACA,MAAM,CAACiH,OAAO,EAAE;EACrB,IAAI,CAACjH,MAAM,CAAC8d,UAAU,EAAE;IACtB,IAAI9d,MAAM,CAAC2B,MAAM,CAACiiB,aAAa,EAAEjpB,CAAC,CAAC4hB,cAAc,CAAC,CAAC;IACnD,IAAIvc,MAAM,CAAC2B,MAAM,CAACkiB,wBAAwB,IAAI7jB,MAAM,CAAC2U,SAAS,EAAE;MAC9Dha,CAAC,CAAC0lB,eAAe,CAAC,CAAC;MACnB1lB,CAAC,CAACmpB,wBAAwB,CAAC,CAAC;IAC9B;EACF;AACF;AAEA,SAASC,QAAQA,CAAA,EAAG;EAClB,MAAM/jB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJsD,SAAS;IACTsD,YAAY;IACZK;EACF,CAAC,GAAGjH,MAAM;EACV,IAAI,CAACiH,OAAO,EAAE;EACdjH,MAAM,CAACqU,iBAAiB,GAAGrU,MAAM,CAACmO,SAAS;EAC3C,IAAInO,MAAM,CAAC4F,YAAY,CAAC,CAAC,EAAE;IACzB5F,MAAM,CAACmO,SAAS,GAAG,CAAC7K,SAAS,CAAC0gB,UAAU;EAC1C,CAAC,MAAM;IACLhkB,MAAM,CAACmO,SAAS,GAAG,CAAC7K,SAAS,CAAC2gB,SAAS;EACzC;EACA;EACA,IAAIjkB,MAAM,CAACmO,SAAS,KAAK,CAAC,EAAEnO,MAAM,CAACmO,SAAS,GAAG,CAAC;EAChDnO,MAAM,CAACwS,iBAAiB,CAAC,CAAC;EAC1BxS,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;EAC5B,IAAIiE,WAAW;EACf,MAAMlF,cAAc,GAAGpP,MAAM,CAACqP,YAAY,CAAC,CAAC,GAAGrP,MAAM,CAACwO,YAAY,CAAC,CAAC;EACpE,IAAIY,cAAc,KAAK,CAAC,EAAE;IACxBkF,WAAW,GAAG,CAAC;EACjB,CAAC,MAAM;IACLA,WAAW,GAAG,CAACtU,MAAM,CAACmO,SAAS,GAAGnO,MAAM,CAACwO,YAAY,CAAC,CAAC,IAAIY,cAAc;EAC3E;EACA,IAAIkF,WAAW,KAAKtU,MAAM,CAACgP,QAAQ,EAAE;IACnChP,MAAM,CAACkP,cAAc,CAACtI,YAAY,GAAG,CAAC5G,MAAM,CAACmO,SAAS,GAAGnO,MAAM,CAACmO,SAAS,CAAC;EAC5E;EACAnO,MAAM,CAACE,IAAI,CAAC,cAAc,EAAEF,MAAM,CAACmO,SAAS,EAAE,KAAK,CAAC;AACtD;AAEA,SAAS+V,MAAMA,CAACvpB,CAAC,EAAE;EACjB,MAAMqF,MAAM,GAAG,IAAI;EACnBiR,oBAAoB,CAACjR,MAAM,EAAErF,CAAC,CAACuG,MAAM,CAAC;EACtC,IAAIlB,MAAM,CAAC2B,MAAM,CAACgH,OAAO,IAAI3I,MAAM,CAAC2B,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAI,CAACnJ,MAAM,CAAC2B,MAAM,CAACwO,UAAU,EAAE;IAChG;EACF;EACAnQ,MAAM,CAAC6T,MAAM,CAAC,CAAC;AACjB;AAEA,SAASsQ,oBAAoBA,CAAA,EAAG;EAC9B,MAAMnkB,MAAM,GAAG,IAAI;EACnB,IAAIA,MAAM,CAACokB,6BAA6B,EAAE;EAC1CpkB,MAAM,CAACokB,6BAA6B,GAAG,IAAI;EAC3C,IAAIpkB,MAAM,CAAC2B,MAAM,CAAC6d,mBAAmB,EAAE;IACrCxf,MAAM,CAACmB,EAAE,CAACrE,KAAK,CAACunB,WAAW,GAAG,MAAM;EACtC;AACF;AAEA,MAAM1gB,MAAM,GAAGA,CAAC3D,MAAM,EAAEgE,MAAM,KAAK;EACjC,MAAMrH,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IACJqH,MAAM;IACNR,EAAE;IACFmC,SAAS;IACT7F;EACF,CAAC,GAAGuC,MAAM;EACV,MAAMskB,OAAO,GAAG,CAAC,CAAC3iB,MAAM,CAACye,MAAM;EAC/B,MAAMmE,SAAS,GAAGvgB,MAAM,KAAK,IAAI,GAAG,kBAAkB,GAAG,qBAAqB;EAC9E,MAAMwgB,YAAY,GAAGxgB,MAAM;EAC3B,IAAI,CAAC7C,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;;EAEnC;EACAxE,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,YAAY,EAAEvkB,MAAM,CAACmkB,oBAAoB,EAAE;IAC7DM,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACFnjB,EAAE,CAACojB,SAAS,CAAC,CAAC,YAAY,EAAEvkB,MAAM,CAACwc,YAAY,EAAE;IAC/CiI,OAAO,EAAE;EACX,CAAC,CAAC;EACFtjB,EAAE,CAACojB,SAAS,CAAC,CAAC,aAAa,EAAEvkB,MAAM,CAACwc,YAAY,EAAE;IAChDiI,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,WAAW,EAAEvkB,MAAM,CAACmf,WAAW,EAAE;IACnDsF,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACF3nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,aAAa,EAAEvkB,MAAM,CAACmf,WAAW,EAAE;IACrDsF,OAAO,EAAE,KAAK;IACdH;EACF,CAAC,CAAC;EACF3nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,UAAU,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACjD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,WAAW,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IAClD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,eAAe,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACtD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,aAAa,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACpD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,YAAY,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACnD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,cAAc,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACrD4C,OAAO,EAAE;EACX,CAAC,CAAC;EACF9nB,QAAQ,CAAC4nB,SAAS,CAAC,CAAC,aAAa,EAAEvkB,MAAM,CAAC6hB,UAAU,EAAE;IACpD4C,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,IAAI9iB,MAAM,CAACiiB,aAAa,IAAIjiB,MAAM,CAACkiB,wBAAwB,EAAE;IAC3D1iB,EAAE,CAACojB,SAAS,CAAC,CAAC,OAAO,EAAEvkB,MAAM,CAAC2jB,OAAO,EAAE,IAAI,CAAC;EAC9C;EACA,IAAIhiB,MAAM,CAACgH,OAAO,EAAE;IAClBrF,SAAS,CAACihB,SAAS,CAAC,CAAC,QAAQ,EAAEvkB,MAAM,CAAC+jB,QAAQ,CAAC;EACjD;;EAEA;EACA,IAAIpiB,MAAM,CAAC+iB,oBAAoB,EAAE;IAC/B1kB,MAAM,CAACwkB,YAAY,CAAC,CAAC/mB,MAAM,CAACC,GAAG,IAAID,MAAM,CAACE,OAAO,GAAG,yCAAyC,GAAG,uBAAuB,EAAEulB,QAAQ,EAAE,IAAI,CAAC;EAC1I,CAAC,MAAM;IACLljB,MAAM,CAACwkB,YAAY,CAAC,CAAC,gBAAgB,EAAEtB,QAAQ,EAAE,IAAI,CAAC;EACxD;;EAEA;EACA/hB,EAAE,CAACojB,SAAS,CAAC,CAAC,MAAM,EAAEvkB,MAAM,CAACkkB,MAAM,EAAE;IACnCI,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC;AACD,SAASK,YAAYA,CAAA,EAAG;EACtB,MAAM3kB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJ2B;EACF,CAAC,GAAG3B,MAAM;EACVA,MAAM,CAACwc,YAAY,GAAGA,YAAY,CAACoI,IAAI,CAAC5kB,MAAM,CAAC;EAC/CA,MAAM,CAACmf,WAAW,GAAGA,WAAW,CAACyF,IAAI,CAAC5kB,MAAM,CAAC;EAC7CA,MAAM,CAAC6hB,UAAU,GAAGA,UAAU,CAAC+C,IAAI,CAAC5kB,MAAM,CAAC;EAC3CA,MAAM,CAACmkB,oBAAoB,GAAGA,oBAAoB,CAACS,IAAI,CAAC5kB,MAAM,CAAC;EAC/D,IAAI2B,MAAM,CAACgH,OAAO,EAAE;IAClB3I,MAAM,CAAC+jB,QAAQ,GAAGA,QAAQ,CAACa,IAAI,CAAC5kB,MAAM,CAAC;EACzC;EACAA,MAAM,CAAC2jB,OAAO,GAAGA,OAAO,CAACiB,IAAI,CAAC5kB,MAAM,CAAC;EACrCA,MAAM,CAACkkB,MAAM,GAAGA,MAAM,CAACU,IAAI,CAAC5kB,MAAM,CAAC;EACnC2D,MAAM,CAAC3D,MAAM,EAAE,IAAI,CAAC;AACtB;AACA,SAAS6kB,YAAYA,CAAA,EAAG;EACtB,MAAM7kB,MAAM,GAAG,IAAI;EACnB2D,MAAM,CAAC3D,MAAM,EAAE,KAAK,CAAC;AACvB;AACA,IAAI8kB,QAAQ,GAAG;EACbH,YAAY;EACZE;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAAC/kB,MAAM,EAAE2B,MAAM,KAAK;EACxC,OAAO3B,MAAM,CAAC6I,IAAI,IAAIlH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC;AAC3D,CAAC;AACD,SAASqa,aAAaA,CAAA,EAAG;EACvB,MAAMnjB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJqS,SAAS;IACT9R,WAAW;IACXoB,MAAM;IACNR;EACF,CAAC,GAAGnB,MAAM;EACV,MAAMoJ,WAAW,GAAGzH,MAAM,CAACyH,WAAW;EACtC,IAAI,CAACA,WAAW,IAAIA,WAAW,IAAIpD,MAAM,CAACqD,IAAI,CAACD,WAAW,CAAC,CAAC3G,MAAM,KAAK,CAAC,EAAE;EAC1E,MAAM9F,QAAQ,GAAGrC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM0qB,eAAe,GAAGrjB,MAAM,CAACqjB,eAAe,KAAK,QAAQ,IAAI,CAACrjB,MAAM,CAACqjB,eAAe,GAAGrjB,MAAM,CAACqjB,eAAe,GAAG,WAAW;EAC7H,MAAMC,mBAAmB,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC/lB,QAAQ,CAACyC,MAAM,CAACqjB,eAAe,CAAC,IAAI,CAACrjB,MAAM,CAACqjB,eAAe,GAAGhlB,MAAM,CAACmB,EAAE,GAAGxE,QAAQ,CAAC2U,aAAa,CAAC3P,MAAM,CAACqjB,eAAe,CAAC;EAC5K,MAAME,UAAU,GAAGllB,MAAM,CAACmlB,aAAa,CAAC/b,WAAW,EAAE4b,eAAe,EAAEC,mBAAmB,CAAC;EAC1F,IAAI,CAACC,UAAU,IAAIllB,MAAM,CAAColB,iBAAiB,KAAKF,UAAU,EAAE;EAC5D,MAAMG,oBAAoB,GAAGH,UAAU,IAAI9b,WAAW,GAAGA,WAAW,CAAC8b,UAAU,CAAC,GAAGvR,SAAS;EAC5F,MAAM2R,gBAAgB,GAAGD,oBAAoB,IAAIrlB,MAAM,CAACulB,cAAc;EACtE,MAAMC,WAAW,GAAGT,aAAa,CAAC/kB,MAAM,EAAE2B,MAAM,CAAC;EACjD,MAAM8jB,UAAU,GAAGV,aAAa,CAAC/kB,MAAM,EAAEslB,gBAAgB,CAAC;EAC1D,MAAMI,aAAa,GAAG1lB,MAAM,CAAC2B,MAAM,CAAC+Z,UAAU;EAC9C,MAAMiK,YAAY,GAAGL,gBAAgB,CAAC5J,UAAU;EAChD,MAAMkK,UAAU,GAAGjkB,MAAM,CAACsF,OAAO;EACjC,IAAIue,WAAW,IAAI,CAACC,UAAU,EAAE;IAC9BtkB,EAAE,CAACyL,SAAS,CAACI,MAAM,CAAC,GAAGrL,MAAM,CAAC+K,sBAAsB,MAAM,EAAE,GAAG/K,MAAM,CAAC+K,sBAAsB,aAAa,CAAC;IAC1G1M,MAAM,CAAC6lB,oBAAoB,CAAC,CAAC;EAC/B,CAAC,MAAM,IAAI,CAACL,WAAW,IAAIC,UAAU,EAAE;IACrCtkB,EAAE,CAACyL,SAAS,CAACG,GAAG,CAAC,GAAGpL,MAAM,CAAC+K,sBAAsB,MAAM,CAAC;IACxD,IAAI4Y,gBAAgB,CAACzc,IAAI,CAAC4Q,IAAI,IAAI6L,gBAAgB,CAACzc,IAAI,CAAC4Q,IAAI,KAAK,QAAQ,IAAI,CAAC6L,gBAAgB,CAACzc,IAAI,CAAC4Q,IAAI,IAAI9X,MAAM,CAACkH,IAAI,CAAC4Q,IAAI,KAAK,QAAQ,EAAE;MACzItY,EAAE,CAACyL,SAAS,CAACG,GAAG,CAAC,GAAGpL,MAAM,CAAC+K,sBAAsB,aAAa,CAAC;IACjE;IACA1M,MAAM,CAAC6lB,oBAAoB,CAAC,CAAC;EAC/B;EACA,IAAIH,aAAa,IAAI,CAACC,YAAY,EAAE;IAClC3lB,MAAM,CAACyb,eAAe,CAAC,CAAC;EAC1B,CAAC,MAAM,IAAI,CAACiK,aAAa,IAAIC,YAAY,EAAE;IACzC3lB,MAAM,CAACmb,aAAa,CAAC,CAAC;EACxB;;EAEA;EACA,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAACra,OAAO,CAACglB,IAAI,IAAI;IACxD,IAAI,OAAOR,gBAAgB,CAACQ,IAAI,CAAC,KAAK,WAAW,EAAE;IACnD,MAAMC,gBAAgB,GAAGpkB,MAAM,CAACmkB,IAAI,CAAC,IAAInkB,MAAM,CAACmkB,IAAI,CAAC,CAAC7e,OAAO;IAC7D,MAAM+e,eAAe,GAAGV,gBAAgB,CAACQ,IAAI,CAAC,IAAIR,gBAAgB,CAACQ,IAAI,CAAC,CAAC7e,OAAO;IAChF,IAAI8e,gBAAgB,IAAI,CAACC,eAAe,EAAE;MACxChmB,MAAM,CAAC8lB,IAAI,CAAC,CAACG,OAAO,CAAC,CAAC;IACxB;IACA,IAAI,CAACF,gBAAgB,IAAIC,eAAe,EAAE;MACxChmB,MAAM,CAAC8lB,IAAI,CAAC,CAACI,MAAM,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGb,gBAAgB,CAAC7P,SAAS,IAAI6P,gBAAgB,CAAC7P,SAAS,KAAK9T,MAAM,CAAC8T,SAAS;EACtG,MAAM2Q,WAAW,GAAGzkB,MAAM,CAACuJ,IAAI,KAAKoa,gBAAgB,CAACnc,aAAa,KAAKxH,MAAM,CAACwH,aAAa,IAAIgd,gBAAgB,CAAC;EAChH,MAAME,OAAO,GAAG1kB,MAAM,CAACuJ,IAAI;EAC3B,IAAIib,gBAAgB,IAAI5lB,WAAW,EAAE;IACnCP,MAAM,CAACsmB,eAAe,CAAC,CAAC;EAC1B;EACAnqB,MAAM,CAAC6D,MAAM,CAAC2B,MAAM,EAAE2jB,gBAAgB,CAAC;EACvC,MAAMiB,SAAS,GAAGvmB,MAAM,CAAC2B,MAAM,CAACsF,OAAO;EACvC,MAAMuf,OAAO,GAAGxmB,MAAM,CAAC2B,MAAM,CAACuJ,IAAI;EAClClF,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;IACpB+e,cAAc,EAAE/e,MAAM,CAAC2B,MAAM,CAACod,cAAc;IAC5C5I,cAAc,EAAEnW,MAAM,CAAC2B,MAAM,CAACwU,cAAc;IAC5CC,cAAc,EAAEpW,MAAM,CAAC2B,MAAM,CAACyU;EAChC,CAAC,CAAC;EACF,IAAIwP,UAAU,IAAI,CAACW,SAAS,EAAE;IAC5BvmB,MAAM,CAACimB,OAAO,CAAC,CAAC;EAClB,CAAC,MAAM,IAAI,CAACL,UAAU,IAAIW,SAAS,EAAE;IACnCvmB,MAAM,CAACkmB,MAAM,CAAC,CAAC;EACjB;EACAlmB,MAAM,CAAColB,iBAAiB,GAAGF,UAAU;EACrCllB,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEolB,gBAAgB,CAAC;EAClD,IAAI/kB,WAAW,EAAE;IACf,IAAI6lB,WAAW,EAAE;MACfpmB,MAAM,CAACgb,WAAW,CAAC,CAAC;MACpBhb,MAAM,CAAC4Y,UAAU,CAACvG,SAAS,CAAC;MAC5BrS,MAAM,CAACmG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,CAACkgB,OAAO,IAAIG,OAAO,EAAE;MAC9BxmB,MAAM,CAAC4Y,UAAU,CAACvG,SAAS,CAAC;MAC5BrS,MAAM,CAACmG,YAAY,CAAC,CAAC;IACvB,CAAC,MAAM,IAAIkgB,OAAO,IAAI,CAACG,OAAO,EAAE;MAC9BxmB,MAAM,CAACgb,WAAW,CAAC,CAAC;IACtB;EACF;EACAhb,MAAM,CAACE,IAAI,CAAC,YAAY,EAAEolB,gBAAgB,CAAC;AAC7C;AAEA,SAASH,aAAaA,CAAC/b,WAAW,EAAEwS,IAAI,EAAE6K,WAAW,EAAE;EACrD,IAAI7K,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,QAAQ;EACjB;EACA,IAAI,CAACxS,WAAW,IAAIwS,IAAI,KAAK,WAAW,IAAI,CAAC6K,WAAW,EAAE,OAAO9S,SAAS;EAC1E,IAAIuR,UAAU,GAAG,KAAK;EACtB,MAAMxoB,MAAM,GAAGtC,SAAS,CAAC,CAAC;EAC1B,MAAMssB,aAAa,GAAG9K,IAAI,KAAK,QAAQ,GAAGlf,MAAM,CAACiqB,WAAW,GAAGF,WAAW,CAAC9gB,YAAY;EACvF,MAAMihB,MAAM,GAAG5gB,MAAM,CAACqD,IAAI,CAACD,WAAW,CAAC,CAAC9J,GAAG,CAACunB,KAAK,IAAI;IACnD,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACroB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MACzD,MAAMsoB,QAAQ,GAAGvgB,UAAU,CAACsgB,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5C,MAAMC,KAAK,GAAGN,aAAa,GAAGI,QAAQ;MACtC,OAAO;QACLE,KAAK;QACLH;MACF,CAAC;IACH;IACA,OAAO;MACLG,KAAK,EAAEH,KAAK;MACZA;IACF,CAAC;EACH,CAAC,CAAC;EACFD,MAAM,CAACK,IAAI,CAAC,CAAC9sB,CAAC,EAAEI,CAAC,KAAKuL,QAAQ,CAAC3L,CAAC,CAAC6sB,KAAK,EAAE,EAAE,CAAC,GAAGlhB,QAAQ,CAACvL,CAAC,CAACysB,KAAK,EAAE,EAAE,CAAC,CAAC;EACpE,KAAK,IAAI5qB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwqB,MAAM,CAACnkB,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;IACzC,MAAM;MACJyqB,KAAK;MACLG;IACF,CAAC,GAAGJ,MAAM,CAACxqB,CAAC,CAAC;IACb,IAAIwf,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAIlf,MAAM,CAACwqB,UAAU,CAAC,eAAeF,KAAK,KAAK,CAAC,CAACzT,OAAO,EAAE;QACxD2R,UAAU,GAAG2B,KAAK;MACpB;IACF,CAAC,MAAM,IAAIG,KAAK,IAAIP,WAAW,CAAC/gB,WAAW,EAAE;MAC3Cwf,UAAU,GAAG2B,KAAK;IACpB;EACF;EACA,OAAO3B,UAAU,IAAI,KAAK;AAC5B;AAEA,IAAI9b,WAAW,GAAG;EAChB+Z,aAAa;EACbgC;AACF,CAAC;AAED,SAASgC,cAAcA,CAACzmB,OAAO,EAAE0mB,MAAM,EAAE;EACvC,MAAMC,aAAa,GAAG,EAAE;EACxB3mB,OAAO,CAACI,OAAO,CAACwmB,IAAI,IAAI;IACtB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5BthB,MAAM,CAACqD,IAAI,CAACie,IAAI,CAAC,CAACxmB,OAAO,CAACymB,UAAU,IAAI;QACtC,IAAID,IAAI,CAACC,UAAU,CAAC,EAAE;UACpBF,aAAa,CAACrkB,IAAI,CAACokB,MAAM,GAAGG,UAAU,CAAC;QACzC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;MACnCD,aAAa,CAACrkB,IAAI,CAACokB,MAAM,GAAGE,IAAI,CAAC;IACnC;EACF,CAAC,CAAC;EACF,OAAOD,aAAa;AACtB;AACA,SAASG,UAAUA,CAAA,EAAG;EACpB,MAAMxnB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJunB,UAAU;IACV5lB,MAAM;IACNkF,GAAG;IACH1F,EAAE;IACF1D;EACF,CAAC,GAAGuC,MAAM;EACV;EACA,MAAMynB,QAAQ,GAAGN,cAAc,CAAC,CAAC,aAAa,EAAExlB,MAAM,CAAC8T,SAAS,EAAE;IAChE,WAAW,EAAEzV,MAAM,CAAC2B,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q;EACzD,CAAC,EAAE;IACD,YAAY,EAAEtF,MAAM,CAACwO;EACvB,CAAC,EAAE;IACD,KAAK,EAAEtJ;EACT,CAAC,EAAE;IACD,MAAM,EAAElF,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG;EAC5C,CAAC,EAAE;IACD,aAAa,EAAEnH,MAAM,CAACkH,IAAI,IAAIlH,MAAM,CAACkH,IAAI,CAACC,IAAI,GAAG,CAAC,IAAInH,MAAM,CAACkH,IAAI,CAAC4Q,IAAI,KAAK;EAC7E,CAAC,EAAE;IACD,SAAS,EAAEhc,MAAM,CAACE;EACpB,CAAC,EAAE;IACD,KAAK,EAAEF,MAAM,CAACC;EAChB,CAAC,EAAE;IACD,UAAU,EAAEiE,MAAM,CAACgH;EACrB,CAAC,EAAE;IACD,UAAU,EAAEhH,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC+G;EACvC,CAAC,EAAE;IACD,gBAAgB,EAAE/G,MAAM,CAAC4K;EAC3B,CAAC,CAAC,EAAE5K,MAAM,CAAC+K,sBAAsB,CAAC;EAClC6a,UAAU,CAACvkB,IAAI,CAAC,GAAGykB,QAAQ,CAAC;EAC5BtmB,EAAE,CAACyL,SAAS,CAACG,GAAG,CAAC,GAAGwa,UAAU,CAAC;EAC/BvnB,MAAM,CAAC6lB,oBAAoB,CAAC,CAAC;AAC/B;AAEA,SAAS6B,aAAaA,CAAA,EAAG;EACvB,MAAM1nB,MAAM,GAAG,IAAI;EACnB,MAAM;IACJmB,EAAE;IACFomB;EACF,CAAC,GAAGvnB,MAAM;EACV,IAAI,CAACmB,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;EACnCA,EAAE,CAACyL,SAAS,CAACI,MAAM,CAAC,GAAGua,UAAU,CAAC;EAClCvnB,MAAM,CAAC6lB,oBAAoB,CAAC,CAAC;AAC/B;AAEA,IAAI8B,OAAO,GAAG;EACZH,UAAU;EACVE;AACF,CAAC;AAED,SAASpb,aAAaA,CAAA,EAAG;EACvB,MAAMtM,MAAM,GAAG,IAAI;EACnB,MAAM;IACJsb,QAAQ,EAAEsM,SAAS;IACnBjmB;EACF,CAAC,GAAG3B,MAAM;EACV,MAAM;IACJ0H;EACF,CAAC,GAAG/F,MAAM;EACV,IAAI+F,kBAAkB,EAAE;IACtB,MAAMoI,cAAc,GAAG9P,MAAM,CAACmH,MAAM,CAAC1E,MAAM,GAAG,CAAC;IAC/C,MAAMolB,kBAAkB,GAAG7nB,MAAM,CAACuH,UAAU,CAACuI,cAAc,CAAC,GAAG9P,MAAM,CAACwH,eAAe,CAACsI,cAAc,CAAC,GAAGpI,kBAAkB,GAAG,CAAC;IAC9H1H,MAAM,CAACsb,QAAQ,GAAGtb,MAAM,CAACkG,IAAI,GAAG2hB,kBAAkB;EACpD,CAAC,MAAM;IACL7nB,MAAM,CAACsb,QAAQ,GAAGtb,MAAM,CAACsH,QAAQ,CAAC7E,MAAM,KAAK,CAAC;EAChD;EACA,IAAId,MAAM,CAACwU,cAAc,KAAK,IAAI,EAAE;IAClCnW,MAAM,CAACmW,cAAc,GAAG,CAACnW,MAAM,CAACsb,QAAQ;EAC1C;EACA,IAAI3Z,MAAM,CAACyU,cAAc,KAAK,IAAI,EAAE;IAClCpW,MAAM,CAACoW,cAAc,GAAG,CAACpW,MAAM,CAACsb,QAAQ;EAC1C;EACA,IAAIsM,SAAS,IAAIA,SAAS,KAAK5nB,MAAM,CAACsb,QAAQ,EAAE;IAC9Ctb,MAAM,CAACuP,KAAK,GAAG,KAAK;EACtB;EACA,IAAIqY,SAAS,KAAK5nB,MAAM,CAACsb,QAAQ,EAAE;IACjCtb,MAAM,CAACE,IAAI,CAACF,MAAM,CAACsb,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC;EAClD;AACF;AACA,IAAIwM,eAAe,GAAG;EACpBxb;AACF,CAAC;AAED,IAAIyb,QAAQ,GAAG;EACb9kB,IAAI,EAAE,IAAI;EACVwS,SAAS,EAAE,YAAY;EACvB8K,cAAc,EAAE,KAAK;EACrByH,qBAAqB,EAAE,kBAAkB;EACzCzM,iBAAiB,EAAE,SAAS;EAC5B9E,YAAY,EAAE,CAAC;EACfvJ,KAAK,EAAE,GAAG;EACVvE,OAAO,EAAE,KAAK;EACd+b,oBAAoB,EAAE,IAAI;EAC1B9iB,cAAc,EAAE,IAAI;EACpBwe,MAAM,EAAE,KAAK;EACb6H,cAAc,EAAE,KAAK;EACrBC,YAAY,EAAE,QAAQ;EACtBjhB,OAAO,EAAE,IAAI;EACbyX,iBAAiB,EAAE,uDAAuD;EAC1E;EACA5gB,KAAK,EAAE,IAAI;EACXE,MAAM,EAAE,IAAI;EACZ;EACA4W,8BAA8B,EAAE,KAAK;EACrC;EACAvX,SAAS,EAAE,IAAI;EACf8qB,GAAG,EAAE,IAAI;EACT;EACA/L,kBAAkB,EAAE,KAAK;EACzBC,kBAAkB,EAAE,EAAE;EACtB;EACAlM,UAAU,EAAE,KAAK;EACjB;EACArF,cAAc,EAAE,KAAK;EACrB;EACAkJ,gBAAgB,EAAE,KAAK;EACvB;EACAnJ,MAAM,EAAE,OAAO;EACf;;EAEA;EACAzB,WAAW,EAAEuK,SAAS;EACtBqR,eAAe,EAAE,QAAQ;EACzB;EACAhd,YAAY,EAAE,CAAC;EACfmB,aAAa,EAAE,CAAC;EAChBsB,cAAc,EAAE,CAAC;EACjBE,kBAAkB,EAAE,CAAC;EACrB0M,kBAAkB,EAAE,KAAK;EACzB3O,cAAc,EAAE,KAAK;EACrBgD,oBAAoB,EAAE,KAAK;EAC3BhE,kBAAkB,EAAE,CAAC;EACrB;EACAG,iBAAiB,EAAE,CAAC;EACpB;EACA0K,mBAAmB,EAAE,IAAI;EACzBxG,wBAAwB,EAAE,KAAK;EAC/B;EACAM,aAAa,EAAE,IAAI;EACnB;EACArC,YAAY,EAAE,KAAK;EACnB;EACAwW,UAAU,EAAE,CAAC;EACbV,UAAU,EAAE,EAAE;EACdzE,aAAa,EAAE,IAAI;EACnBwH,WAAW,EAAE,IAAI;EACjBF,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE,GAAG;EACpBF,YAAY,EAAE,GAAG;EACjBd,YAAY,EAAE,IAAI;EAClB7C,cAAc,EAAE,IAAI;EACpBzG,SAAS,EAAE,CAAC;EACZ6H,wBAAwB,EAAE,KAAK;EAC/BnB,wBAAwB,EAAE,IAAI;EAC9BC,6BAA6B,EAAE,KAAK;EACpCO,mBAAmB,EAAE,KAAK;EAC1B;EACA4I,iBAAiB,EAAE,IAAI;EACvB;EACAzG,UAAU,EAAE,IAAI;EAChBD,eAAe,EAAE,IAAI;EACrB;EACAnV,mBAAmB,EAAE,KAAK;EAC1B;EACAmP,UAAU,EAAE,KAAK;EACjB;EACAkI,aAAa,EAAE,IAAI;EACnBC,wBAAwB,EAAE,IAAI;EAC9BjQ,mBAAmB,EAAE,KAAK;EAC1B;EACA1I,IAAI,EAAE,KAAK;EACXkO,kBAAkB,EAAE,IAAI;EACxBI,oBAAoB,EAAE,CAAC;EACvBjC,mBAAmB,EAAE,IAAI;EACzB;EACAnF,MAAM,EAAE,KAAK;EACb;EACAgE,cAAc,EAAE,IAAI;EACpBD,cAAc,EAAE,IAAI;EACpB4H,YAAY,EAAE,IAAI;EAClB;EACAF,SAAS,EAAE,IAAI;EACfL,cAAc,EAAE,mBAAmB;EACnCG,iBAAiB,EAAE,IAAI;EACvB;EACA0K,gBAAgB,EAAE,IAAI;EACtBvb,uBAAuB,EAAE,EAAE;EAC3B;EACAJ,sBAAsB,EAAE,SAAS;EACjC;EACAtF,UAAU,EAAE,cAAc;EAC1B8R,eAAe,EAAE,oBAAoB;EACrCrI,gBAAgB,EAAE,qBAAqB;EACvC/B,iBAAiB,EAAE,sBAAsB;EACzCC,sBAAsB,EAAE,4BAA4B;EACpD+B,cAAc,EAAE,mBAAmB;EACnCC,cAAc,EAAE,mBAAmB;EACnCuX,YAAY,EAAE,gBAAgB;EAC9B/W,kBAAkB,EAAE,uBAAuB;EAC3CM,mBAAmB,EAAE,CAAC;EACtB;EACAsB,kBAAkB,EAAE,IAAI;EACxB;EACAoV,YAAY,EAAE;AAChB,CAAC;AAED,SAASC,kBAAkBA,CAAC7mB,MAAM,EAAE8mB,gBAAgB,EAAE;EACpD,OAAO,SAASzmB,YAAYA,CAAC0mB,GAAG,EAAE;IAChC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAClBA,GAAG,GAAG,CAAC,CAAC;IACV;IACA,MAAMC,eAAe,GAAG3iB,MAAM,CAACqD,IAAI,CAACqf,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAME,YAAY,GAAGF,GAAG,CAACC,eAAe,CAAC;IACzC,IAAI,OAAOC,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;MAC7DzsB,MAAM,CAACssB,gBAAgB,EAAEC,GAAG,CAAC;MAC7B;IACF;IACA,IAAI/mB,MAAM,CAACgnB,eAAe,CAAC,KAAK,IAAI,EAAE;MACpChnB,MAAM,CAACgnB,eAAe,CAAC,GAAG;QACxB1hB,OAAO,EAAE;MACX,CAAC;IACH;IACA,IAAI0hB,eAAe,KAAK,YAAY,IAAIhnB,MAAM,CAACgnB,eAAe,CAAC,IAAIhnB,MAAM,CAACgnB,eAAe,CAAC,CAAC1hB,OAAO,IAAI,CAACtF,MAAM,CAACgnB,eAAe,CAAC,CAAC1F,MAAM,IAAI,CAACthB,MAAM,CAACgnB,eAAe,CAAC,CAAC3F,MAAM,EAAE;MACxKrhB,MAAM,CAACgnB,eAAe,CAAC,CAACE,IAAI,GAAG,IAAI;IACrC;IACA,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAACrqB,OAAO,CAACmqB,eAAe,CAAC,IAAI,CAAC,IAAIhnB,MAAM,CAACgnB,eAAe,CAAC,IAAIhnB,MAAM,CAACgnB,eAAe,CAAC,CAAC1hB,OAAO,IAAI,CAACtF,MAAM,CAACgnB,eAAe,CAAC,CAACxnB,EAAE,EAAE;MAC1JQ,MAAM,CAACgnB,eAAe,CAAC,CAACE,IAAI,GAAG,IAAI;IACrC;IACA,IAAI,EAAEF,eAAe,IAAIhnB,MAAM,IAAI,SAAS,IAAIinB,YAAY,CAAC,EAAE;MAC7DzsB,MAAM,CAACssB,gBAAgB,EAAEC,GAAG,CAAC;MAC7B;IACF;IACA,IAAI,OAAO/mB,MAAM,CAACgnB,eAAe,CAAC,KAAK,QAAQ,IAAI,EAAE,SAAS,IAAIhnB,MAAM,CAACgnB,eAAe,CAAC,CAAC,EAAE;MAC1FhnB,MAAM,CAACgnB,eAAe,CAAC,CAAC1hB,OAAO,GAAG,IAAI;IACxC;IACA,IAAI,CAACtF,MAAM,CAACgnB,eAAe,CAAC,EAAEhnB,MAAM,CAACgnB,eAAe,CAAC,GAAG;MACtD1hB,OAAO,EAAE;IACX,CAAC;IACD9K,MAAM,CAACssB,gBAAgB,EAAEC,GAAG,CAAC;EAC/B,CAAC;AACH;;AAEA;AACA,MAAMI,UAAU,GAAG;EACjBplB,aAAa;EACbmQ,MAAM;EACN1F,SAAS;EACT0H,UAAU;EACVrM,KAAK;EACL0B,IAAI;EACJwQ,UAAU;EACV/X,MAAM,EAAEmhB,QAAQ;EAChB1b,WAAW;EACXkD,aAAa,EAAEwb,eAAe;EAC9BH;AACF,CAAC;AACD,MAAMoB,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAMC,MAAM,CAAC;EACXjO,WAAWA,CAAA,EAAG;IACZ,IAAI5Z,EAAE;IACN,IAAIQ,MAAM;IACV,KAAK,IAAI2C,IAAI,GAAGC,SAAS,CAAC9B,MAAM,EAAE+B,IAAI,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAC9B;IACA,IAAIF,IAAI,CAAC/B,MAAM,KAAK,CAAC,IAAI+B,IAAI,CAAC,CAAC,CAAC,CAACuW,WAAW,IAAI/U,MAAM,CAACijB,SAAS,CAACC,QAAQ,CAACvhB,IAAI,CAACnD,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjH3D,MAAM,GAAG6C,IAAI,CAAC,CAAC,CAAC;IAClB,CAAC,MAAM;MACL,CAACrD,EAAE,EAAEQ,MAAM,CAAC,GAAG6C,IAAI;IACrB;IACA,IAAI,CAAC7C,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC;IACxBA,MAAM,GAAGxF,MAAM,CAAC,CAAC,CAAC,EAAEwF,MAAM,CAAC;IAC3B,IAAIR,EAAE,IAAI,CAACQ,MAAM,CAACR,EAAE,EAAEQ,MAAM,CAACR,EAAE,GAAGA,EAAE;IACpC,MAAMxE,QAAQ,GAAGrC,WAAW,CAAC,CAAC;IAC9B,IAAIqH,MAAM,CAACR,EAAE,IAAI,OAAOQ,MAAM,CAACR,EAAE,KAAK,QAAQ,IAAIxE,QAAQ,CAACwsB,gBAAgB,CAACxnB,MAAM,CAACR,EAAE,CAAC,CAACsB,MAAM,GAAG,CAAC,EAAE;MACjG,MAAM2mB,OAAO,GAAG,EAAE;MAClBzsB,QAAQ,CAACwsB,gBAAgB,CAACxnB,MAAM,CAACR,EAAE,CAAC,CAACL,OAAO,CAAC2lB,WAAW,IAAI;QAC1D,MAAM4C,SAAS,GAAGltB,MAAM,CAAC,CAAC,CAAC,EAAEwF,MAAM,EAAE;UACnCR,EAAE,EAAEslB;QACN,CAAC,CAAC;QACF2C,OAAO,CAACpmB,IAAI,CAAC,IAAIgmB,MAAM,CAACK,SAAS,CAAC,CAAC;MACrC,CAAC,CAAC;MACF;MACA,OAAOD,OAAO;IAChB;;IAEA;IACA,MAAMppB,MAAM,GAAG,IAAI;IACnBA,MAAM,CAACspB,UAAU,GAAG,IAAI;IACxBtpB,MAAM,CAACxD,OAAO,GAAGS,UAAU,CAAC,CAAC;IAC7B+C,MAAM,CAACvC,MAAM,GAAGiB,SAAS,CAAC;MACxBrB,SAAS,EAAEsE,MAAM,CAACtE;IACpB,CAAC,CAAC;IACF2C,MAAM,CAACpB,OAAO,GAAGiB,UAAU,CAAC,CAAC;IAC7BG,MAAM,CAAC+D,eAAe,GAAG,CAAC,CAAC;IAC3B/D,MAAM,CAAC6E,kBAAkB,GAAG,EAAE;IAC9B7E,MAAM,CAACupB,OAAO,GAAG,CAAC,GAAGvpB,MAAM,CAACwpB,WAAW,CAAC;IACxC,IAAI7nB,MAAM,CAAC4nB,OAAO,IAAI9kB,KAAK,CAACY,OAAO,CAAC1D,MAAM,CAAC4nB,OAAO,CAAC,EAAE;MACnDvpB,MAAM,CAACupB,OAAO,CAACvmB,IAAI,CAAC,GAAGrB,MAAM,CAAC4nB,OAAO,CAAC;IACxC;IACA,MAAMd,gBAAgB,GAAG,CAAC,CAAC;IAC3BzoB,MAAM,CAACupB,OAAO,CAACzoB,OAAO,CAAC2oB,GAAG,IAAI;MAC5BA,GAAG,CAAC;QACF9nB,MAAM;QACN3B,MAAM;QACNgC,YAAY,EAAEwmB,kBAAkB,CAAC7mB,MAAM,EAAE8mB,gBAAgB,CAAC;QAC1DxoB,EAAE,EAAED,MAAM,CAACC,EAAE,CAAC2kB,IAAI,CAAC5kB,MAAM,CAAC;QAC1BkE,IAAI,EAAElE,MAAM,CAACkE,IAAI,CAAC0gB,IAAI,CAAC5kB,MAAM,CAAC;QAC9BoE,GAAG,EAAEpE,MAAM,CAACoE,GAAG,CAACwgB,IAAI,CAAC5kB,MAAM,CAAC;QAC5BE,IAAI,EAAEF,MAAM,CAACE,IAAI,CAAC0kB,IAAI,CAAC5kB,MAAM;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAM0pB,YAAY,GAAGvtB,MAAM,CAAC,CAAC,CAAC,EAAE4rB,QAAQ,EAAEU,gBAAgB,CAAC;;IAE3D;IACAzoB,MAAM,CAAC2B,MAAM,GAAGxF,MAAM,CAAC,CAAC,CAAC,EAAEutB,YAAY,EAAEX,gBAAgB,EAAEpnB,MAAM,CAAC;IAClE3B,MAAM,CAACulB,cAAc,GAAGppB,MAAM,CAAC,CAAC,CAAC,EAAE6D,MAAM,CAAC2B,MAAM,CAAC;IACjD3B,MAAM,CAAC2pB,YAAY,GAAGxtB,MAAM,CAAC,CAAC,CAAC,EAAEwF,MAAM,CAAC;;IAExC;IACA,IAAI3B,MAAM,CAAC2B,MAAM,IAAI3B,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,EAAE;MACrC+F,MAAM,CAACqD,IAAI,CAACrJ,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,CAAC,CAACa,OAAO,CAAC8oB,SAAS,IAAI;QACjD5pB,MAAM,CAACC,EAAE,CAAC2pB,SAAS,EAAE5pB,MAAM,CAAC2B,MAAM,CAAC1B,EAAE,CAAC2pB,SAAS,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;IACA,IAAI5pB,MAAM,CAAC2B,MAAM,IAAI3B,MAAM,CAAC2B,MAAM,CAACiD,KAAK,EAAE;MACxC5E,MAAM,CAAC4E,KAAK,CAAC5E,MAAM,CAAC2B,MAAM,CAACiD,KAAK,CAAC;IACnC;;IAEA;IACAoB,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;MACpBiH,OAAO,EAAEjH,MAAM,CAAC2B,MAAM,CAACsF,OAAO;MAC9B9F,EAAE;MACF;MACAomB,UAAU,EAAE,EAAE;MACd;MACApgB,MAAM,EAAE,EAAE;MACVI,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAE,EAAE;MACZE,eAAe,EAAE,EAAE;MACnB;MACA5B,YAAYA,CAAA,EAAG;QACb,OAAO5F,MAAM,CAAC2B,MAAM,CAAC8T,SAAS,KAAK,YAAY;MACjD,CAAC;MACD5P,UAAUA,CAAA,EAAG;QACX,OAAO7F,MAAM,CAAC2B,MAAM,CAAC8T,SAAS,KAAK,UAAU;MAC/C,CAAC;MACD;MACAjI,WAAW,EAAE,CAAC;MACd6E,SAAS,EAAE,CAAC;MACZ;MACA/C,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,KAAK;MACZ;MACApB,SAAS,EAAE,CAAC;MACZkG,iBAAiB,EAAE,CAAC;MACpBrF,QAAQ,EAAE,CAAC;MACX6a,QAAQ,EAAE,CAAC;MACXlV,SAAS,EAAE,KAAK;MAChB7G,qBAAqBA,CAAA,EAAG;QACtB;QACA;QACA,OAAOzD,IAAI,CAACyf,KAAK,CAAC,IAAI,CAAC3b,SAAS,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE;MACvD,CAAC;MACD;MACAgI,cAAc,EAAEnW,MAAM,CAAC2B,MAAM,CAACwU,cAAc;MAC5CC,cAAc,EAAEpW,MAAM,CAAC2B,MAAM,CAACyU,cAAc;MAC5C;MACAqE,eAAe,EAAE;QACf4C,SAAS,EAAE1J,SAAS;QACpB2J,OAAO,EAAE3J,SAAS;QAClByK,mBAAmB,EAAEzK,SAAS;QAC9B4K,cAAc,EAAE5K,SAAS;QACzB0K,WAAW,EAAE1K,SAAS;QACtBM,gBAAgB,EAAEN,SAAS;QAC3B+G,cAAc,EAAE/G,SAAS;QACzB8K,kBAAkB,EAAE9K,SAAS;QAC7B;QACA+K,iBAAiB,EAAE1e,MAAM,CAAC2B,MAAM,CAAC+c,iBAAiB;QAClD;QACAyD,aAAa,EAAE,CAAC;QAChB4H,YAAY,EAAEpW,SAAS;QACvB;QACAqW,UAAU,EAAE,EAAE;QACd7I,mBAAmB,EAAExN,SAAS;QAC9B2K,WAAW,EAAE3K,SAAS;QACtBgJ,SAAS,EAAE,IAAI;QACfE,OAAO,EAAE;MACX,CAAC;MACD;MACAiB,UAAU,EAAE,IAAI;MAChB;MACAiB,cAAc,EAAE/e,MAAM,CAAC2B,MAAM,CAACod,cAAc;MAC5C/B,OAAO,EAAE;QACPb,MAAM,EAAE,CAAC;QACTgC,MAAM,EAAE,CAAC;QACTH,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXzD,IAAI,EAAE;MACR,CAAC;MACD;MACAyP,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE;IAChB,CAAC,CAAC;IACFlqB,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA,IAAIF,MAAM,CAAC2B,MAAM,CAACsB,IAAI,EAAE;MACtBjD,MAAM,CAACiD,IAAI,CAAC,CAAC;IACf;;IAEA;IACA;IACA,OAAOjD,MAAM;EACf;EACAyG,iBAAiBA,CAAC0jB,QAAQ,EAAE;IAC1B,IAAI,IAAI,CAACvkB,YAAY,CAAC,CAAC,EAAE;MACvB,OAAOukB,QAAQ;IACjB;IACA;IACA,OAAO;MACL,OAAO,EAAE,QAAQ;MACjB,YAAY,EAAE,aAAa;MAC3B,gBAAgB,EAAE,cAAc;MAChC,aAAa,EAAE,YAAY;MAC3B,cAAc,EAAE,eAAe;MAC/B,cAAc,EAAE,aAAa;MAC7B,eAAe,EAAE,gBAAgB;MACjC,aAAa,EAAE;IACjB,CAAC,CAACA,QAAQ,CAAC;EACb;EACAxR,aAAaA,CAACtQ,OAAO,EAAE;IACrB,MAAM;MACJ3B,QAAQ;MACR/E;IACF,CAAC,GAAG,IAAI;IACR,MAAMwF,MAAM,GAAGvM,eAAe,CAAC8L,QAAQ,EAAE,IAAI/E,MAAM,CAACyF,UAAU,gBAAgB,CAAC;IAC/E,MAAMyI,eAAe,GAAGxT,YAAY,CAAC8K,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/C,OAAO9K,YAAY,CAACgM,OAAO,CAAC,GAAGwH,eAAe;EAChD;EACAvC,mBAAmBA,CAACvI,KAAK,EAAE;IACzB,OAAO,IAAI,CAAC4T,aAAa,CAAC,IAAI,CAACxR,MAAM,CAACwJ,IAAI,CAACtI,OAAO,IAAIA,OAAO,CAAC6K,YAAY,CAAC,yBAAyB,CAAC,GAAG,CAAC,KAAKnO,KAAK,CAAC,CAAC;EACvH;EACAuU,YAAYA,CAAA,EAAG;IACb,MAAMtZ,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ0G,QAAQ;MACR/E;IACF,CAAC,GAAG3B,MAAM;IACVA,MAAM,CAACmH,MAAM,GAAGvM,eAAe,CAAC8L,QAAQ,EAAE,IAAI/E,MAAM,CAACyF,UAAU,gBAAgB,CAAC;EAClF;EACA8e,MAAMA,CAAA,EAAG;IACP,MAAMlmB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACiH,OAAO,EAAE;IACpBjH,MAAM,CAACiH,OAAO,GAAG,IAAI;IACrB,IAAIjH,MAAM,CAAC2B,MAAM,CAAC+Z,UAAU,EAAE;MAC5B1b,MAAM,CAACmb,aAAa,CAAC,CAAC;IACxB;IACAnb,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;EACvB;EACA+lB,OAAOA,CAAA,EAAG;IACR,MAAMjmB,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAACiH,OAAO,EAAE;IACrBjH,MAAM,CAACiH,OAAO,GAAG,KAAK;IACtB,IAAIjH,MAAM,CAAC2B,MAAM,CAAC+Z,UAAU,EAAE;MAC5B1b,MAAM,CAACyb,eAAe,CAAC,CAAC;IAC1B;IACAzb,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;EACxB;EACAkqB,WAAWA,CAACpb,QAAQ,EAAE9B,KAAK,EAAE;IAC3B,MAAMlN,MAAM,GAAG,IAAI;IACnBgP,QAAQ,GAAG3E,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,GAAG,CAACoE,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,MAAMtE,GAAG,GAAG1K,MAAM,CAACwO,YAAY,CAAC,CAAC;IACjC,MAAM5D,GAAG,GAAG5K,MAAM,CAACqP,YAAY,CAAC,CAAC;IACjC,MAAMgb,OAAO,GAAG,CAACzf,GAAG,GAAGF,GAAG,IAAIsE,QAAQ,GAAGtE,GAAG;IAC5C1K,MAAM,CAACuU,WAAW,CAAC8V,OAAO,EAAE,OAAOnd,KAAK,KAAK,WAAW,GAAG,CAAC,GAAGA,KAAK,CAAC;IACrElN,MAAM,CAACwS,iBAAiB,CAAC,CAAC;IAC1BxS,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;EAC9B;EACAwV,oBAAoBA,CAAA,EAAG;IACrB,MAAM7lB,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAAC4mB,YAAY,IAAI,CAACvoB,MAAM,CAACmB,EAAE,EAAE;IAC/C,MAAMmpB,GAAG,GAAGtqB,MAAM,CAACmB,EAAE,CAAC8M,SAAS,CAAC5O,KAAK,CAAC,GAAG,CAAC,CAACiK,MAAM,CAAC2E,SAAS,IAAI;MAC7D,OAAOA,SAAS,CAACzP,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAIyP,SAAS,CAACzP,OAAO,CAACwB,MAAM,CAAC2B,MAAM,CAAC+K,sBAAsB,CAAC,KAAK,CAAC;IAC3G,CAAC,CAAC;IACF1M,MAAM,CAACE,IAAI,CAAC,mBAAmB,EAAEoqB,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;EACjD;EACAC,eAAeA,CAACniB,OAAO,EAAE;IACvB,MAAMrI,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACM,SAAS,EAAE,OAAO,EAAE;IAC/B,OAAO+H,OAAO,CAAC4F,SAAS,CAAC5O,KAAK,CAAC,GAAG,CAAC,CAACiK,MAAM,CAAC2E,SAAS,IAAI;MACtD,OAAOA,SAAS,CAACzP,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAIyP,SAAS,CAACzP,OAAO,CAACwB,MAAM,CAAC2B,MAAM,CAACyF,UAAU,CAAC,KAAK,CAAC;IACrG,CAAC,CAAC,CAACmjB,IAAI,CAAC,GAAG,CAAC;EACd;EACAvZ,iBAAiBA,CAAA,EAAG;IAClB,MAAMhR,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,CAAC2B,MAAM,CAAC4mB,YAAY,IAAI,CAACvoB,MAAM,CAACmB,EAAE,EAAE;IAC/C,MAAMspB,OAAO,GAAG,EAAE;IAClBzqB,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;MAC/B,MAAMkf,UAAU,GAAGvnB,MAAM,CAACwqB,eAAe,CAACniB,OAAO,CAAC;MAClDoiB,OAAO,CAACznB,IAAI,CAAC;QACXqF,OAAO;QACPkf;MACF,CAAC,CAAC;MACFvnB,MAAM,CAACE,IAAI,CAAC,aAAa,EAAEmI,OAAO,EAAEkf,UAAU,CAAC;IACjD,CAAC,CAAC;IACFvnB,MAAM,CAACE,IAAI,CAAC,eAAe,EAAEuqB,OAAO,CAAC;EACvC;EACA1Y,oBAAoBA,CAAC2Y,IAAI,EAAEC,KAAK,EAAE;IAChC,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,SAAS;IAClB;IACA,IAAIC,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,KAAK;IACf;IACA,MAAM3qB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ2B,MAAM;MACNwF,MAAM;MACNI,UAAU;MACVC,eAAe;MACftB,IAAI,EAAES,UAAU;MAChB6G;IACF,CAAC,GAAGxN,MAAM;IACV,IAAI4qB,GAAG,GAAG,CAAC;IACX,IAAI,OAAOjpB,MAAM,CAACwH,aAAa,KAAK,QAAQ,EAAE,OAAOxH,MAAM,CAACwH,aAAa;IACzE,IAAIxH,MAAM,CAAC+G,cAAc,EAAE;MACzB,IAAIO,SAAS,GAAG9B,MAAM,CAACqG,WAAW,CAAC,GAAGnD,IAAI,CAACe,IAAI,CAACjE,MAAM,CAACqG,WAAW,CAAC,CAACjD,eAAe,CAAC,GAAG,CAAC;MACxF,IAAIsgB,SAAS;MACb,KAAK,IAAIzuB,CAAC,GAAGoR,WAAW,GAAG,CAAC,EAAEpR,CAAC,GAAG+K,MAAM,CAAC1E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;QACvD,IAAI+K,MAAM,CAAC/K,CAAC,CAAC,IAAI,CAACyuB,SAAS,EAAE;UAC3B5hB,SAAS,IAAIoB,IAAI,CAACe,IAAI,CAACjE,MAAM,CAAC/K,CAAC,CAAC,CAACmO,eAAe,CAAC;UACjDqgB,GAAG,IAAI,CAAC;UACR,IAAI3hB,SAAS,GAAGtC,UAAU,EAAEkkB,SAAS,GAAG,IAAI;QAC9C;MACF;MACA,KAAK,IAAIzuB,CAAC,GAAGoR,WAAW,GAAG,CAAC,EAAEpR,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI+K,MAAM,CAAC/K,CAAC,CAAC,IAAI,CAACyuB,SAAS,EAAE;UAC3B5hB,SAAS,IAAI9B,MAAM,CAAC/K,CAAC,CAAC,CAACmO,eAAe;UACtCqgB,GAAG,IAAI,CAAC;UACR,IAAI3hB,SAAS,GAAGtC,UAAU,EAAEkkB,SAAS,GAAG,IAAI;QAC9C;MACF;IACF,CAAC,MAAM;MACL;MACA,IAAIH,IAAI,KAAK,SAAS,EAAE;QACtB,KAAK,IAAItuB,CAAC,GAAGoR,WAAW,GAAG,CAAC,EAAEpR,CAAC,GAAG+K,MAAM,CAAC1E,MAAM,EAAErG,CAAC,IAAI,CAAC,EAAE;UACvD,MAAM0uB,WAAW,GAAGH,KAAK,GAAGpjB,UAAU,CAACnL,CAAC,CAAC,GAAGoL,eAAe,CAACpL,CAAC,CAAC,GAAGmL,UAAU,CAACiG,WAAW,CAAC,GAAG7G,UAAU,GAAGY,UAAU,CAACnL,CAAC,CAAC,GAAGmL,UAAU,CAACiG,WAAW,CAAC,GAAG7G,UAAU;UAC5J,IAAImkB,WAAW,EAAE;YACfF,GAAG,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA,KAAK,IAAIxuB,CAAC,GAAGoR,WAAW,GAAG,CAAC,EAAEpR,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAC5C,MAAM0uB,WAAW,GAAGvjB,UAAU,CAACiG,WAAW,CAAC,GAAGjG,UAAU,CAACnL,CAAC,CAAC,GAAGuK,UAAU;UACxE,IAAImkB,WAAW,EAAE;YACfF,GAAG,IAAI,CAAC;UACV;QACF;MACF;IACF;IACA,OAAOA,GAAG;EACZ;EACA/W,MAAMA,CAAA,EAAG;IACP,MAAM7T,MAAM,GAAG,IAAI;IACnB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACM,SAAS,EAAE;IACjC,MAAM;MACJgH,QAAQ;MACR3F;IACF,CAAC,GAAG3B,MAAM;IACV;IACA,IAAI2B,MAAM,CAACyH,WAAW,EAAE;MACtBpJ,MAAM,CAACmjB,aAAa,CAAC,CAAC;IACxB;IACA,CAAC,GAAGnjB,MAAM,CAACmB,EAAE,CAACgoB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAACroB,OAAO,CAACoQ,OAAO,IAAI;MACrE,IAAIA,OAAO,CAAC6Z,QAAQ,EAAE;QACpB9Z,oBAAoB,CAACjR,MAAM,EAAEkR,OAAO,CAAC;MACvC;IACF,CAAC,CAAC;IACFlR,MAAM,CAACyF,UAAU,CAAC,CAAC;IACnBzF,MAAM,CAACmG,YAAY,CAAC,CAAC;IACrBnG,MAAM,CAACkP,cAAc,CAAC,CAAC;IACvBlP,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;IAC5B,SAAS6D,YAAYA,CAAA,EAAG;MACtB,MAAM8W,cAAc,GAAGhrB,MAAM,CAAC4G,YAAY,GAAG5G,MAAM,CAACmO,SAAS,GAAG,CAAC,CAAC,GAAGnO,MAAM,CAACmO,SAAS;MACrF,MAAM0G,YAAY,GAAGxK,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,GAAG,CAACogB,cAAc,EAAEhrB,MAAM,CAACqP,YAAY,CAAC,CAAC,CAAC,EAAErP,MAAM,CAACwO,YAAY,CAAC,CAAC,CAAC;MACrGxO,MAAM,CAACkU,YAAY,CAACW,YAAY,CAAC;MACjC7U,MAAM,CAACwS,iBAAiB,CAAC,CAAC;MAC1BxS,MAAM,CAACqQ,mBAAmB,CAAC,CAAC;IAC9B;IACA,IAAI4a,UAAU;IACd,IAAItpB,MAAM,CAACoW,QAAQ,IAAIpW,MAAM,CAACoW,QAAQ,CAAC9Q,OAAO,IAAI,CAACtF,MAAM,CAACgH,OAAO,EAAE;MACjEuL,YAAY,CAAC,CAAC;MACd,IAAIvS,MAAM,CAACwO,UAAU,EAAE;QACrBnQ,MAAM,CAACiN,gBAAgB,CAAC,CAAC;MAC3B;IACF,CAAC,MAAM;MACL,IAAI,CAACtL,MAAM,CAACwH,aAAa,KAAK,MAAM,IAAIxH,MAAM,CAACwH,aAAa,GAAG,CAAC,KAAKnJ,MAAM,CAACuP,KAAK,IAAI,CAAC5N,MAAM,CAAC+G,cAAc,EAAE;QAC3G,MAAMvB,MAAM,GAAGnH,MAAM,CAACgH,OAAO,IAAIrF,MAAM,CAACqF,OAAO,CAACC,OAAO,GAAGjH,MAAM,CAACgH,OAAO,CAACG,MAAM,GAAGnH,MAAM,CAACmH,MAAM;QAC/F8jB,UAAU,GAAGjrB,MAAM,CAAC8V,OAAO,CAAC3O,MAAM,CAAC1E,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MAChE,CAAC,MAAM;QACLwoB,UAAU,GAAGjrB,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAACwN,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;MACjE;MACA,IAAI,CAACyd,UAAU,EAAE;QACf/W,YAAY,CAAC,CAAC;MAChB;IACF;IACA,IAAIvS,MAAM,CAAC0K,aAAa,IAAI/E,QAAQ,KAAKtH,MAAM,CAACsH,QAAQ,EAAE;MACxDtH,MAAM,CAACsM,aAAa,CAAC,CAAC;IACxB;IACAtM,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC;EACvB;EACAomB,eAAeA,CAAC4E,YAAY,EAAEC,UAAU,EAAE;IACxC,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAG,IAAI;IACnB;IACA,MAAMnrB,MAAM,GAAG,IAAI;IACnB,MAAMorB,gBAAgB,GAAGprB,MAAM,CAAC2B,MAAM,CAAC8T,SAAS;IAChD,IAAI,CAACyV,YAAY,EAAE;MACjB;MACAA,YAAY,GAAGE,gBAAgB,KAAK,YAAY,GAAG,UAAU,GAAG,YAAY;IAC9E;IACA,IAAIF,YAAY,KAAKE,gBAAgB,IAAIF,YAAY,KAAK,YAAY,IAAIA,YAAY,KAAK,UAAU,EAAE;MACrG,OAAOlrB,MAAM;IACf;IACAA,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACI,MAAM,CAAC,GAAGhN,MAAM,CAAC2B,MAAM,CAAC+K,sBAAsB,GAAG0e,gBAAgB,EAAE,CAAC;IACxFprB,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACG,GAAG,CAAC,GAAG/M,MAAM,CAAC2B,MAAM,CAAC+K,sBAAsB,GAAGwe,YAAY,EAAE,CAAC;IACjFlrB,MAAM,CAAC6lB,oBAAoB,CAAC,CAAC;IAC7B7lB,MAAM,CAAC2B,MAAM,CAAC8T,SAAS,GAAGyV,YAAY;IACtClrB,MAAM,CAACmH,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;MAC/B,IAAI6iB,YAAY,KAAK,UAAU,EAAE;QAC/B7iB,OAAO,CAACvL,KAAK,CAACgB,KAAK,GAAG,EAAE;MAC1B,CAAC,MAAM;QACLuK,OAAO,CAACvL,KAAK,CAACkB,MAAM,GAAG,EAAE;MAC3B;IACF,CAAC,CAAC;IACFgC,MAAM,CAACE,IAAI,CAAC,iBAAiB,CAAC;IAC9B,IAAIirB,UAAU,EAAEnrB,MAAM,CAAC6T,MAAM,CAAC,CAAC;IAC/B,OAAO7T,MAAM;EACf;EACAqrB,uBAAuBA,CAAC5V,SAAS,EAAE;IACjC,MAAMzV,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAAC6G,GAAG,IAAI4O,SAAS,KAAK,KAAK,IAAI,CAACzV,MAAM,CAAC6G,GAAG,IAAI4O,SAAS,KAAK,KAAK,EAAE;IAC7EzV,MAAM,CAAC6G,GAAG,GAAG4O,SAAS,KAAK,KAAK;IAChCzV,MAAM,CAAC4G,YAAY,GAAG5G,MAAM,CAAC2B,MAAM,CAAC8T,SAAS,KAAK,YAAY,IAAIzV,MAAM,CAAC6G,GAAG;IAC5E,IAAI7G,MAAM,CAAC6G,GAAG,EAAE;MACd7G,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACG,GAAG,CAAC,GAAG/M,MAAM,CAAC2B,MAAM,CAAC+K,sBAAsB,KAAK,CAAC;MACrE1M,MAAM,CAACmB,EAAE,CAACwU,GAAG,GAAG,KAAK;IACvB,CAAC,MAAM;MACL3V,MAAM,CAACmB,EAAE,CAACyL,SAAS,CAACI,MAAM,CAAC,GAAGhN,MAAM,CAAC2B,MAAM,CAAC+K,sBAAsB,KAAK,CAAC;MACxE1M,MAAM,CAACmB,EAAE,CAACwU,GAAG,GAAG,KAAK;IACvB;IACA3V,MAAM,CAAC6T,MAAM,CAAC,CAAC;EACjB;EACAyX,KAAKA,CAACC,OAAO,EAAE;IACb,MAAMvrB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACwrB,OAAO,EAAE,OAAO,IAAI;;IAE/B;IACA,IAAIrqB,EAAE,GAAGoqB,OAAO,IAAIvrB,MAAM,CAAC2B,MAAM,CAACR,EAAE;IACpC,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAC1BA,EAAE,GAAGxE,QAAQ,CAAC2U,aAAa,CAACnQ,EAAE,CAAC;IACjC;IACA,IAAI,CAACA,EAAE,EAAE;MACP,OAAO,KAAK;IACd;IACAA,EAAE,CAACnB,MAAM,GAAGA,MAAM;IAClB,IAAImB,EAAE,CAACsqB,UAAU,IAAItqB,EAAE,CAACsqB,UAAU,CAACxP,IAAI,IAAI9a,EAAE,CAACsqB,UAAU,CAACxP,IAAI,CAAC0C,QAAQ,KAAK3e,MAAM,CAAC2B,MAAM,CAACqmB,qBAAqB,CAAC0D,WAAW,CAAC,CAAC,EAAE;MAC5H1rB,MAAM,CAAC8C,SAAS,GAAG,IAAI;IACzB;IACA,MAAM6oB,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,OAAO,IAAI,CAAC3rB,MAAM,CAAC2B,MAAM,CAAC2mB,YAAY,IAAI,EAAE,EAAEsD,IAAI,CAAC,CAAC,CAACvsB,KAAK,CAAC,GAAG,CAAC,CAACkrB,IAAI,CAAC,GAAG,CAAC,EAAE;IAC7E,CAAC;IACD,MAAMsB,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI1qB,EAAE,IAAIA,EAAE,CAACqQ,UAAU,IAAIrQ,EAAE,CAACqQ,UAAU,CAACF,aAAa,EAAE;QACtD,MAAMwa,GAAG,GAAG3qB,EAAE,CAACqQ,UAAU,CAACF,aAAa,CAACqa,kBAAkB,CAAC,CAAC,CAAC;QAC7D;QACA,OAAOG,GAAG;MACZ;MACA,OAAOlxB,eAAe,CAACuG,EAAE,EAAEwqB,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,CAAC;IACD;IACA,IAAIroB,SAAS,GAAGuoB,UAAU,CAAC,CAAC;IAC5B,IAAI,CAACvoB,SAAS,IAAItD,MAAM,CAAC2B,MAAM,CAACsmB,cAAc,EAAE;MAC9C3kB,SAAS,GAAGzH,aAAa,CAAC,KAAK,EAAEmE,MAAM,CAAC2B,MAAM,CAAC2mB,YAAY,CAAC;MAC5DnnB,EAAE,CAACgY,MAAM,CAAC7V,SAAS,CAAC;MACpB1I,eAAe,CAACuG,EAAE,EAAE,IAAInB,MAAM,CAAC2B,MAAM,CAACyF,UAAU,EAAE,CAAC,CAACtG,OAAO,CAACuH,OAAO,IAAI;QACrE/E,SAAS,CAAC6V,MAAM,CAAC9Q,OAAO,CAAC;MAC3B,CAAC,CAAC;IACJ;IACArC,MAAM,CAACC,MAAM,CAACjG,MAAM,EAAE;MACpBmB,EAAE;MACFmC,SAAS;MACToD,QAAQ,EAAE1G,MAAM,CAAC8C,SAAS,IAAI,CAAC3B,EAAE,CAACsqB,UAAU,CAACxP,IAAI,CAAC8P,UAAU,GAAG5qB,EAAE,CAACsqB,UAAU,CAACxP,IAAI,GAAG3Y,SAAS;MAC7FF,MAAM,EAAEpD,MAAM,CAAC8C,SAAS,GAAG3B,EAAE,CAACsqB,UAAU,CAACxP,IAAI,GAAG9a,EAAE;MAClDqqB,OAAO,EAAE,IAAI;MACb;MACA3kB,GAAG,EAAE1F,EAAE,CAACwU,GAAG,CAAC3W,WAAW,CAAC,CAAC,KAAK,KAAK,IAAItE,YAAY,CAACyG,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK;MAC9EyF,YAAY,EAAE5G,MAAM,CAAC2B,MAAM,CAAC8T,SAAS,KAAK,YAAY,KAAKtU,EAAE,CAACwU,GAAG,CAAC3W,WAAW,CAAC,CAAC,KAAK,KAAK,IAAItE,YAAY,CAACyG,EAAE,EAAE,WAAW,CAAC,KAAK,KAAK,CAAC;MACrI2F,QAAQ,EAAEpM,YAAY,CAAC4I,SAAS,EAAE,SAAS,CAAC,KAAK;IACnD,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EACAL,IAAIA,CAAC9B,EAAE,EAAE;IACP,MAAMnB,MAAM,GAAG,IAAI;IACnB,IAAIA,MAAM,CAACO,WAAW,EAAE,OAAOP,MAAM;IACrC,MAAMwrB,OAAO,GAAGxrB,MAAM,CAACsrB,KAAK,CAACnqB,EAAE,CAAC;IAChC,IAAIqqB,OAAO,KAAK,KAAK,EAAE,OAAOxrB,MAAM;IACpCA,MAAM,CAACE,IAAI,CAAC,YAAY,CAAC;;IAEzB;IACA,IAAIF,MAAM,CAAC2B,MAAM,CAACyH,WAAW,EAAE;MAC7BpJ,MAAM,CAACmjB,aAAa,CAAC,CAAC;IACxB;;IAEA;IACAnjB,MAAM,CAACwnB,UAAU,CAAC,CAAC;;IAEnB;IACAxnB,MAAM,CAACyF,UAAU,CAAC,CAAC;;IAEnB;IACAzF,MAAM,CAACmG,YAAY,CAAC,CAAC;IACrB,IAAInG,MAAM,CAAC2B,MAAM,CAAC0K,aAAa,EAAE;MAC/BrM,MAAM,CAACsM,aAAa,CAAC,CAAC;IACxB;;IAEA;IACA,IAAItM,MAAM,CAAC2B,MAAM,CAAC+Z,UAAU,IAAI1b,MAAM,CAACiH,OAAO,EAAE;MAC9CjH,MAAM,CAACmb,aAAa,CAAC,CAAC;IACxB;;IAEA;IACA,IAAInb,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,IAAIlL,MAAM,CAACgH,OAAO,IAAIhH,MAAM,CAAC2B,MAAM,CAACqF,OAAO,CAACC,OAAO,EAAE;MACzEjH,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAAC2B,MAAM,CAAC8U,YAAY,GAAGzW,MAAM,CAACgH,OAAO,CAACqE,YAAY,EAAE,CAAC,EAAErL,MAAM,CAAC2B,MAAM,CAACwR,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC5H,CAAC,MAAM;MACLnT,MAAM,CAAC8V,OAAO,CAAC9V,MAAM,CAAC2B,MAAM,CAAC8U,YAAY,EAAE,CAAC,EAAEzW,MAAM,CAAC2B,MAAM,CAACwR,kBAAkB,EAAE,KAAK,EAAE,IAAI,CAAC;IAC9F;;IAEA;IACA,IAAInT,MAAM,CAAC2B,MAAM,CAACuJ,IAAI,EAAE;MACtBlL,MAAM,CAAC4Y,UAAU,CAACjF,SAAS,EAAE,IAAI,CAAC;IACpC;;IAEA;IACA3T,MAAM,CAAC2kB,YAAY,CAAC,CAAC;IACrB,MAAMqH,YAAY,GAAG,CAAC,GAAGhsB,MAAM,CAACmB,EAAE,CAACgoB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IACxE,IAAInpB,MAAM,CAAC8C,SAAS,EAAE;MACpBkpB,YAAY,CAAChpB,IAAI,CAAC,GAAGhD,MAAM,CAACoD,MAAM,CAAC+lB,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IAC1E;IACA6C,YAAY,CAAClrB,OAAO,CAACoQ,OAAO,IAAI;MAC9B,IAAIA,OAAO,CAAC6Z,QAAQ,EAAE;QACpB9Z,oBAAoB,CAACjR,MAAM,EAAEkR,OAAO,CAAC;MACvC,CAAC,MAAM;QACLA,OAAO,CAACrP,gBAAgB,CAAC,MAAM,EAAElH,CAAC,IAAI;UACpCsW,oBAAoB,CAACjR,MAAM,EAAErF,CAAC,CAACuG,MAAM,CAAC;QACxC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFyQ,OAAO,CAAC3R,MAAM,CAAC;;IAEf;IACAA,MAAM,CAACO,WAAW,GAAG,IAAI;IACzBoR,OAAO,CAAC3R,MAAM,CAAC;;IAEf;IACAA,MAAM,CAACE,IAAI,CAAC,MAAM,CAAC;IACnBF,MAAM,CAACE,IAAI,CAAC,WAAW,CAAC;IACxB,OAAOF,MAAM;EACf;EACAuD,OAAOA,CAAC0oB,cAAc,EAAEC,WAAW,EAAE;IACnC,IAAID,cAAc,KAAK,KAAK,CAAC,EAAE;MAC7BA,cAAc,GAAG,IAAI;IACvB;IACA,IAAIC,WAAW,KAAK,KAAK,CAAC,EAAE;MAC1BA,WAAW,GAAG,IAAI;IACpB;IACA,MAAMlsB,MAAM,GAAG,IAAI;IACnB,MAAM;MACJ2B,MAAM;MACNR,EAAE;MACFmC,SAAS;MACT6D;IACF,CAAC,GAAGnH,MAAM;IACV,IAAI,OAAOA,MAAM,CAAC2B,MAAM,KAAK,WAAW,IAAI3B,MAAM,CAACM,SAAS,EAAE;MAC5D,OAAO,IAAI;IACb;IACAN,MAAM,CAACE,IAAI,CAAC,eAAe,CAAC;;IAE5B;IACAF,MAAM,CAACO,WAAW,GAAG,KAAK;;IAE1B;IACAP,MAAM,CAAC6kB,YAAY,CAAC,CAAC;;IAErB;IACA,IAAIljB,MAAM,CAACuJ,IAAI,EAAE;MACflL,MAAM,CAACgb,WAAW,CAAC,CAAC;IACtB;;IAEA;IACA,IAAIkR,WAAW,EAAE;MACflsB,MAAM,CAAC0nB,aAAa,CAAC,CAAC;MACtB,IAAIvmB,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;QAChCA,EAAE,CAACuQ,eAAe,CAAC,OAAO,CAAC;MAC7B;MACA,IAAIpO,SAAS,EAAE;QACbA,SAAS,CAACoO,eAAe,CAAC,OAAO,CAAC;MACpC;MACA,IAAIvK,MAAM,IAAIA,MAAM,CAAC1E,MAAM,EAAE;QAC3B0E,MAAM,CAACrG,OAAO,CAACuH,OAAO,IAAI;UACxBA,OAAO,CAACuE,SAAS,CAACI,MAAM,CAACrL,MAAM,CAACmN,iBAAiB,EAAEnN,MAAM,CAACoN,sBAAsB,EAAEpN,MAAM,CAACkP,gBAAgB,EAAElP,MAAM,CAACmP,cAAc,EAAEnP,MAAM,CAACoP,cAAc,CAAC;UACxJ1I,OAAO,CAACqJ,eAAe,CAAC,OAAO,CAAC;UAChCrJ,OAAO,CAACqJ,eAAe,CAAC,yBAAyB,CAAC;QACpD,CAAC,CAAC;MACJ;IACF;IACA1R,MAAM,CAACE,IAAI,CAAC,SAAS,CAAC;;IAEtB;IACA8F,MAAM,CAACqD,IAAI,CAACrJ,MAAM,CAAC+D,eAAe,CAAC,CAACjD,OAAO,CAAC8oB,SAAS,IAAI;MACvD5pB,MAAM,CAACoE,GAAG,CAACwlB,SAAS,CAAC;IACvB,CAAC,CAAC;IACF,IAAIqC,cAAc,KAAK,KAAK,EAAE;MAC5B,IAAIjsB,MAAM,CAACmB,EAAE,IAAI,OAAOnB,MAAM,CAACmB,EAAE,KAAK,QAAQ,EAAE;QAC9CnB,MAAM,CAACmB,EAAE,CAACnB,MAAM,GAAG,IAAI;MACzB;MACAzD,WAAW,CAACyD,MAAM,CAAC;IACrB;IACAA,MAAM,CAACM,SAAS,GAAG,IAAI;IACvB,OAAO,IAAI;EACb;EACA,OAAO6rB,cAAcA,CAACC,WAAW,EAAE;IACjCjwB,MAAM,CAAC4sB,gBAAgB,EAAEqD,WAAW,CAAC;EACvC;EACA,WAAWrD,gBAAgBA,CAAA,EAAG;IAC5B,OAAOA,gBAAgB;EACzB;EACA,WAAWhB,QAAQA,CAAA,EAAG;IACpB,OAAOA,QAAQ;EACjB;EACA,OAAOsE,aAAaA,CAAC5C,GAAG,EAAE;IACxB,IAAI,CAACT,MAAM,CAACC,SAAS,CAACO,WAAW,EAAER,MAAM,CAACC,SAAS,CAACO,WAAW,GAAG,EAAE;IACpE,MAAMD,OAAO,GAAGP,MAAM,CAACC,SAAS,CAACO,WAAW;IAC5C,IAAI,OAAOC,GAAG,KAAK,UAAU,IAAIF,OAAO,CAAC/qB,OAAO,CAACirB,GAAG,CAAC,GAAG,CAAC,EAAE;MACzDF,OAAO,CAACvmB,IAAI,CAACymB,GAAG,CAAC;IACnB;EACF;EACA,OAAO6C,GAAGA,CAACC,MAAM,EAAE;IACjB,IAAI9nB,KAAK,CAACY,OAAO,CAACknB,MAAM,CAAC,EAAE;MACzBA,MAAM,CAACzrB,OAAO,CAAC0rB,CAAC,IAAIxD,MAAM,CAACqD,aAAa,CAACG,CAAC,CAAC,CAAC;MAC5C,OAAOxD,MAAM;IACf;IACAA,MAAM,CAACqD,aAAa,CAACE,MAAM,CAAC;IAC5B,OAAOvD,MAAM;EACf;AACF;AACAhjB,MAAM,CAACqD,IAAI,CAACyf,UAAU,CAAC,CAAChoB,OAAO,CAAC2rB,cAAc,IAAI;EAChDzmB,MAAM,CAACqD,IAAI,CAACyf,UAAU,CAAC2D,cAAc,CAAC,CAAC,CAAC3rB,OAAO,CAAC4rB,WAAW,IAAI;IAC7D1D,MAAM,CAACC,SAAS,CAACyD,WAAW,CAAC,GAAG5D,UAAU,CAAC2D,cAAc,CAAC,CAACC,WAAW,CAAC;EACzE,CAAC,CAAC;AACJ,CAAC,CAAC;AACF1D,MAAM,CAACsD,GAAG,CAAC,CAACxsB,MAAM,EAAEiC,QAAQ,CAAC,CAAC;AAE9B,SAASinB,MAAM,IAAI2D,CAAC,EAAE5E,QAAQ,IAAI6E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}