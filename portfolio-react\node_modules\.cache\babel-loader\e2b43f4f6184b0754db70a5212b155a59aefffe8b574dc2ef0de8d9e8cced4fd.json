{"ast": null, "code": "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\nexport { Pagination as default };", "map": {"version": 3, "names": ["c", "classesToSelector", "createElementIfNotDefined", "m", "makeElementsArray", "h", "elementOuterSize", "i", "elementIndex", "s", "setInnerHTML", "b", "elementParents", "Pagination", "_ref", "swiper", "extendParams", "on", "emit", "pfx", "pagination", "el", "bulletElement", "clickable", "hideOnClick", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "type", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "hiddenClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "lockClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "bulletSize", "dynamicBulletIndex", "isPaginationDisabled", "params", "Array", "isArray", "length", "setSideBullets", "bulletEl", "position", "classList", "add", "getMoveDirection", "prevIndex", "nextIndex", "onBulletClick", "e", "target", "closest", "preventDefault", "index", "slidesPerGroup", "loop", "realIndex", "moveDirection", "slides", "slideNext", "slidePrev", "slideToLoop", "slideTo", "update", "rtl", "current", "previousIndex", "<PERSON><PERSON><PERSON><PERSON>", "virtual", "enabled", "total", "Math", "ceil", "snapGrid", "previousRealIndex", "floor", "snapIndex", "previousSnapIndex", "activeIndex", "firstIndex", "lastIndex", "midIndex", "isHorizontal", "for<PERSON>ach", "subEl", "style", "undefined", "max", "min", "classesToRemove", "map", "suffix", "includes", "split", "flat", "remove", "bullet", "bulletIndex", "isElement", "setAttribute", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "querySelectorAll", "fractionEl", "textContent", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "transform", "transitionDuration", "speed", "watchOverflow", "isLocked", "render", "grid", "rows", "paginationHTML", "numberOfBullets", "freeMode", "call", "push", "init", "originalParams", "querySelector", "document", "uniqueNavElements", "find", "Object", "assign", "addEventListener", "destroy", "removeEventListener", "disable", "_s", "targetEl", "contains", "navigation", "nextEl", "prevEl", "isHidden", "toggle", "enable", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/pagination.mjs"], "sourcesContent": ["import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,iBAAiB,QAAQ,mCAAmC;AAC1E,SAASD,CAAC,IAAIE,yBAAyB,QAAQ,6CAA6C;AAC5F,SAASC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAE9I,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,EAAE;IACFC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAMK,GAAG,GAAG,mBAAmB;EAC/BH,YAAY,CAAC;IACXI,UAAU,EAAE;MACVC,EAAE,EAAE,IAAI;MACRC,aAAa,EAAE,MAAM;MACrBC,SAAS,EAAE,KAAK;MAChBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,IAAI,EAAE,SAAS;MACf;MACAC,cAAc,EAAE,KAAK;MACrBC,kBAAkB,EAAE,CAAC;MACrBC,qBAAqB,EAAEC,MAAM,IAAIA,MAAM;MACvCC,mBAAmB,EAAED,MAAM,IAAIA,MAAM;MACrCE,WAAW,EAAE,GAAGjB,GAAG,SAAS;MAC5BkB,iBAAiB,EAAE,GAAGlB,GAAG,gBAAgB;MACzCmB,aAAa,EAAE,GAAGnB,GAAG,GAAG;MACxBoB,YAAY,EAAE,GAAGpB,GAAG,UAAU;MAC9BqB,UAAU,EAAE,GAAGrB,GAAG,QAAQ;MAC1BsB,WAAW,EAAE,GAAGtB,GAAG,SAAS;MAC5BuB,oBAAoB,EAAE,GAAGvB,GAAG,mBAAmB;MAC/CwB,wBAAwB,EAAE,GAAGxB,GAAG,uBAAuB;MACvDyB,cAAc,EAAE,GAAGzB,GAAG,YAAY;MAClC0B,SAAS,EAAE,GAAG1B,GAAG,OAAO;MACxB2B,eAAe,EAAE,GAAG3B,GAAG,aAAa;MACpC4B,aAAa,EAAE,GAAG5B,GAAG,WAAW;MAChC6B,uBAAuB,EAAE,GAAG7B,GAAG;IACjC;EACF,CAAC,CAAC;EACFJ,MAAM,CAACK,UAAU,GAAG;IAClBC,EAAE,EAAE,IAAI;IACR4B,OAAO,EAAE;EACX,CAAC;EACD,IAAIC,UAAU;EACd,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,SAASC,oBAAoBA,CAAA,EAAG;IAC9B,OAAO,CAACrC,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACC,EAAE,IAAI,CAACN,MAAM,CAACK,UAAU,CAACC,EAAE,IAAIiC,KAAK,CAACC,OAAO,CAACxC,MAAM,CAACK,UAAU,CAACC,EAAE,CAAC,IAAIN,MAAM,CAACK,UAAU,CAACC,EAAE,CAACmC,MAAM,KAAK,CAAC;EAC1I;EACA,SAASC,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC1C,MAAM;MACJtB;IACF,CAAC,GAAGtB,MAAM,CAACsC,MAAM,CAACjC,UAAU;IAC5B,IAAI,CAACsC,QAAQ,EAAE;IACfA,QAAQ,GAAGA,QAAQ,CAAC,GAAGC,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM,gBAAgB,CAAC;IACjF,IAAID,QAAQ,EAAE;MACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,GAAGxB,iBAAiB,IAAIsB,QAAQ,EAAE,CAAC;MAC1DD,QAAQ,GAAGA,QAAQ,CAAC,GAAGC,QAAQ,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM,gBAAgB,CAAC;MACjF,IAAID,QAAQ,EAAE;QACZA,QAAQ,CAACE,SAAS,CAACC,GAAG,CAAC,GAAGxB,iBAAiB,IAAIsB,QAAQ,IAAIA,QAAQ,EAAE,CAAC;MACxE;IACF;EACF;EACA,SAASG,gBAAgBA,CAACC,SAAS,EAAEC,SAAS,EAAER,MAAM,EAAE;IACtDO,SAAS,GAAGA,SAAS,GAAGP,MAAM;IAC9BQ,SAAS,GAAGA,SAAS,GAAGR,MAAM;IAC9B,IAAIQ,SAAS,KAAKD,SAAS,GAAG,CAAC,EAAE;MAC/B,OAAO,MAAM;IACf,CAAC,MAAM,IAAIC,SAAS,KAAKD,SAAS,GAAG,CAAC,EAAE;MACtC,OAAO,UAAU;IACnB;IACA;EACF;EACA,SAASE,aAAaA,CAACC,CAAC,EAAE;IACxB,MAAMR,QAAQ,GAAGQ,CAAC,CAACC,MAAM,CAACC,OAAO,CAACnE,iBAAiB,CAACc,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACgB,WAAW,CAAC,CAAC;IAC1F,IAAI,CAACsB,QAAQ,EAAE;MACb;IACF;IACAQ,CAAC,CAACG,cAAc,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG9D,YAAY,CAACkD,QAAQ,CAAC,GAAG3C,MAAM,CAACsC,MAAM,CAACkB,cAAc;IACnE,IAAIxD,MAAM,CAACsC,MAAM,CAACmB,IAAI,EAAE;MACtB,IAAIzD,MAAM,CAAC0D,SAAS,KAAKH,KAAK,EAAE;MAChC,MAAMI,aAAa,GAAGZ,gBAAgB,CAAC/C,MAAM,CAAC0D,SAAS,EAAEH,KAAK,EAAEvD,MAAM,CAAC4D,MAAM,CAACnB,MAAM,CAAC;MACrF,IAAIkB,aAAa,KAAK,MAAM,EAAE;QAC5B3D,MAAM,CAAC6D,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIF,aAAa,KAAK,UAAU,EAAE;QACvC3D,MAAM,CAAC8D,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM;QACL9D,MAAM,CAAC+D,WAAW,CAACR,KAAK,CAAC;MAC3B;IACF,CAAC,MAAM;MACLvD,MAAM,CAACgE,OAAO,CAACT,KAAK,CAAC;IACvB;EACF;EACA,SAASU,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMC,GAAG,GAAGlE,MAAM,CAACkE,GAAG;IACtB,MAAM5B,MAAM,GAAGtC,MAAM,CAACsC,MAAM,CAACjC,UAAU;IACvC,IAAIgC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,IAAI/B,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1B;IACA,IAAI6D,OAAO;IACX,IAAIC,aAAa;IACjB,MAAMC,YAAY,GAAGrE,MAAM,CAACsE,OAAO,IAAItE,MAAM,CAACsC,MAAM,CAACgC,OAAO,CAACC,OAAO,GAAGvE,MAAM,CAACsE,OAAO,CAACV,MAAM,CAACnB,MAAM,GAAGzC,MAAM,CAAC4D,MAAM,CAACnB,MAAM;IAC1H,MAAM+B,KAAK,GAAGxE,MAAM,CAACsC,MAAM,CAACmB,IAAI,GAAGgB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGrE,MAAM,CAACsC,MAAM,CAACkB,cAAc,CAAC,GAAGxD,MAAM,CAAC2E,QAAQ,CAAClC,MAAM;IAClH,IAAIzC,MAAM,CAACsC,MAAM,CAACmB,IAAI,EAAE;MACtBW,aAAa,GAAGpE,MAAM,CAAC4E,iBAAiB,IAAI,CAAC;MAC7CT,OAAO,GAAGnE,MAAM,CAACsC,MAAM,CAACkB,cAAc,GAAG,CAAC,GAAGiB,IAAI,CAACI,KAAK,CAAC7E,MAAM,CAAC0D,SAAS,GAAG1D,MAAM,CAACsC,MAAM,CAACkB,cAAc,CAAC,GAAGxD,MAAM,CAAC0D,SAAS;IAC7H,CAAC,MAAM,IAAI,OAAO1D,MAAM,CAAC8E,SAAS,KAAK,WAAW,EAAE;MAClDX,OAAO,GAAGnE,MAAM,CAAC8E,SAAS;MAC1BV,aAAa,GAAGpE,MAAM,CAAC+E,iBAAiB;IAC1C,CAAC,MAAM;MACLX,aAAa,GAAGpE,MAAM,CAACoE,aAAa,IAAI,CAAC;MACzCD,OAAO,GAAGnE,MAAM,CAACgF,WAAW,IAAI,CAAC;IACnC;IACA;IACA,IAAI1C,MAAM,CAACvB,IAAI,KAAK,SAAS,IAAIf,MAAM,CAACK,UAAU,CAAC6B,OAAO,IAAIlC,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAACO,MAAM,GAAG,CAAC,EAAE;MAClG,MAAMP,OAAO,GAAGlC,MAAM,CAACK,UAAU,CAAC6B,OAAO;MACzC,IAAI+C,UAAU;MACd,IAAIC,SAAS;MACb,IAAIC,QAAQ;MACZ,IAAI7C,MAAM,CAACtB,cAAc,EAAE;QACzBmB,UAAU,GAAG5C,gBAAgB,CAAC2C,OAAO,CAAC,CAAC,CAAC,EAAElC,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,EAAE,IAAI,CAAC;QAC3F9E,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAI;UAClBA,KAAK,CAACC,KAAK,CAACvF,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,GAAGjD,UAAU,IAAIG,MAAM,CAACrB,kBAAkB,GAAG,CAAC,CAAC,IAAI;QAC/G,CAAC,CAAC;QACF,IAAIqB,MAAM,CAACrB,kBAAkB,GAAG,CAAC,IAAImD,aAAa,KAAKoB,SAAS,EAAE;UAChEpD,kBAAkB,IAAI+B,OAAO,IAAIC,aAAa,IAAI,CAAC,CAAC;UACpD,IAAIhC,kBAAkB,GAAGE,MAAM,CAACrB,kBAAkB,GAAG,CAAC,EAAE;YACtDmB,kBAAkB,GAAGE,MAAM,CAACrB,kBAAkB,GAAG,CAAC;UACpD,CAAC,MAAM,IAAImB,kBAAkB,GAAG,CAAC,EAAE;YACjCA,kBAAkB,GAAG,CAAC;UACxB;QACF;QACA6C,UAAU,GAAGR,IAAI,CAACgB,GAAG,CAACtB,OAAO,GAAG/B,kBAAkB,EAAE,CAAC,CAAC;QACtD8C,SAAS,GAAGD,UAAU,IAAIR,IAAI,CAACiB,GAAG,CAACxD,OAAO,CAACO,MAAM,EAAEH,MAAM,CAACrB,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAClFkE,QAAQ,GAAG,CAACD,SAAS,GAAGD,UAAU,IAAI,CAAC;MACzC;MACA/C,OAAO,CAACmD,OAAO,CAAC1C,QAAQ,IAAI;QAC1B,MAAMgD,eAAe,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,CAACC,GAAG,CAACC,MAAM,IAAI,GAAGvD,MAAM,CAAChB,iBAAiB,GAAGuE,MAAM,EAAE,CAAC,CAAC,CAACD,GAAG,CAAClG,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACoG,QAAQ,CAAC,GAAG,CAAC,GAAGpG,CAAC,CAACqG,KAAK,CAAC,GAAG,CAAC,GAAGrG,CAAC,CAAC,CAACsG,IAAI,CAAC,CAAC;QAC3NrD,QAAQ,CAACE,SAAS,CAACoD,MAAM,CAAC,GAAGN,eAAe,CAAC;MAC/C,CAAC,CAAC;MACF,IAAIrF,EAAE,CAACmC,MAAM,GAAG,CAAC,EAAE;QACjBP,OAAO,CAACmD,OAAO,CAACa,MAAM,IAAI;UACxB,MAAMC,WAAW,GAAG1G,YAAY,CAACyG,MAAM,CAAC;UACxC,IAAIC,WAAW,KAAKhC,OAAO,EAAE;YAC3B+B,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAGR,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;UAC9D,CAAC,MAAM,IAAI/F,MAAM,CAACoG,SAAS,EAAE;YAC3BF,MAAM,CAACG,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;UACvC;UACA,IAAI/D,MAAM,CAACtB,cAAc,EAAE;YACzB,IAAImF,WAAW,IAAIlB,UAAU,IAAIkB,WAAW,IAAIjB,SAAS,EAAE;cACzDgB,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAG,GAAGR,MAAM,CAAChB,iBAAiB,OAAO,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;YACxE;YACA,IAAII,WAAW,KAAKlB,UAAU,EAAE;cAC9BvC,cAAc,CAACwD,MAAM,EAAE,MAAM,CAAC;YAChC;YACA,IAAIC,WAAW,KAAKjB,SAAS,EAAE;cAC7BxC,cAAc,CAACwD,MAAM,EAAE,MAAM,CAAC;YAChC;UACF;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMA,MAAM,GAAGhE,OAAO,CAACiC,OAAO,CAAC;QAC/B,IAAI+B,MAAM,EAAE;UACVA,MAAM,CAACrD,SAAS,CAACC,GAAG,CAAC,GAAGR,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9D;QACA,IAAI/F,MAAM,CAACoG,SAAS,EAAE;UACpBlE,OAAO,CAACmD,OAAO,CAAC,CAAC1C,QAAQ,EAAEwD,WAAW,KAAK;YACzCxD,QAAQ,CAAC0D,YAAY,CAAC,MAAM,EAAEF,WAAW,KAAKhC,OAAO,GAAG,eAAe,GAAG,QAAQ,CAAC;UACrF,CAAC,CAAC;QACJ;QACA,IAAI7B,MAAM,CAACtB,cAAc,EAAE;UACzB,MAAMsF,oBAAoB,GAAGpE,OAAO,CAAC+C,UAAU,CAAC;UAChD,MAAMsB,mBAAmB,GAAGrE,OAAO,CAACgD,SAAS,CAAC;UAC9C,KAAK,IAAI1F,CAAC,GAAGyF,UAAU,EAAEzF,CAAC,IAAI0F,SAAS,EAAE1F,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI0C,OAAO,CAAC1C,CAAC,CAAC,EAAE;cACd0C,OAAO,CAAC1C,CAAC,CAAC,CAACqD,SAAS,CAACC,GAAG,CAAC,GAAG,GAAGR,MAAM,CAAChB,iBAAiB,OAAO,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5E;UACF;UACArD,cAAc,CAAC4D,oBAAoB,EAAE,MAAM,CAAC;UAC5C5D,cAAc,CAAC6D,mBAAmB,EAAE,MAAM,CAAC;QAC7C;MACF;MACA,IAAIjE,MAAM,CAACtB,cAAc,EAAE;QACzB,MAAMwF,oBAAoB,GAAG/B,IAAI,CAACiB,GAAG,CAACxD,OAAO,CAACO,MAAM,EAAEH,MAAM,CAACrB,kBAAkB,GAAG,CAAC,CAAC;QACpF,MAAMwF,aAAa,GAAG,CAACtE,UAAU,GAAGqE,oBAAoB,GAAGrE,UAAU,IAAI,CAAC,GAAGgD,QAAQ,GAAGhD,UAAU;QAClG,MAAMuE,UAAU,GAAGxC,GAAG,GAAG,OAAO,GAAG,MAAM;QACzChC,OAAO,CAACmD,OAAO,CAACa,MAAM,IAAI;UACxBA,MAAM,CAACX,KAAK,CAACvF,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAGsB,UAAU,GAAG,KAAK,CAAC,GAAG,GAAGD,aAAa,IAAI;QACjF,CAAC,CAAC;MACJ;IACF;IACAnG,EAAE,CAAC+E,OAAO,CAAC,CAACC,KAAK,EAAEqB,UAAU,KAAK;MAChC,IAAIrE,MAAM,CAACvB,IAAI,KAAK,UAAU,EAAE;QAC9BuE,KAAK,CAACsB,gBAAgB,CAAC1H,iBAAiB,CAACoD,MAAM,CAACd,YAAY,CAAC,CAAC,CAAC6D,OAAO,CAACwB,UAAU,IAAI;UACnFA,UAAU,CAACC,WAAW,GAAGxE,MAAM,CAACpB,qBAAqB,CAACiD,OAAO,GAAG,CAAC,CAAC;QACpE,CAAC,CAAC;QACFmB,KAAK,CAACsB,gBAAgB,CAAC1H,iBAAiB,CAACoD,MAAM,CAACb,UAAU,CAAC,CAAC,CAAC4D,OAAO,CAAC0B,OAAO,IAAI;UAC9EA,OAAO,CAACD,WAAW,GAAGxE,MAAM,CAAClB,mBAAmB,CAACoD,KAAK,CAAC;QACzD,CAAC,CAAC;MACJ;MACA,IAAIlC,MAAM,CAACvB,IAAI,KAAK,aAAa,EAAE;QACjC,IAAIiG,oBAAoB;QACxB,IAAI1E,MAAM,CAACxB,mBAAmB,EAAE;UAC9BkG,oBAAoB,GAAGhH,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG,UAAU,GAAG,YAAY;QAC1E,CAAC,MAAM;UACL4B,oBAAoB,GAAGhH,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG,YAAY,GAAG,UAAU;QAC1E;QACA,MAAM6B,KAAK,GAAG,CAAC9C,OAAO,GAAG,CAAC,IAAIK,KAAK;QACnC,IAAI0C,MAAM,GAAG,CAAC;QACd,IAAIC,MAAM,GAAG,CAAC;QACd,IAAIH,oBAAoB,KAAK,YAAY,EAAE;UACzCE,MAAM,GAAGD,KAAK;QAChB,CAAC,MAAM;UACLE,MAAM,GAAGF,KAAK;QAChB;QACA3B,KAAK,CAACsB,gBAAgB,CAAC1H,iBAAiB,CAACoD,MAAM,CAACX,oBAAoB,CAAC,CAAC,CAAC0D,OAAO,CAAC+B,UAAU,IAAI;UAC3FA,UAAU,CAAC7B,KAAK,CAAC8B,SAAS,GAAG,6BAA6BH,MAAM,YAAYC,MAAM,GAAG;UACrFC,UAAU,CAAC7B,KAAK,CAAC+B,kBAAkB,GAAG,GAAGtH,MAAM,CAACsC,MAAM,CAACiF,KAAK,IAAI;QAClE,CAAC,CAAC;MACJ;MACA,IAAIjF,MAAM,CAACvB,IAAI,KAAK,QAAQ,IAAIuB,MAAM,CAACzB,YAAY,EAAE;QACnDlB,YAAY,CAAC2F,KAAK,EAAEhD,MAAM,CAACzB,YAAY,CAACb,MAAM,EAAEmE,OAAO,GAAG,CAAC,EAAEK,KAAK,CAAC,CAAC;QACpE,IAAImC,UAAU,KAAK,CAAC,EAAExG,IAAI,CAAC,kBAAkB,EAAEmF,KAAK,CAAC;MACvD,CAAC,MAAM;QACL,IAAIqB,UAAU,KAAK,CAAC,EAAExG,IAAI,CAAC,kBAAkB,EAAEmF,KAAK,CAAC;QACrDnF,IAAI,CAAC,kBAAkB,EAAEmF,KAAK,CAAC;MACjC;MACA,IAAItF,MAAM,CAACsC,MAAM,CAACkF,aAAa,IAAIxH,MAAM,CAACuE,OAAO,EAAE;QACjDe,KAAK,CAACzC,SAAS,CAAC7C,MAAM,CAACyH,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC,CAACnF,MAAM,CAACR,SAAS,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EACA,SAAS4F,MAAMA,CAAA,EAAG;IAChB;IACA,MAAMpF,MAAM,GAAGtC,MAAM,CAACsC,MAAM,CAACjC,UAAU;IACvC,IAAIgC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,MAAMgC,YAAY,GAAGrE,MAAM,CAACsE,OAAO,IAAItE,MAAM,CAACsC,MAAM,CAACgC,OAAO,CAACC,OAAO,GAAGvE,MAAM,CAACsE,OAAO,CAACV,MAAM,CAACnB,MAAM,GAAGzC,MAAM,CAAC2H,IAAI,IAAI3H,MAAM,CAACsC,MAAM,CAACqF,IAAI,CAACC,IAAI,GAAG,CAAC,GAAG5H,MAAM,CAAC4D,MAAM,CAACnB,MAAM,GAAGgC,IAAI,CAACC,IAAI,CAAC1E,MAAM,CAACsC,MAAM,CAACqF,IAAI,CAACC,IAAI,CAAC,GAAG5H,MAAM,CAAC4D,MAAM,CAACnB,MAAM;IACnO,IAAInC,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7BA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1B,IAAIuH,cAAc,GAAG,EAAE;IACvB,IAAIvF,MAAM,CAACvB,IAAI,KAAK,SAAS,EAAE;MAC7B,IAAI+G,eAAe,GAAG9H,MAAM,CAACsC,MAAM,CAACmB,IAAI,GAAGgB,IAAI,CAACC,IAAI,CAACL,YAAY,GAAGrE,MAAM,CAACsC,MAAM,CAACkB,cAAc,CAAC,GAAGxD,MAAM,CAAC2E,QAAQ,CAAClC,MAAM;MAC1H,IAAIzC,MAAM,CAACsC,MAAM,CAACyF,QAAQ,IAAI/H,MAAM,CAACsC,MAAM,CAACyF,QAAQ,CAACxD,OAAO,IAAIuD,eAAe,GAAGzD,YAAY,EAAE;QAC9FyD,eAAe,GAAGzD,YAAY;MAChC;MACA,KAAK,IAAI7E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsI,eAAe,EAAEtI,CAAC,IAAI,CAAC,EAAE;QAC3C,IAAI8C,MAAM,CAAC5B,YAAY,EAAE;UACvBmH,cAAc,IAAIvF,MAAM,CAAC5B,YAAY,CAACsH,IAAI,CAAChI,MAAM,EAAER,CAAC,EAAE8C,MAAM,CAACjB,WAAW,CAAC;QAC3E,CAAC,MAAM;UACL;UACAwG,cAAc,IAAI,IAAIvF,MAAM,CAAC/B,aAAa,IAAIP,MAAM,CAACoG,SAAS,GAAG,eAAe,GAAG,EAAE,WAAW9D,MAAM,CAACjB,WAAW,OAAOiB,MAAM,CAAC/B,aAAa,GAAG;QAClJ;MACF;IACF;IACA,IAAI+B,MAAM,CAACvB,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAIuB,MAAM,CAAC1B,cAAc,EAAE;QACzBiH,cAAc,GAAGvF,MAAM,CAAC1B,cAAc,CAACoH,IAAI,CAAChI,MAAM,EAAEsC,MAAM,CAACd,YAAY,EAAEc,MAAM,CAACb,UAAU,CAAC;MAC7F,CAAC,MAAM;QACLoG,cAAc,GAAG,gBAAgBvF,MAAM,CAACd,YAAY,WAAW,GAAG,KAAK,GAAG,gBAAgBc,MAAM,CAACb,UAAU,WAAW;MACxH;IACF;IACA,IAAIa,MAAM,CAACvB,IAAI,KAAK,aAAa,EAAE;MACjC,IAAIuB,MAAM,CAAC3B,iBAAiB,EAAE;QAC5BkH,cAAc,GAAGvF,MAAM,CAAC3B,iBAAiB,CAACqH,IAAI,CAAChI,MAAM,EAAEsC,MAAM,CAACX,oBAAoB,CAAC;MACrF,CAAC,MAAM;QACLkG,cAAc,GAAG,gBAAgBvF,MAAM,CAACX,oBAAoB,WAAW;MACzE;IACF;IACA3B,MAAM,CAACK,UAAU,CAAC6B,OAAO,GAAG,EAAE;IAC9B5B,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAI;MAClB,IAAIhD,MAAM,CAACvB,IAAI,KAAK,QAAQ,EAAE;QAC5BpB,YAAY,CAAC2F,KAAK,EAAEuC,cAAc,IAAI,EAAE,CAAC;MAC3C;MACA,IAAIvF,MAAM,CAACvB,IAAI,KAAK,SAAS,EAAE;QAC7Bf,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAAC+F,IAAI,CAAC,GAAG3C,KAAK,CAACsB,gBAAgB,CAAC1H,iBAAiB,CAACoD,MAAM,CAACjB,WAAW,CAAC,CAAC,CAAC;MAClG;IACF,CAAC,CAAC;IACF,IAAIiB,MAAM,CAACvB,IAAI,KAAK,QAAQ,EAAE;MAC5BZ,IAAI,CAAC,kBAAkB,EAAEG,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC;EACF;EACA,SAAS4H,IAAIA,CAAA,EAAG;IACdlI,MAAM,CAACsC,MAAM,CAACjC,UAAU,GAAGlB,yBAAyB,CAACa,MAAM,EAAEA,MAAM,CAACmI,cAAc,CAAC9H,UAAU,EAAEL,MAAM,CAACsC,MAAM,CAACjC,UAAU,EAAE;MACvHC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,MAAMgC,MAAM,GAAGtC,MAAM,CAACsC,MAAM,CAACjC,UAAU;IACvC,IAAI,CAACiC,MAAM,CAAChC,EAAE,EAAE;IAChB,IAAIA,EAAE;IACN,IAAI,OAAOgC,MAAM,CAAChC,EAAE,KAAK,QAAQ,IAAIN,MAAM,CAACoG,SAAS,EAAE;MACrD9F,EAAE,GAAGN,MAAM,CAACM,EAAE,CAAC8H,aAAa,CAAC9F,MAAM,CAAChC,EAAE,CAAC;IACzC;IACA,IAAI,CAACA,EAAE,IAAI,OAAOgC,MAAM,CAAChC,EAAE,KAAK,QAAQ,EAAE;MACxCA,EAAE,GAAG,CAAC,GAAG+H,QAAQ,CAACzB,gBAAgB,CAACtE,MAAM,CAAChC,EAAE,CAAC,CAAC;IAChD;IACA,IAAI,CAACA,EAAE,EAAE;MACPA,EAAE,GAAGgC,MAAM,CAAChC,EAAE;IAChB;IACA,IAAI,CAACA,EAAE,IAAIA,EAAE,CAACmC,MAAM,KAAK,CAAC,EAAE;IAC5B,IAAIzC,MAAM,CAACsC,MAAM,CAACgG,iBAAiB,IAAI,OAAOhG,MAAM,CAAChC,EAAE,KAAK,QAAQ,IAAIiC,KAAK,CAACC,OAAO,CAAClC,EAAE,CAAC,IAAIA,EAAE,CAACmC,MAAM,GAAG,CAAC,EAAE;MAC1GnC,EAAE,GAAG,CAAC,GAAGN,MAAM,CAACM,EAAE,CAACsG,gBAAgB,CAACtE,MAAM,CAAChC,EAAE,CAAC,CAAC;MAC/C;MACA,IAAIA,EAAE,CAACmC,MAAM,GAAG,CAAC,EAAE;QACjBnC,EAAE,GAAGA,EAAE,CAACiI,IAAI,CAACjD,KAAK,IAAI;UACpB,IAAIzF,cAAc,CAACyF,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,KAAKtF,MAAM,CAACM,EAAE,EAAE,OAAO,KAAK;UACnE,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;IACF;IACA,IAAIiC,KAAK,CAACC,OAAO,CAAClC,EAAE,CAAC,IAAIA,EAAE,CAACmC,MAAM,KAAK,CAAC,EAAEnC,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;IACpDkI,MAAM,CAACC,MAAM,CAACzI,MAAM,CAACK,UAAU,EAAE;MAC/BC;IACF,CAAC,CAAC;IACFA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAI;MAClB,IAAIhD,MAAM,CAACvB,IAAI,KAAK,SAAS,IAAIuB,MAAM,CAAC9B,SAAS,EAAE;QACjD8E,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC,GAAG,CAACR,MAAM,CAACT,cAAc,IAAI,EAAE,EAAEkE,KAAK,CAAC,GAAG,CAAC,CAAC;MAClE;MACAT,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACf,aAAa,GAAGe,MAAM,CAACvB,IAAI,CAAC;MACvDuE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC9C,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;MAC1F,IAAIM,MAAM,CAACvB,IAAI,KAAK,SAAS,IAAIuB,MAAM,CAACtB,cAAc,EAAE;QACtDsE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC,GAAGR,MAAM,CAACf,aAAa,GAAGe,MAAM,CAACvB,IAAI,UAAU,CAAC;QACpEqB,kBAAkB,GAAG,CAAC;QACtB,IAAIE,MAAM,CAACrB,kBAAkB,GAAG,CAAC,EAAE;UACjCqB,MAAM,CAACrB,kBAAkB,GAAG,CAAC;QAC/B;MACF;MACA,IAAIqB,MAAM,CAACvB,IAAI,KAAK,aAAa,IAAIuB,MAAM,CAACxB,mBAAmB,EAAE;QAC/DwE,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACV,wBAAwB,CAAC;MACtD;MACA,IAAIU,MAAM,CAAC9B,SAAS,EAAE;QACpB8E,KAAK,CAACoD,gBAAgB,CAAC,OAAO,EAAExF,aAAa,CAAC;MAChD;MACA,IAAI,CAAClD,MAAM,CAACuE,OAAO,EAAE;QACnBe,KAAK,CAACzC,SAAS,CAACC,GAAG,CAACR,MAAM,CAACR,SAAS,CAAC;MACvC;IACF,CAAC,CAAC;EACJ;EACA,SAAS6G,OAAOA,CAAA,EAAG;IACjB,MAAMrG,MAAM,GAAGtC,MAAM,CAACsC,MAAM,CAACjC,UAAU;IACvC,IAAIgC,oBAAoB,CAAC,CAAC,EAAE;IAC5B,IAAI/B,EAAE,GAAGN,MAAM,CAACK,UAAU,CAACC,EAAE;IAC7B,IAAIA,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAI;QAClBA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACZ,WAAW,CAAC;QAC1C4D,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACf,aAAa,GAAGe,MAAM,CAACvB,IAAI,CAAC;QAC1DuE,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAACjG,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;QAC7F,IAAIM,MAAM,CAAC9B,SAAS,EAAE;UACpB8E,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC,GAAG,CAAC3D,MAAM,CAACT,cAAc,IAAI,EAAE,EAAEkE,KAAK,CAAC,GAAG,CAAC,CAAC;UACnET,KAAK,CAACsD,mBAAmB,CAAC,OAAO,EAAE1F,aAAa,CAAC;QACnD;MACF,CAAC,CAAC;IACJ;IACA,IAAIlD,MAAM,CAACK,UAAU,CAAC6B,OAAO,EAAElC,MAAM,CAACK,UAAU,CAAC6B,OAAO,CAACmD,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC,GAAG3D,MAAM,CAAChB,iBAAiB,CAACyE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3I;EACA7F,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B,IAAI,CAACF,MAAM,CAACK,UAAU,IAAI,CAACL,MAAM,CAACK,UAAU,CAACC,EAAE,EAAE;IACjD,MAAMgC,MAAM,GAAGtC,MAAM,CAACsC,MAAM,CAACjC,UAAU;IACvC,IAAI;MACFC;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrBC,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;IAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAI;MAClBA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAAC3D,MAAM,CAACP,eAAe,EAAEO,MAAM,CAACN,aAAa,CAAC;MACpEsD,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC9C,MAAM,CAACoF,YAAY,CAAC,CAAC,GAAG9C,MAAM,CAACP,eAAe,GAAGO,MAAM,CAACN,aAAa,CAAC;IAC5F,CAAC,CAAC;EACJ,CAAC,CAAC;EACF9B,EAAE,CAAC,MAAM,EAAE,MAAM;IACf,IAAIF,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACkE,OAAO,KAAK,KAAK,EAAE;MAC9C;MACAsE,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACLX,IAAI,CAAC,CAAC;MACNR,MAAM,CAAC,CAAC;MACRzD,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF/D,EAAE,CAAC,mBAAmB,EAAE,MAAM;IAC5B,IAAI,OAAOF,MAAM,CAAC8E,SAAS,KAAK,WAAW,EAAE;MAC3Cb,MAAM,CAAC,CAAC;IACV;EACF,CAAC,CAAC;EACF/D,EAAE,CAAC,iBAAiB,EAAE,MAAM;IAC1B+D,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACF/D,EAAE,CAAC,sBAAsB,EAAE,MAAM;IAC/BwH,MAAM,CAAC,CAAC;IACRzD,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACF/D,EAAE,CAAC,SAAS,EAAE,MAAM;IAClByI,OAAO,CAAC,CAAC;EACX,CAAC,CAAC;EACFzI,EAAE,CAAC,gBAAgB,EAAE,MAAM;IACzB,IAAI;MACFI;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAAC7C,MAAM,CAACuE,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAACvE,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACyB,SAAS,CAAC,CAAC;IAC7G;EACF,CAAC,CAAC;EACF5B,EAAE,CAAC,aAAa,EAAE,MAAM;IACtB+D,MAAM,CAAC,CAAC;EACV,CAAC,CAAC;EACF/D,EAAE,CAAC,OAAO,EAAE,CAAC4I,EAAE,EAAE3F,CAAC,KAAK;IACrB,MAAM4F,QAAQ,GAAG5F,CAAC,CAACC,MAAM;IACzB,MAAM9C,EAAE,GAAGjB,iBAAiB,CAACW,MAAM,CAACK,UAAU,CAACC,EAAE,CAAC;IAClD,IAAIN,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACC,EAAE,IAAIN,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACI,WAAW,IAAIH,EAAE,IAAIA,EAAE,CAACmC,MAAM,GAAG,CAAC,IAAI,CAACsG,QAAQ,CAAClG,SAAS,CAACmG,QAAQ,CAAChJ,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACgB,WAAW,CAAC,EAAE;MACpK,IAAIrB,MAAM,CAACiJ,UAAU,KAAKjJ,MAAM,CAACiJ,UAAU,CAACC,MAAM,IAAIH,QAAQ,KAAK/I,MAAM,CAACiJ,UAAU,CAACC,MAAM,IAAIlJ,MAAM,CAACiJ,UAAU,CAACE,MAAM,IAAIJ,QAAQ,KAAK/I,MAAM,CAACiJ,UAAU,CAACE,MAAM,CAAC,EAAE;MACnK,MAAMC,QAAQ,GAAG9I,EAAE,CAAC,CAAC,CAAC,CAACuC,SAAS,CAACmG,QAAQ,CAAChJ,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACqB,WAAW,CAAC;MAC/E,IAAI0H,QAAQ,KAAK,IAAI,EAAE;QACrBjJ,IAAI,CAAC,gBAAgB,CAAC;MACxB,CAAC,MAAM;QACLA,IAAI,CAAC,gBAAgB,CAAC;MACxB;MACAG,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACwG,MAAM,CAACrJ,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAACqB,WAAW,CAAC,CAAC;IACnF;EACF,CAAC,CAAC;EACF,MAAM4H,MAAM,GAAGA,CAAA,KAAM;IACnBtJ,MAAM,CAACM,EAAE,CAACuC,SAAS,CAACoD,MAAM,CAACjG,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAAC4B,uBAAuB,CAAC;IAC5E,IAAI;MACF3B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACoD,MAAM,CAACjG,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAAC4B,uBAAuB,CAAC,CAAC;IAC/F;IACAiG,IAAI,CAAC,CAAC;IACNR,MAAM,CAAC,CAAC;IACRzD,MAAM,CAAC,CAAC;EACV,CAAC;EACD,MAAM4E,OAAO,GAAGA,CAAA,KAAM;IACpB7I,MAAM,CAACM,EAAE,CAACuC,SAAS,CAACC,GAAG,CAAC9C,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAAC4B,uBAAuB,CAAC;IACzE,IAAI;MACF3B;IACF,CAAC,GAAGN,MAAM,CAACK,UAAU;IACrB,IAAIC,EAAE,EAAE;MACNA,EAAE,GAAGjB,iBAAiB,CAACiB,EAAE,CAAC;MAC1BA,EAAE,CAAC+E,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACzC,SAAS,CAACC,GAAG,CAAC9C,MAAM,CAACsC,MAAM,CAACjC,UAAU,CAAC4B,uBAAuB,CAAC,CAAC;IAC5F;IACA0G,OAAO,CAAC,CAAC;EACX,CAAC;EACDH,MAAM,CAACC,MAAM,CAACzI,MAAM,CAACK,UAAU,EAAE;IAC/BiJ,MAAM;IACNT,OAAO;IACPnB,MAAM;IACNzD,MAAM;IACNiE,IAAI;IACJS;EACF,CAAC,CAAC;AACJ;AAEA,SAAS7I,UAAU,IAAIyJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}