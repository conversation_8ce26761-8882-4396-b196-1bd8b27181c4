{"ast": null, "code": "import { f as now, l as elementTransitionEnd } from '../shared/utils.mjs';\nfunction freeMode(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    once\n  } = _ref;\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02\n    }\n  });\n  function onTouchStart() {\n    if (swiper.params.cssMode) return;\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({\n      currentPos: swiper.rtl ? swiper.translate : -swiper.translate\n    });\n  }\n  function onTouchMove() {\n    if (swiper.params.cssMode) return;\n    const {\n      touchEventsData: data,\n      touches\n    } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now()\n    });\n  }\n  function onTouchEnd(_ref2) {\n    let {\n      currentPos\n    } = _ref2;\n    if (swiper.params.cssMode) return;\n    const {\n      params,\n      wrapperEl,\n      rtlTranslate: rtl,\n      snapGrid,\n      touchEventsData: data\n    } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n        if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      emit('_freeModeStaticRelease');\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd\n    }\n  });\n}\nexport { freeMode as default };", "map": {"version": 3, "names": ["f", "now", "l", "elementTransitionEnd", "freeMode", "_ref", "swiper", "extendParams", "emit", "once", "enabled", "momentum", "momentumRatio", "momentumBounce", "momentumBounceRatio", "momentumVelocityRatio", "sticky", "minimumVelocity", "onTouchStart", "params", "cssMode", "translate", "getTranslate", "setTranslate", "setTransition", "touchEventsData", "velocities", "length", "onTouchEnd", "currentPos", "rtl", "onTouchMove", "data", "touches", "push", "position", "isHorizontal", "time", "touchStartTime", "_ref2", "wrapperEl", "rtlTranslate", "snapGrid", "touchEndTime", "timeDiff", "minTranslate", "slideTo", "activeIndex", "maxTranslate", "slides", "lastMoveEvent", "pop", "velocityEvent", "distance", "velocity", "Math", "abs", "momentumDuration", "momentumDistance", "newPosition", "doBounce", "afterBouncePosition", "bounceAmount", "needsLoopFix", "allowMomentumBounce", "loop", "centeredSlides", "nextSlide", "j", "swipeDirection", "loopFix", "moveDistance", "currentSlideSize", "slidesSizesGrid", "speed", "slideToClosest", "updateProgress", "transitionStart", "animating", "destroyed", "setTimeout", "transitionEnd", "updateActiveIndex", "updateSlidesClasses", "longSwipesMs", "Object", "assign", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/free-mode.mjs"], "sourcesContent": ["import { f as now, l as elementTransitionEnd } from '../shared/utils.mjs';\n\nfunction freeMode(_ref) {\n  let {\n    swiper,\n    extendParams,\n    emit,\n    once\n  } = _ref;\n  extendParams({\n    freeMode: {\n      enabled: false,\n      momentum: true,\n      momentumRatio: 1,\n      momentumBounce: true,\n      momentumBounceRatio: 1,\n      momentumVelocityRatio: 1,\n      sticky: false,\n      minimumVelocity: 0.02\n    }\n  });\n  function onTouchStart() {\n    if (swiper.params.cssMode) return;\n    const translate = swiper.getTranslate();\n    swiper.setTranslate(translate);\n    swiper.setTransition(0);\n    swiper.touchEventsData.velocities.length = 0;\n    swiper.freeMode.onTouchEnd({\n      currentPos: swiper.rtl ? swiper.translate : -swiper.translate\n    });\n  }\n  function onTouchMove() {\n    if (swiper.params.cssMode) return;\n    const {\n      touchEventsData: data,\n      touches\n    } = swiper;\n    // Velocity\n    if (data.velocities.length === 0) {\n      data.velocities.push({\n        position: touches[swiper.isHorizontal() ? 'startX' : 'startY'],\n        time: data.touchStartTime\n      });\n    }\n    data.velocities.push({\n      position: touches[swiper.isHorizontal() ? 'currentX' : 'currentY'],\n      time: now()\n    });\n  }\n  function onTouchEnd(_ref2) {\n    let {\n      currentPos\n    } = _ref2;\n    if (swiper.params.cssMode) return;\n    const {\n      params,\n      wrapperEl,\n      rtlTranslate: rtl,\n      snapGrid,\n      touchEventsData: data\n    } = swiper;\n    // Time diff\n    const touchEndTime = now();\n    const timeDiff = touchEndTime - data.touchStartTime;\n    if (currentPos < -swiper.minTranslate()) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (currentPos > -swiper.maxTranslate()) {\n      if (swiper.slides.length < snapGrid.length) {\n        swiper.slideTo(snapGrid.length - 1);\n      } else {\n        swiper.slideTo(swiper.slides.length - 1);\n      }\n      return;\n    }\n    if (params.freeMode.momentum) {\n      if (data.velocities.length > 1) {\n        const lastMoveEvent = data.velocities.pop();\n        const velocityEvent = data.velocities.pop();\n        const distance = lastMoveEvent.position - velocityEvent.position;\n        const time = lastMoveEvent.time - velocityEvent.time;\n        swiper.velocity = distance / time;\n        swiper.velocity /= 2;\n        if (Math.abs(swiper.velocity) < params.freeMode.minimumVelocity) {\n          swiper.velocity = 0;\n        }\n        // this implies that the user stopped moving a finger then released.\n        // There would be no events with distance zero, so the last event is stale.\n        if (time > 150 || now() - lastMoveEvent.time > 300) {\n          swiper.velocity = 0;\n        }\n      } else {\n        swiper.velocity = 0;\n      }\n      swiper.velocity *= params.freeMode.momentumVelocityRatio;\n      data.velocities.length = 0;\n      let momentumDuration = 1000 * params.freeMode.momentumRatio;\n      const momentumDistance = swiper.velocity * momentumDuration;\n      let newPosition = swiper.translate + momentumDistance;\n      if (rtl) newPosition = -newPosition;\n      let doBounce = false;\n      let afterBouncePosition;\n      const bounceAmount = Math.abs(swiper.velocity) * 20 * params.freeMode.momentumBounceRatio;\n      let needsLoopFix;\n      if (newPosition < swiper.maxTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition + swiper.maxTranslate() < -bounceAmount) {\n            newPosition = swiper.maxTranslate() - bounceAmount;\n          }\n          afterBouncePosition = swiper.maxTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.maxTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (newPosition > swiper.minTranslate()) {\n        if (params.freeMode.momentumBounce) {\n          if (newPosition - swiper.minTranslate() > bounceAmount) {\n            newPosition = swiper.minTranslate() + bounceAmount;\n          }\n          afterBouncePosition = swiper.minTranslate();\n          doBounce = true;\n          data.allowMomentumBounce = true;\n        } else {\n          newPosition = swiper.minTranslate();\n        }\n        if (params.loop && params.centeredSlides) needsLoopFix = true;\n      } else if (params.freeMode.sticky) {\n        let nextSlide;\n        for (let j = 0; j < snapGrid.length; j += 1) {\n          if (snapGrid[j] > -newPosition) {\n            nextSlide = j;\n            break;\n          }\n        }\n        if (Math.abs(snapGrid[nextSlide] - newPosition) < Math.abs(snapGrid[nextSlide - 1] - newPosition) || swiper.swipeDirection === 'next') {\n          newPosition = snapGrid[nextSlide];\n        } else {\n          newPosition = snapGrid[nextSlide - 1];\n        }\n        newPosition = -newPosition;\n      }\n      if (needsLoopFix) {\n        once('transitionEnd', () => {\n          swiper.loopFix();\n        });\n      }\n      // Fix duration\n      if (swiper.velocity !== 0) {\n        if (rtl) {\n          momentumDuration = Math.abs((-newPosition - swiper.translate) / swiper.velocity);\n        } else {\n          momentumDuration = Math.abs((newPosition - swiper.translate) / swiper.velocity);\n        }\n        if (params.freeMode.sticky) {\n          // If freeMode.sticky is active and the user ends a swipe with a slow-velocity\n          // event, then durations can be 20+ seconds to slide one (or zero!) slides.\n          // It's easy to see this when simulating touch with mouse events. To fix this,\n          // limit single-slide swipes to the default slide duration. This also has the\n          // nice side effect of matching slide speed if the user stopped moving before\n          // lifting finger or mouse vs. moving slowly before lifting the finger/mouse.\n          // For faster swipes, also apply limits (albeit higher ones).\n          const moveDistance = Math.abs((rtl ? -newPosition : newPosition) - swiper.translate);\n          const currentSlideSize = swiper.slidesSizesGrid[swiper.activeIndex];\n          if (moveDistance < currentSlideSize) {\n            momentumDuration = params.speed;\n          } else if (moveDistance < 2 * currentSlideSize) {\n            momentumDuration = params.speed * 1.5;\n          } else {\n            momentumDuration = params.speed * 2.5;\n          }\n        }\n      } else if (params.freeMode.sticky) {\n        swiper.slideToClosest();\n        return;\n      }\n      if (params.freeMode.momentumBounce && doBounce) {\n        swiper.updateProgress(afterBouncePosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        swiper.animating = true;\n        elementTransitionEnd(wrapperEl, () => {\n          if (!swiper || swiper.destroyed || !data.allowMomentumBounce) return;\n          emit('momentumBounce');\n          swiper.setTransition(params.speed);\n          setTimeout(() => {\n            swiper.setTranslate(afterBouncePosition);\n            elementTransitionEnd(wrapperEl, () => {\n              if (!swiper || swiper.destroyed) return;\n              swiper.transitionEnd();\n            });\n          }, 0);\n        });\n      } else if (swiper.velocity) {\n        emit('_freeModeNoMomentumRelease');\n        swiper.updateProgress(newPosition);\n        swiper.setTransition(momentumDuration);\n        swiper.setTranslate(newPosition);\n        swiper.transitionStart(true, swiper.swipeDirection);\n        if (!swiper.animating) {\n          swiper.animating = true;\n          elementTransitionEnd(wrapperEl, () => {\n            if (!swiper || swiper.destroyed) return;\n            swiper.transitionEnd();\n          });\n        }\n      } else {\n        swiper.updateProgress(newPosition);\n      }\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    } else if (params.freeMode.sticky) {\n      swiper.slideToClosest();\n      return;\n    } else if (params.freeMode) {\n      emit('_freeModeNoMomentumRelease');\n    }\n    if (!params.freeMode.momentum || timeDiff >= params.longSwipesMs) {\n      emit('_freeModeStaticRelease');\n      swiper.updateProgress();\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n  }\n  Object.assign(swiper, {\n    freeMode: {\n      onTouchStart,\n      onTouchMove,\n      onTouchEnd\n    }\n  });\n}\n\nexport { freeMode as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,oBAAoB,QAAQ,qBAAqB;AAEzE,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC,IAAI;IACJC;EACF,CAAC,GAAGJ,IAAI;EACRE,YAAY,CAAC;IACXH,QAAQ,EAAE;MACRM,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE,CAAC;MACxBC,MAAM,EAAE,KAAK;MACbC,eAAe,EAAE;IACnB;EACF,CAAC,CAAC;EACF,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAIZ,MAAM,CAACa,MAAM,CAACC,OAAO,EAAE;IAC3B,MAAMC,SAAS,GAAGf,MAAM,CAACgB,YAAY,CAAC,CAAC;IACvChB,MAAM,CAACiB,YAAY,CAACF,SAAS,CAAC;IAC9Bf,MAAM,CAACkB,aAAa,CAAC,CAAC,CAAC;IACvBlB,MAAM,CAACmB,eAAe,CAACC,UAAU,CAACC,MAAM,GAAG,CAAC;IAC5CrB,MAAM,CAACF,QAAQ,CAACwB,UAAU,CAAC;MACzBC,UAAU,EAAEvB,MAAM,CAACwB,GAAG,GAAGxB,MAAM,CAACe,SAAS,GAAG,CAACf,MAAM,CAACe;IACtD,CAAC,CAAC;EACJ;EACA,SAASU,WAAWA,CAAA,EAAG;IACrB,IAAIzB,MAAM,CAACa,MAAM,CAACC,OAAO,EAAE;IAC3B,MAAM;MACJK,eAAe,EAAEO,IAAI;MACrBC;IACF,CAAC,GAAG3B,MAAM;IACV;IACA,IAAI0B,IAAI,CAACN,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MAChCK,IAAI,CAACN,UAAU,CAACQ,IAAI,CAAC;QACnBC,QAAQ,EAAEF,OAAO,CAAC3B,MAAM,CAAC8B,YAAY,CAAC,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC9DC,IAAI,EAAEL,IAAI,CAACM;MACb,CAAC,CAAC;IACJ;IACAN,IAAI,CAACN,UAAU,CAACQ,IAAI,CAAC;MACnBC,QAAQ,EAAEF,OAAO,CAAC3B,MAAM,CAAC8B,YAAY,CAAC,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAClEC,IAAI,EAAEpC,GAAG,CAAC;IACZ,CAAC,CAAC;EACJ;EACA,SAAS2B,UAAUA,CAACW,KAAK,EAAE;IACzB,IAAI;MACFV;IACF,CAAC,GAAGU,KAAK;IACT,IAAIjC,MAAM,CAACa,MAAM,CAACC,OAAO,EAAE;IAC3B,MAAM;MACJD,MAAM;MACNqB,SAAS;MACTC,YAAY,EAAEX,GAAG;MACjBY,QAAQ;MACRjB,eAAe,EAAEO;IACnB,CAAC,GAAG1B,MAAM;IACV;IACA,MAAMqC,YAAY,GAAG1C,GAAG,CAAC,CAAC;IAC1B,MAAM2C,QAAQ,GAAGD,YAAY,GAAGX,IAAI,CAACM,cAAc;IACnD,IAAIT,UAAU,GAAG,CAACvB,MAAM,CAACuC,YAAY,CAAC,CAAC,EAAE;MACvCvC,MAAM,CAACwC,OAAO,CAACxC,MAAM,CAACyC,WAAW,CAAC;MAClC;IACF;IACA,IAAIlB,UAAU,GAAG,CAACvB,MAAM,CAAC0C,YAAY,CAAC,CAAC,EAAE;MACvC,IAAI1C,MAAM,CAAC2C,MAAM,CAACtB,MAAM,GAAGe,QAAQ,CAACf,MAAM,EAAE;QAC1CrB,MAAM,CAACwC,OAAO,CAACJ,QAAQ,CAACf,MAAM,GAAG,CAAC,CAAC;MACrC,CAAC,MAAM;QACLrB,MAAM,CAACwC,OAAO,CAACxC,MAAM,CAAC2C,MAAM,CAACtB,MAAM,GAAG,CAAC,CAAC;MAC1C;MACA;IACF;IACA,IAAIR,MAAM,CAACf,QAAQ,CAACO,QAAQ,EAAE;MAC5B,IAAIqB,IAAI,CAACN,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC9B,MAAMuB,aAAa,GAAGlB,IAAI,CAACN,UAAU,CAACyB,GAAG,CAAC,CAAC;QAC3C,MAAMC,aAAa,GAAGpB,IAAI,CAACN,UAAU,CAACyB,GAAG,CAAC,CAAC;QAC3C,MAAME,QAAQ,GAAGH,aAAa,CAACf,QAAQ,GAAGiB,aAAa,CAACjB,QAAQ;QAChE,MAAME,IAAI,GAAGa,aAAa,CAACb,IAAI,GAAGe,aAAa,CAACf,IAAI;QACpD/B,MAAM,CAACgD,QAAQ,GAAGD,QAAQ,GAAGhB,IAAI;QACjC/B,MAAM,CAACgD,QAAQ,IAAI,CAAC;QACpB,IAAIC,IAAI,CAACC,GAAG,CAAClD,MAAM,CAACgD,QAAQ,CAAC,GAAGnC,MAAM,CAACf,QAAQ,CAACa,eAAe,EAAE;UAC/DX,MAAM,CAACgD,QAAQ,GAAG,CAAC;QACrB;QACA;QACA;QACA,IAAIjB,IAAI,GAAG,GAAG,IAAIpC,GAAG,CAAC,CAAC,GAAGiD,aAAa,CAACb,IAAI,GAAG,GAAG,EAAE;UAClD/B,MAAM,CAACgD,QAAQ,GAAG,CAAC;QACrB;MACF,CAAC,MAAM;QACLhD,MAAM,CAACgD,QAAQ,GAAG,CAAC;MACrB;MACAhD,MAAM,CAACgD,QAAQ,IAAInC,MAAM,CAACf,QAAQ,CAACW,qBAAqB;MACxDiB,IAAI,CAACN,UAAU,CAACC,MAAM,GAAG,CAAC;MAC1B,IAAI8B,gBAAgB,GAAG,IAAI,GAAGtC,MAAM,CAACf,QAAQ,CAACQ,aAAa;MAC3D,MAAM8C,gBAAgB,GAAGpD,MAAM,CAACgD,QAAQ,GAAGG,gBAAgB;MAC3D,IAAIE,WAAW,GAAGrD,MAAM,CAACe,SAAS,GAAGqC,gBAAgB;MACrD,IAAI5B,GAAG,EAAE6B,WAAW,GAAG,CAACA,WAAW;MACnC,IAAIC,QAAQ,GAAG,KAAK;MACpB,IAAIC,mBAAmB;MACvB,MAAMC,YAAY,GAAGP,IAAI,CAACC,GAAG,CAAClD,MAAM,CAACgD,QAAQ,CAAC,GAAG,EAAE,GAAGnC,MAAM,CAACf,QAAQ,CAACU,mBAAmB;MACzF,IAAIiD,YAAY;MAChB,IAAIJ,WAAW,GAAGrD,MAAM,CAAC0C,YAAY,CAAC,CAAC,EAAE;QACvC,IAAI7B,MAAM,CAACf,QAAQ,CAACS,cAAc,EAAE;UAClC,IAAI8C,WAAW,GAAGrD,MAAM,CAAC0C,YAAY,CAAC,CAAC,GAAG,CAACc,YAAY,EAAE;YACvDH,WAAW,GAAGrD,MAAM,CAAC0C,YAAY,CAAC,CAAC,GAAGc,YAAY;UACpD;UACAD,mBAAmB,GAAGvD,MAAM,CAAC0C,YAAY,CAAC,CAAC;UAC3CY,QAAQ,GAAG,IAAI;UACf5B,IAAI,CAACgC,mBAAmB,GAAG,IAAI;QACjC,CAAC,MAAM;UACLL,WAAW,GAAGrD,MAAM,CAAC0C,YAAY,CAAC,CAAC;QACrC;QACA,IAAI7B,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC+C,cAAc,EAAEH,YAAY,GAAG,IAAI;MAC/D,CAAC,MAAM,IAAIJ,WAAW,GAAGrD,MAAM,CAACuC,YAAY,CAAC,CAAC,EAAE;QAC9C,IAAI1B,MAAM,CAACf,QAAQ,CAACS,cAAc,EAAE;UAClC,IAAI8C,WAAW,GAAGrD,MAAM,CAACuC,YAAY,CAAC,CAAC,GAAGiB,YAAY,EAAE;YACtDH,WAAW,GAAGrD,MAAM,CAACuC,YAAY,CAAC,CAAC,GAAGiB,YAAY;UACpD;UACAD,mBAAmB,GAAGvD,MAAM,CAACuC,YAAY,CAAC,CAAC;UAC3Ce,QAAQ,GAAG,IAAI;UACf5B,IAAI,CAACgC,mBAAmB,GAAG,IAAI;QACjC,CAAC,MAAM;UACLL,WAAW,GAAGrD,MAAM,CAACuC,YAAY,CAAC,CAAC;QACrC;QACA,IAAI1B,MAAM,CAAC8C,IAAI,IAAI9C,MAAM,CAAC+C,cAAc,EAAEH,YAAY,GAAG,IAAI;MAC/D,CAAC,MAAM,IAAI5C,MAAM,CAACf,QAAQ,CAACY,MAAM,EAAE;QACjC,IAAImD,SAAS;QACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,QAAQ,CAACf,MAAM,EAAEyC,CAAC,IAAI,CAAC,EAAE;UAC3C,IAAI1B,QAAQ,CAAC0B,CAAC,CAAC,GAAG,CAACT,WAAW,EAAE;YAC9BQ,SAAS,GAAGC,CAAC;YACb;UACF;QACF;QACA,IAAIb,IAAI,CAACC,GAAG,CAACd,QAAQ,CAACyB,SAAS,CAAC,GAAGR,WAAW,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAACd,QAAQ,CAACyB,SAAS,GAAG,CAAC,CAAC,GAAGR,WAAW,CAAC,IAAIrD,MAAM,CAAC+D,cAAc,KAAK,MAAM,EAAE;UACrIV,WAAW,GAAGjB,QAAQ,CAACyB,SAAS,CAAC;QACnC,CAAC,MAAM;UACLR,WAAW,GAAGjB,QAAQ,CAACyB,SAAS,GAAG,CAAC,CAAC;QACvC;QACAR,WAAW,GAAG,CAACA,WAAW;MAC5B;MACA,IAAII,YAAY,EAAE;QAChBtD,IAAI,CAAC,eAAe,EAAE,MAAM;UAC1BH,MAAM,CAACgE,OAAO,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ;MACA;MACA,IAAIhE,MAAM,CAACgD,QAAQ,KAAK,CAAC,EAAE;QACzB,IAAIxB,GAAG,EAAE;UACP2B,gBAAgB,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,CAACG,WAAW,GAAGrD,MAAM,CAACe,SAAS,IAAIf,MAAM,CAACgD,QAAQ,CAAC;QAClF,CAAC,MAAM;UACLG,gBAAgB,GAAGF,IAAI,CAACC,GAAG,CAAC,CAACG,WAAW,GAAGrD,MAAM,CAACe,SAAS,IAAIf,MAAM,CAACgD,QAAQ,CAAC;QACjF;QACA,IAAInC,MAAM,CAACf,QAAQ,CAACY,MAAM,EAAE;UAC1B;UACA;UACA;UACA;UACA;UACA;UACA;UACA,MAAMuD,YAAY,GAAGhB,IAAI,CAACC,GAAG,CAAC,CAAC1B,GAAG,GAAG,CAAC6B,WAAW,GAAGA,WAAW,IAAIrD,MAAM,CAACe,SAAS,CAAC;UACpF,MAAMmD,gBAAgB,GAAGlE,MAAM,CAACmE,eAAe,CAACnE,MAAM,CAACyC,WAAW,CAAC;UACnE,IAAIwB,YAAY,GAAGC,gBAAgB,EAAE;YACnCf,gBAAgB,GAAGtC,MAAM,CAACuD,KAAK;UACjC,CAAC,MAAM,IAAIH,YAAY,GAAG,CAAC,GAAGC,gBAAgB,EAAE;YAC9Cf,gBAAgB,GAAGtC,MAAM,CAACuD,KAAK,GAAG,GAAG;UACvC,CAAC,MAAM;YACLjB,gBAAgB,GAAGtC,MAAM,CAACuD,KAAK,GAAG,GAAG;UACvC;QACF;MACF,CAAC,MAAM,IAAIvD,MAAM,CAACf,QAAQ,CAACY,MAAM,EAAE;QACjCV,MAAM,CAACqE,cAAc,CAAC,CAAC;QACvB;MACF;MACA,IAAIxD,MAAM,CAACf,QAAQ,CAACS,cAAc,IAAI+C,QAAQ,EAAE;QAC9CtD,MAAM,CAACsE,cAAc,CAACf,mBAAmB,CAAC;QAC1CvD,MAAM,CAACkB,aAAa,CAACiC,gBAAgB,CAAC;QACtCnD,MAAM,CAACiB,YAAY,CAACoC,WAAW,CAAC;QAChCrD,MAAM,CAACuE,eAAe,CAAC,IAAI,EAAEvE,MAAM,CAAC+D,cAAc,CAAC;QACnD/D,MAAM,CAACwE,SAAS,GAAG,IAAI;QACvB3E,oBAAoB,CAACqC,SAAS,EAAE,MAAM;UACpC,IAAI,CAAClC,MAAM,IAAIA,MAAM,CAACyE,SAAS,IAAI,CAAC/C,IAAI,CAACgC,mBAAmB,EAAE;UAC9DxD,IAAI,CAAC,gBAAgB,CAAC;UACtBF,MAAM,CAACkB,aAAa,CAACL,MAAM,CAACuD,KAAK,CAAC;UAClCM,UAAU,CAAC,MAAM;YACf1E,MAAM,CAACiB,YAAY,CAACsC,mBAAmB,CAAC;YACxC1D,oBAAoB,CAACqC,SAAS,EAAE,MAAM;cACpC,IAAI,CAAClC,MAAM,IAAIA,MAAM,CAACyE,SAAS,EAAE;cACjCzE,MAAM,CAAC2E,aAAa,CAAC,CAAC;YACxB,CAAC,CAAC;UACJ,CAAC,EAAE,CAAC,CAAC;QACP,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3E,MAAM,CAACgD,QAAQ,EAAE;QAC1B9C,IAAI,CAAC,4BAA4B,CAAC;QAClCF,MAAM,CAACsE,cAAc,CAACjB,WAAW,CAAC;QAClCrD,MAAM,CAACkB,aAAa,CAACiC,gBAAgB,CAAC;QACtCnD,MAAM,CAACiB,YAAY,CAACoC,WAAW,CAAC;QAChCrD,MAAM,CAACuE,eAAe,CAAC,IAAI,EAAEvE,MAAM,CAAC+D,cAAc,CAAC;QACnD,IAAI,CAAC/D,MAAM,CAACwE,SAAS,EAAE;UACrBxE,MAAM,CAACwE,SAAS,GAAG,IAAI;UACvB3E,oBAAoB,CAACqC,SAAS,EAAE,MAAM;YACpC,IAAI,CAAClC,MAAM,IAAIA,MAAM,CAACyE,SAAS,EAAE;YACjCzE,MAAM,CAAC2E,aAAa,CAAC,CAAC;UACxB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL3E,MAAM,CAACsE,cAAc,CAACjB,WAAW,CAAC;MACpC;MACArD,MAAM,CAAC4E,iBAAiB,CAAC,CAAC;MAC1B5E,MAAM,CAAC6E,mBAAmB,CAAC,CAAC;IAC9B,CAAC,MAAM,IAAIhE,MAAM,CAACf,QAAQ,CAACY,MAAM,EAAE;MACjCV,MAAM,CAACqE,cAAc,CAAC,CAAC;MACvB;IACF,CAAC,MAAM,IAAIxD,MAAM,CAACf,QAAQ,EAAE;MAC1BI,IAAI,CAAC,4BAA4B,CAAC;IACpC;IACA,IAAI,CAACW,MAAM,CAACf,QAAQ,CAACO,QAAQ,IAAIiC,QAAQ,IAAIzB,MAAM,CAACiE,YAAY,EAAE;MAChE5E,IAAI,CAAC,wBAAwB,CAAC;MAC9BF,MAAM,CAACsE,cAAc,CAAC,CAAC;MACvBtE,MAAM,CAAC4E,iBAAiB,CAAC,CAAC;MAC1B5E,MAAM,CAAC6E,mBAAmB,CAAC,CAAC;IAC9B;EACF;EACAE,MAAM,CAACC,MAAM,CAAChF,MAAM,EAAE;IACpBF,QAAQ,EAAE;MACRc,YAAY;MACZa,WAAW;MACXH;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASxB,QAAQ,IAAImF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}