{"ast": null, "code": "import React,{useState}from'react';import{Swiper,SwiperSlide}from'swiper/react';import{Navigation,Pagination,Autoplay,EffectFade}from'swiper/modules';import FullscreenImageViewer from'./FullscreenImageViewer';// Import Swiper styles\nimport'swiper/css';import'swiper/css/navigation';import'swiper/css/pagination';import'swiper/css/effect-fade';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProjectImageSwiper=_ref=>{let{images,title,isNDA=false}=_ref;const[fullscreenImage,setFullscreenImage]=useState(null);const[isFullscreenOpen,setIsFullscreenOpen]=useState(false);const handleFullscreenClick=(e,imageUrl)=>{// Prevent event bubbling to parent elements\ne.stopPropagation();// Only open fullscreen for non-NDA projects\nif(!isNDA){setFullscreenImage(imageUrl);setIsFullscreenOpen(true);}};const closeFullscreen=()=>{setIsFullscreenOpen(false);setFullscreenImage(null);};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"project-image-swiper\",children:/*#__PURE__*/_jsxs(Swiper,{modules:[Navigation,Pagination,Autoplay,EffectFade],spaceBetween:0,slidesPerView:1,navigation:{nextEl:'.swiper-button-next-custom',prevEl:'.swiper-button-prev-custom'},pagination:{clickable:true,dynamicBullets:true},autoplay:{delay:4000,disableOnInteraction:false,pauseOnMouseEnter:true},effect:\"fade\",fadeEffect:{crossFade:true},loop:images.length>1,className:\"project-swiper\",children:[images.map((image,index)=>/*#__PURE__*/_jsx(SwiperSlide,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"swiper-slide-content\",children:[/*#__PURE__*/_jsx(\"img\",{src:image,alt:\"\".concat(title,\" - View \").concat(index+1),className:\"swiper-image\"}),!isNDA&&/*#__PURE__*/_jsx(\"div\",{className:\"fullscreen-icon-overlay\",onClick:e=>handleFullscreenClick(e,image),children:/*#__PURE__*/_jsxs(\"svg\",{className:\"fullscreen-icon\",width:\"20\",height:\"20\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M8 3H5C3.89543 3 3 3.89543 3 5V8\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M21 8V5C21 3.89543 20.1046 3 19 3H16\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M16 21H19C20.1046 21 21 20.1046 21 19V16\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M3 16V19C3 20.1046 3.89543 21 5 21H8\",stroke:\"currentColor\",strokeWidth:\"2\",strokeLinecap:\"round\",strokeLinejoin:\"round\"})]})})]})},index)),images.length>1&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"swiper-button-prev-custom\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u2039\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"swiper-button-next-custom\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u203A\"})})]}),images.length>1&&/*#__PURE__*/_jsxs(\"div\",{className:\"swipe-indicator\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"swipe-text\",children:\"Swipe\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"swipe-animation\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"swipe-dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"swipe-dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"swipe-dot\"})]})]})]})}),!isNDA&&/*#__PURE__*/_jsx(FullscreenImageViewer,{isOpen:isFullscreenOpen,imageUrl:fullscreenImage,imageAlt:\"\".concat(title,\" - Fullscreen View\"),onClose:closeFullscreen})]});};export default ProjectImageSwiper;", "map": {"version": 3, "names": ["React", "useState", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Autoplay", "EffectFade", "FullscreenImageViewer", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProjectImageSwiper", "_ref", "images", "title", "isNDA", "fullscreenImage", "setFullscreenImage", "isFullscreenOpen", "setIsFullscreenOpen", "handleFullscreenClick", "e", "imageUrl", "stopPropagation", "closeFullscreen", "children", "className", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "nextEl", "prevEl", "pagination", "clickable", "dynamicBullets", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "effect", "fadeEffect", "crossFade", "loop", "length", "map", "image", "index", "src", "alt", "concat", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "isOpen", "imageAlt", "onClose"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ProjectImageSwiper.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\nimport FullscreenImageViewer from './FullscreenImageViewer';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\nconst ProjectImageSwiper = ({ images, title, isNDA = false }) => {\n  const [fullscreenImage, setFullscreenImage] = useState(null);\n  const [isFullscreenOpen, setIsFullscreenOpen] = useState(false);\n\n  const handleFullscreenClick = (e, imageUrl) => {\n    // Prevent event bubbling to parent elements\n    e.stopPropagation();\n\n    // Only open fullscreen for non-NDA projects\n    if (!isNDA) {\n      setFullscreenImage(imageUrl);\n      setIsFullscreenOpen(true);\n    }\n  };\n\n  const closeFullscreen = () => {\n    setIsFullscreenOpen(false);\n    setFullscreenImage(null);\n  };\n\n  return (\n    <>\n      <div className=\"project-image-swiper\">\n      <Swiper\n        modules={[Navigation, Pagination, Autoplay, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        navigation={{\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom',\n        }}\n        pagination={{\n          clickable: true,\n          dynamicBullets: true,\n        }}\n        autoplay={{\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true,\n        }}\n        effect=\"fade\"\n        fadeEffect={{\n          crossFade: true\n        }}\n        loop={images.length > 1}\n        className=\"project-swiper\"\n      >\n        {images.map((image, index) => (\n          <SwiperSlide key={index}>\n            <div className=\"swiper-slide-content\">\n              <img\n                src={image}\n                alt={`${title} - View ${index + 1}`}\n                className=\"swiper-image\"\n              />\n              {/* Fullscreen Icon Overlay */}\n              {!isNDA && (\n                <div\n                  className=\"fullscreen-icon-overlay\"\n                  onClick={(e) => handleFullscreenClick(e, image)}\n                >\n                  <svg className=\"fullscreen-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M8 3H5C3.89543 3 3 3.89543 3 5V8\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M21 8V5C21 3.89543 20.1046 3 19 3H16\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M16 21H19C20.1046 21 21 20.1046 21 19V16\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M3 16V19C3 20.1046 3.89543 21 5 21H8\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </div>\n              )}\n            </div>\n          </SwiperSlide>\n        ))}\n        \n        {/* Custom Navigation Buttons */}\n        {images.length > 1 && (\n          <>\n            <div className=\"swiper-button-prev-custom\">\n              <span>‹</span>\n            </div>\n            <div className=\"swiper-button-next-custom\">\n              <span>›</span>\n            </div>\n          </>\n        )}\n        \n        {/* Swipe Indicator */}\n        {images.length > 1 && (\n          <div className=\"swipe-indicator\">\n            <span className=\"swipe-text\">Swipe</span>\n            <div className=\"swipe-animation\">\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n            </div>\n          </div>\n        )}\n\n\n      </Swiper>\n      </div>\n\n      {/* Fullscreen Image Viewer - Only for non-NDA projects */}\n      {!isNDA && (\n        <FullscreenImageViewer\n          isOpen={isFullscreenOpen}\n          imageUrl={fullscreenImage}\n          imageAlt={`${title} - Fullscreen View`}\n          onClose={closeFullscreen}\n        />\n      )}\n    </>\n  );\n};\n\nexport default ProjectImageSwiper;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,MAAM,CAAEC,WAAW,KAAQ,cAAc,CAClD,OAASC,UAAU,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,gBAAgB,CAC7E,MAAO,CAAAC,qBAAqB,KAAM,yBAAyB,CAE3D;AACA,MAAO,YAAY,CACnB,MAAO,uBAAuB,CAC9B,MAAO,uBAAuB,CAC9B,MAAO,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhC,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAAsC,IAArC,CAAEC,MAAM,CAAEC,KAAK,CAAEC,KAAK,CAAG,KAAM,CAAC,CAAAH,IAAA,CAC1D,KAAM,CAACI,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACqB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAE/D,KAAM,CAAAuB,qBAAqB,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CAC7C;AACAD,CAAC,CAACE,eAAe,CAAC,CAAC,CAEnB;AACA,GAAI,CAACR,KAAK,CAAE,CACVE,kBAAkB,CAACK,QAAQ,CAAC,CAC5BH,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED,KAAM,CAAAK,eAAe,CAAGA,CAAA,GAAM,CAC5BL,mBAAmB,CAAC,KAAK,CAAC,CAC1BF,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,mBACET,KAAA,CAAAE,SAAA,EAAAe,QAAA,eACEnB,IAAA,QAAKoB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACrCjB,KAAA,CAACV,MAAM,EACL6B,OAAO,CAAE,CAAC3B,UAAU,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,UAAU,CAAE,CACxDyB,YAAY,CAAE,CAAE,CAChBC,aAAa,CAAE,CAAE,CACjBC,UAAU,CAAE,CACVC,MAAM,CAAE,4BAA4B,CACpCC,MAAM,CAAE,4BACV,CAAE,CACFC,UAAU,CAAE,CACVC,SAAS,CAAE,IAAI,CACfC,cAAc,CAAE,IAClB,CAAE,CACFC,QAAQ,CAAE,CACRC,KAAK,CAAE,IAAI,CACXC,oBAAoB,CAAE,KAAK,CAC3BC,iBAAiB,CAAE,IACrB,CAAE,CACFC,MAAM,CAAC,MAAM,CACbC,UAAU,CAAE,CACVC,SAAS,CAAE,IACb,CAAE,CACFC,IAAI,CAAE9B,MAAM,CAAC+B,MAAM,CAAG,CAAE,CACxBlB,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAEzBZ,MAAM,CAACgC,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACvBzC,IAAA,CAACP,WAAW,EAAA0B,QAAA,cACVjB,KAAA,QAAKkB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCnB,IAAA,QACE0C,GAAG,CAAEF,KAAM,CACXG,GAAG,IAAAC,MAAA,CAAKpC,KAAK,aAAAoC,MAAA,CAAWH,KAAK,CAAG,CAAC,CAAG,CACpCrB,SAAS,CAAC,cAAc,CACzB,CAAC,CAED,CAACX,KAAK,eACLT,IAAA,QACEoB,SAAS,CAAC,yBAAyB,CACnCyB,OAAO,CAAG9B,CAAC,EAAKD,qBAAqB,CAACC,CAAC,CAAEyB,KAAK,CAAE,CAAArB,QAAA,cAEhDjB,KAAA,QAAKkB,SAAS,CAAC,iBAAiB,CAAC0B,KAAK,CAAC,IAAI,CAACC,MAAM,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,MAAM,CAACC,KAAK,CAAC,4BAA4B,CAAA/B,QAAA,eACxHnB,IAAA,SAAMmD,CAAC,CAAC,kCAAkC,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,cAC/HvD,IAAA,SAAMmD,CAAC,CAAC,sCAAsC,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,cACnIvD,IAAA,SAAMmD,CAAC,CAAC,0CAA0C,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,cACvIvD,IAAA,SAAMmD,CAAC,CAAC,sCAAsC,CAACC,MAAM,CAAC,cAAc,CAACC,WAAW,CAAC,GAAG,CAACC,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,EAChI,CAAC,CACH,CACN,EACE,CAAC,EArBUd,KAsBL,CACd,CAAC,CAGDlC,MAAM,CAAC+B,MAAM,CAAG,CAAC,eAChBpC,KAAA,CAAAE,SAAA,EAAAe,QAAA,eACEnB,IAAA,QAAKoB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCnB,IAAA,SAAAmB,QAAA,CAAM,QAAC,CAAM,CAAC,CACX,CAAC,cACNnB,IAAA,QAAKoB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCnB,IAAA,SAAAmB,QAAA,CAAM,QAAC,CAAM,CAAC,CACX,CAAC,EACN,CACH,CAGAZ,MAAM,CAAC+B,MAAM,CAAG,CAAC,eAChBpC,KAAA,QAAKkB,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnB,IAAA,SAAMoB,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,OAAK,CAAM,CAAC,cACzCjB,KAAA,QAAKkB,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BnB,IAAA,QAAKoB,SAAS,CAAC,WAAW,CAAM,CAAC,cACjCpB,IAAA,QAAKoB,SAAS,CAAC,WAAW,CAAM,CAAC,cACjCpB,IAAA,QAAKoB,SAAS,CAAC,WAAW,CAAM,CAAC,EAC9B,CAAC,EACH,CACN,EAGK,CAAC,CACJ,CAAC,CAGL,CAACX,KAAK,eACLT,IAAA,CAACF,qBAAqB,EACpB0D,MAAM,CAAE5C,gBAAiB,CACzBI,QAAQ,CAAEN,eAAgB,CAC1B+C,QAAQ,IAAAb,MAAA,CAAKpC,KAAK,sBAAqB,CACvCkD,OAAO,CAAExC,eAAgB,CAC1B,CACF,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAAb,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}