{"ast": null, "code": "import { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\nfunction EffectFade(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\nexport { EffectFade as default };", "map": {"version": 3, "names": ["e", "effectInit", "effect<PERSON>arget", "effectVirtualTransitionEnd", "g", "getSlideTransformEl", "EffectFade", "_ref", "swiper", "extendParams", "on", "fadeEffect", "crossFade", "setTranslate", "slides", "params", "i", "length", "slideEl", "offset", "swiperSlideOffset", "tx", "virtualTranslate", "translate", "ty", "isHorizontal", "slideOpacity", "Math", "max", "abs", "progress", "min", "targetEl", "style", "opacity", "transform", "setTransition", "duration", "transformElements", "map", "for<PERSON>ach", "el", "transitionDuration", "allSlides", "effect", "overwriteParams", "<PERSON><PERSON><PERSON><PERSON>iew", "slidesPerGroup", "watchSlidesProgress", "spaceBetween", "cssMode", "default"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/node_modules/swiper/modules/effect-fade.mjs"], "sourcesContent": ["import { e as effectInit } from '../shared/effect-init.mjs';\nimport { e as effectTarget } from '../shared/effect-target.mjs';\nimport { e as effectVirtualTransitionEnd } from '../shared/effect-virtual-transition-end.mjs';\nimport { g as getSlideTransformEl } from '../shared/utils.mjs';\n\nfunction EffectFade(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on\n  } = _ref;\n  extendParams({\n    fadeEffect: {\n      crossFade: false\n    }\n  });\n  const setTranslate = () => {\n    const {\n      slides\n    } = swiper;\n    const params = swiper.params.fadeEffect;\n    for (let i = 0; i < slides.length; i += 1) {\n      const slideEl = swiper.slides[i];\n      const offset = slideEl.swiperSlideOffset;\n      let tx = -offset;\n      if (!swiper.params.virtualTranslate) tx -= swiper.translate;\n      let ty = 0;\n      if (!swiper.isHorizontal()) {\n        ty = tx;\n        tx = 0;\n      }\n      const slideOpacity = swiper.params.fadeEffect.crossFade ? Math.max(1 - Math.abs(slideEl.progress), 0) : 1 + Math.min(Math.max(slideEl.progress, -1), 0);\n      const targetEl = effectTarget(params, slideEl);\n      targetEl.style.opacity = slideOpacity;\n      targetEl.style.transform = `translate3d(${tx}px, ${ty}px, 0px)`;\n    }\n  };\n  const setTransition = duration => {\n    const transformElements = swiper.slides.map(slideEl => getSlideTransformEl(slideEl));\n    transformElements.forEach(el => {\n      el.style.transitionDuration = `${duration}ms`;\n    });\n    effectVirtualTransitionEnd({\n      swiper,\n      duration,\n      transformElements,\n      allSlides: true\n    });\n  };\n  effectInit({\n    effect: 'fade',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams: () => ({\n      slidesPerView: 1,\n      slidesPerGroup: 1,\n      watchSlidesProgress: true,\n      spaceBetween: 0,\n      virtualTranslate: !swiper.params.cssMode\n    })\n  });\n}\n\nexport { EffectFade as default };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,UAAU,QAAQ,2BAA2B;AAC3D,SAASD,CAAC,IAAIE,YAAY,QAAQ,6BAA6B;AAC/D,SAASF,CAAC,IAAIG,0BAA0B,QAAQ,6CAA6C;AAC7F,SAASC,CAAC,IAAIC,mBAAmB,QAAQ,qBAAqB;AAE9D,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAI;IACFC,MAAM;IACNC,YAAY;IACZC;EACF,CAAC,GAAGH,IAAI;EACRE,YAAY,CAAC;IACXE,UAAU,EAAE;MACVC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAM;MACJC;IACF,CAAC,GAAGN,MAAM;IACV,MAAMO,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACJ,UAAU;IACvC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACzC,MAAME,OAAO,GAAGV,MAAM,CAACM,MAAM,CAACE,CAAC,CAAC;MAChC,MAAMG,MAAM,GAAGD,OAAO,CAACE,iBAAiB;MACxC,IAAIC,EAAE,GAAG,CAACF,MAAM;MAChB,IAAI,CAACX,MAAM,CAACO,MAAM,CAACO,gBAAgB,EAAED,EAAE,IAAIb,MAAM,CAACe,SAAS;MAC3D,IAAIC,EAAE,GAAG,CAAC;MACV,IAAI,CAAChB,MAAM,CAACiB,YAAY,CAAC,CAAC,EAAE;QAC1BD,EAAE,GAAGH,EAAE;QACPA,EAAE,GAAG,CAAC;MACR;MACA,MAAMK,YAAY,GAAGlB,MAAM,CAACO,MAAM,CAACJ,UAAU,CAACC,SAAS,GAAGe,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGD,IAAI,CAACE,GAAG,CAACX,OAAO,CAACY,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACC,GAAG,CAACV,OAAO,CAACY,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvJ,MAAME,QAAQ,GAAG9B,YAAY,CAACa,MAAM,EAAEG,OAAO,CAAC;MAC9Cc,QAAQ,CAACC,KAAK,CAACC,OAAO,GAAGR,YAAY;MACrCM,QAAQ,CAACC,KAAK,CAACE,SAAS,GAAG,eAAed,EAAE,OAAOG,EAAE,UAAU;IACjE;EACF,CAAC;EACD,MAAMY,aAAa,GAAGC,QAAQ,IAAI;IAChC,MAAMC,iBAAiB,GAAG9B,MAAM,CAACM,MAAM,CAACyB,GAAG,CAACrB,OAAO,IAAIb,mBAAmB,CAACa,OAAO,CAAC,CAAC;IACpFoB,iBAAiB,CAACE,OAAO,CAACC,EAAE,IAAI;MAC9BA,EAAE,CAACR,KAAK,CAACS,kBAAkB,GAAG,GAAGL,QAAQ,IAAI;IAC/C,CAAC,CAAC;IACFlC,0BAA0B,CAAC;MACzBK,MAAM;MACN6B,QAAQ;MACRC,iBAAiB;MACjBK,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EACD1C,UAAU,CAAC;IACT2C,MAAM,EAAE,MAAM;IACdpC,MAAM;IACNE,EAAE;IACFG,YAAY;IACZuB,aAAa;IACbS,eAAe,EAAEA,CAAA,MAAO;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,IAAI;MACzBC,YAAY,EAAE,CAAC;MACf3B,gBAAgB,EAAE,CAACd,MAAM,CAACO,MAAM,CAACmC;IACnC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAAS5C,UAAU,IAAI6C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}