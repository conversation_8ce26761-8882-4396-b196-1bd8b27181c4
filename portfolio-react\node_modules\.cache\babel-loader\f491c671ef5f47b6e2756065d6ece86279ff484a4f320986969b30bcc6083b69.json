{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfulio\\\\portfolio-react\\\\src\\\\components\\\\ProjectImageSwiper.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProjectImageSwiper = ({\n  images,\n  title,\n  isNDA = false\n}) => {\n  _s();\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  // Handle escape key to close fullscreen\n  useEffect(() => {\n    const handleEscapeKey = event => {\n      if (event.key === 'Escape' && isFullscreen) {\n        setIsFullscreen(false);\n      }\n    };\n    if (isFullscreen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.removeEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isFullscreen]);\n  const openFullscreen = imageIndex => {\n    setCurrentImageIndex(imageIndex);\n    setIsFullscreen(true);\n  };\n  const closeFullscreen = () => {\n    setIsFullscreen(false);\n  };\n  const handleBackdropClick = e => {\n    if (e.target === e.currentTarget) {\n      closeFullscreen();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"project-image-swiper\",\n      children: /*#__PURE__*/_jsxDEV(Swiper, {\n        modules: [Navigation, Pagination, Autoplay, EffectFade],\n        spaceBetween: 0,\n        slidesPerView: 1,\n        navigation: {\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom'\n        },\n        pagination: {\n          clickable: true,\n          dynamicBullets: true\n        },\n        autoplay: {\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true\n        },\n        effect: \"fade\",\n        fadeEffect: {\n          crossFade: true\n        },\n        loop: images.length > 1,\n        className: \"project-swiper\",\n        children: [images.map((image, index) => /*#__PURE__*/_jsxDEV(SwiperSlide, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-slide-content\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: `${title} - View ${index + 1}`,\n              className: \"swiper-image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)), images.length > 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-prev-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u2039\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swiper-button-next-custom\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u203A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"swipe-indicator\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"swipe-text\",\n            children: \"Swipe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"swipe-animation\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"swipe-dot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ProjectImageSwiper, \"F067iQc9tofxHtILw5+fSiwVBnM=\");\n_c = ProjectImageSwiper;\nexport default ProjectImageSwiper;\nvar _c;\n$RefreshReg$(_c, \"ProjectImageSwiper\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Swiper", "SwiperSlide", "Navigation", "Pagination", "Autoplay", "EffectFade", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProjectImageSwiper", "images", "title", "isNDA", "_s", "isFullscreen", "setIsFullscreen", "currentImageIndex", "setCurrentImageIndex", "handleEscapeKey", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "openFullscreen", "imageIndex", "closeFullscreen", "handleBackdropClick", "e", "target", "currentTarget", "children", "className", "modules", "spaceBetween", "<PERSON><PERSON><PERSON><PERSON>iew", "navigation", "nextEl", "prevEl", "pagination", "clickable", "dynamicBullets", "autoplay", "delay", "disableOnInteraction", "pauseOnMouseEnter", "effect", "fadeEffect", "crossFade", "loop", "length", "map", "image", "index", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/components/ProjectImageSwiper.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Swiper, SwiperSlide } from 'swiper/react';\nimport { Navigation, Pagination, Autoplay, EffectFade } from 'swiper/modules';\n\n// Import Swiper styles\nimport 'swiper/css';\nimport 'swiper/css/navigation';\nimport 'swiper/css/pagination';\nimport 'swiper/css/effect-fade';\n\nconst ProjectImageSwiper = ({ images, title, isNDA = false }) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  // Handle escape key to close fullscreen\n  useEffect(() => {\n    const handleEscapeKey = (event) => {\n      if (event.key === 'Escape' && isFullscreen) {\n        setIsFullscreen(false);\n      }\n    };\n\n    if (isFullscreen) {\n      document.addEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscapeKey);\n      document.body.style.overflow = 'unset';\n    };\n  }, [isFullscreen]);\n\n  const openFullscreen = (imageIndex) => {\n    setCurrentImageIndex(imageIndex);\n    setIsFullscreen(true);\n  };\n\n  const closeFullscreen = () => {\n    setIsFullscreen(false);\n  };\n\n  const handleBackdropClick = (e) => {\n    if (e.target === e.currentTarget) {\n      closeFullscreen();\n    }\n  };\n\n  return (\n    <>\n      <div className=\"project-image-swiper\">\n      <Swiper\n        modules={[Navigation, Pagination, Autoplay, EffectFade]}\n        spaceBetween={0}\n        slidesPerView={1}\n        navigation={{\n          nextEl: '.swiper-button-next-custom',\n          prevEl: '.swiper-button-prev-custom',\n        }}\n        pagination={{\n          clickable: true,\n          dynamicBullets: true,\n        }}\n        autoplay={{\n          delay: 4000,\n          disableOnInteraction: false,\n          pauseOnMouseEnter: true,\n        }}\n        effect=\"fade\"\n        fadeEffect={{\n          crossFade: true\n        }}\n        loop={images.length > 1}\n        className=\"project-swiper\"\n      >\n        {images.map((image, index) => (\n          <SwiperSlide key={index}>\n            <div className=\"swiper-slide-content\">\n              <img\n                src={image}\n                alt={`${title} - View ${index + 1}`}\n                className=\"swiper-image\"\n              />\n            </div>\n          </SwiperSlide>\n        ))}\n        \n        {/* Custom Navigation Buttons */}\n        {images.length > 1 && (\n          <>\n            <div className=\"swiper-button-prev-custom\">\n              <span>‹</span>\n            </div>\n            <div className=\"swiper-button-next-custom\">\n              <span>›</span>\n            </div>\n          </>\n        )}\n        \n        {/* Swipe Indicator */}\n        {images.length > 1 && (\n          <div className=\"swipe-indicator\">\n            <span className=\"swipe-text\">Swipe</span>\n            <div className=\"swipe-animation\">\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n              <div className=\"swipe-dot\"></div>\n            </div>\n          </div>\n        )}\n\n\n      </Swiper>\n      </div>\n\n\n    </>\n  );\n};\n\nexport default ProjectImageSwiper;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,gBAAgB;;AAE7E;AACA,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,KAAK;EAAEC,KAAK,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoB,eAAe,GAAIC,KAAK,IAAK;MACjC,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,IAAIN,YAAY,EAAE;QAC1CC,eAAe,CAAC,KAAK,CAAC;MACxB;IACF,CAAC;IAED,IAAID,YAAY,EAAE;MAChBO,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEJ,eAAe,CAAC;MACrDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLJ,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,EAAER,eAAe,CAAC;MACxDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;EAElB,MAAMa,cAAc,GAAIC,UAAU,IAAK;IACrCX,oBAAoB,CAACW,UAAU,CAAC;IAChCb,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMc,eAAe,GAAGA,CAAA,KAAM;IAC5Bd,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMe,mBAAmB,GAAIC,CAAC,IAAK;IACjC,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACE,aAAa,EAAE;MAChCJ,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,oBACEvB,OAAA,CAAAE,SAAA;IAAA0B,QAAA,eACE5B,OAAA;MAAK6B,SAAS,EAAC,sBAAsB;MAAAD,QAAA,eACrC5B,OAAA,CAACP,MAAM;QACLqC,OAAO,EAAE,CAACnC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,CAAE;QACxDiC,YAAY,EAAE,CAAE;QAChBC,aAAa,EAAE,CAAE;QACjBC,UAAU,EAAE;UACVC,MAAM,EAAE,4BAA4B;UACpCC,MAAM,EAAE;QACV,CAAE;QACFC,UAAU,EAAE;UACVC,SAAS,EAAE,IAAI;UACfC,cAAc,EAAE;QAClB,CAAE;QACFC,QAAQ,EAAE;UACRC,KAAK,EAAE,IAAI;UACXC,oBAAoB,EAAE,KAAK;UAC3BC,iBAAiB,EAAE;QACrB,CAAE;QACFC,MAAM,EAAC,MAAM;QACbC,UAAU,EAAE;UACVC,SAAS,EAAE;QACb,CAAE;QACFC,IAAI,EAAE1C,MAAM,CAAC2C,MAAM,GAAG,CAAE;QACxBlB,SAAS,EAAC,gBAAgB;QAAAD,QAAA,GAEzBxB,MAAM,CAAC4C,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBlD,OAAA,CAACN,WAAW;UAAAkC,QAAA,eACV5B,OAAA;YAAK6B,SAAS,EAAC,sBAAsB;YAAAD,QAAA,eACnC5B,OAAA;cACEmD,GAAG,EAAEF,KAAM;cACXG,GAAG,EAAE,GAAG/C,KAAK,WAAW6C,KAAK,GAAG,CAAC,EAAG;cACpCrB,SAAS,EAAC;YAAc;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC,GAPUN,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACd,CAAC,EAGDpD,MAAM,CAAC2C,MAAM,GAAG,CAAC,iBAChB/C,OAAA,CAAAE,SAAA;UAAA0B,QAAA,gBACE5B,OAAA;YAAK6B,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC5B,OAAA;cAAA4B,QAAA,EAAM;YAAC;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACNxD,OAAA;YAAK6B,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC5B,OAAA;cAAA4B,QAAA,EAAM;YAAC;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA,eACN,CACH,EAGApD,MAAM,CAAC2C,MAAM,GAAG,CAAC,iBAChB/C,OAAA;UAAK6B,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9B5B,OAAA;YAAM6B,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzCxD,OAAA;YAAK6B,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9B5B,OAAA;cAAK6B,SAAS,EAAC;YAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjCxD,OAAA;cAAK6B,SAAS,EAAC;YAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjCxD,OAAA;cAAK6B,SAAS,EAAC;YAAW;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC,gBAGN,CAAC;AAEP,CAAC;AAACjD,EAAA,CA9GIJ,kBAAkB;AAAAsD,EAAA,GAAlBtD,kBAAkB;AAgHxB,eAAeA,kBAAkB;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}