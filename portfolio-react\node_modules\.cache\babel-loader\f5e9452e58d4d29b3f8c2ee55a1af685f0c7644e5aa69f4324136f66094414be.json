{"ast": null, "code": "export const jobsData = [{\n  id: 1,\n  slug: \"receeto-frontend-developer\",\n  title: \"Frontend Developer\",\n  company: \"Receeto\",\n  companyLink: \"https://receeto.com\",\n  logo: \"/Receeto_logo.jpg\",\n  logoAlt: \"Receeto Logo\",\n  duration: \"2023 - Present\",\n  summary: \"Developing modern web applications with Angular and React, focusing on user experience and performance optimization.\",\n  description: \"As a Frontend Developer at Receeto, I work on building scalable web applications using modern JavaScript frameworks. My responsibilities include developing responsive user interfaces, implementing complex data visualizations, and optimizing application performance.\",\n  responsibilities: [\"Develop and maintain web applications using Angular and React\", \"Implement responsive designs for mobile and desktop platforms\", \"Collaborate with backend developers to integrate APIs\", \"Optimize application performance and user experience\", \"Write clean, maintainable, and well-documented code\"],\n  technologies: [\"Angular\", \"React\", \"TypeScript\", \"JavaScript\", \"HTML5\", \"CSS3\", \"SCSS\", \"RxJS\", \"Charts.js\", \"Angular Material\"],\n  accomplishments: [{\n    metric: \"40%\",\n    description: \"Improved application performance through lazy loading and build optimization\"\n  }, {\n    metric: \"100%\",\n    description: \"Responsive design compatibility across mobile and desktop platforms\"\n  }, {\n    metric: \"NDA\",\n    description: \"Confidential project delivered successfully while maintaining client privacy\"\n  }],\n  projects: [{\n    title: \"Project 1\",\n    description: \"NDA - details confidential\",\n    images: [\"/NDA.jpg\", \"/NDA.jpg\", \"/NDA.jpg\"],\n    technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"],\n    liveUrl: \"https://receeto.com\",\n    isNDA: true\n  }, {\n    title: \"Project 2\",\n    description: \"NDA - details confidential\",\n    images: [\"/NDA.jpg\", \"/NDA.jpg\"],\n    technologies: [\"Angular\", \"RxJS\", \"Angular Material\"],\n    liveUrl: \"https://receeto.com\",\n    isNDA: true\n  }]\n}];", "map": {"version": 3, "names": ["jobsData", "id", "slug", "title", "company", "companyLink", "logo", "logoAlt", "duration", "summary", "description", "responsibilities", "technologies", "accomplishments", "metric", "projects", "images", "liveUrl", "isNDA"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/data/jobsData.js"], "sourcesContent": ["export const jobsData = [\r\n  {\r\n    id: 1,\r\n    slug: \"receeto-frontend-developer\",\r\n    title: \"Frontend Developer\",\r\n    company: \"Receeto\",\r\n    companyLink: \"https://receeto.com\",\r\n    logo: \"/Receeto_logo.jpg\",\r\n    logoAlt: \"Receeto Logo\",\r\n    duration: \"2023 - Present\",\r\n    summary: \"Developing modern web applications with Angular and React, focusing on user experience and performance optimization.\",\r\n    description: \"As a Frontend Developer at Receeto, I work on building scalable web applications using modern JavaScript frameworks. My responsibilities include developing responsive user interfaces, implementing complex data visualizations, and optimizing application performance.\",\r\n    responsibilities: [\r\n      \"Develop and maintain web applications using Angular and React\",\r\n      \"Implement responsive designs for mobile and desktop platforms\",\r\n      \"Collaborate with backend developers to integrate APIs\",\r\n      \"Optimize application performance and user experience\",\r\n      \"Write clean, maintainable, and well-documented code\"\r\n    ],\r\n    technologies: [\"Angular\", \"React\", \"TypeScript\", \"JavaScript\", \"HTML5\", \"CSS3\", \"SCSS\", \"RxJS\", \"Charts.js\", \"Angular Material\"],\r\n    accomplishments: [\r\n      {\r\n        metric: \"40%\",\r\n        description: \"Improved application performance through lazy loading and build optimization\"\r\n      },\r\n      {\r\n        metric: \"100%\",\r\n        description: \"Responsive design compatibility across mobile and desktop platforms\"\r\n      },\r\n      {\r\n        metric: \"NDA\",\r\n        description: \"Confidential project delivered successfully while maintaining client privacy\"\r\n      }\r\n    ],\r\n    projects: [\r\n      {\r\n        title: \"Project 1\",\r\n        description: \"NDA - details confidential\",\r\n        images: [\"/NDA.jpg\", \"/NDA.jpg\", \"/NDA.jpg\"],\r\n        technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"],\r\n        liveUrl: \"https://receeto.com\",\r\n        isNDA: true\r\n      },\r\n      {\r\n        title: \"Project 2\",\r\n        description: \"NDA - details confidential\",\r\n        images: [\"/NDA.jpg\", \"/NDA.jpg\"],\r\n        technologies: [\"Angular\", \"RxJS\", \"Angular Material\"],\r\n        liveUrl: \"https://receeto.com\",\r\n        isNDA: true\r\n      }\r\n    ]\r\n  }\r\n];"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAG,CACtB;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,4BAA4B;EAClCC,KAAK,EAAE,oBAAoB;EAC3BC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,qBAAqB;EAClCC,IAAI,EAAE,mBAAmB;EACzBC,OAAO,EAAE,cAAc;EACvBC,QAAQ,EAAE,gBAAgB;EAC1BC,OAAO,EAAE,sHAAsH;EAC/HC,WAAW,EAAE,2QAA2Q;EACxRC,gBAAgB,EAAE,CAChB,+DAA+D,EAC/D,+DAA+D,EAC/D,uDAAuD,EACvD,sDAAsD,EACtD,qDAAqD,CACtD;EACDC,YAAY,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,kBAAkB,CAAC;EAChIC,eAAe,EAAE,CACf;IACEC,MAAM,EAAE,KAAK;IACbJ,WAAW,EAAE;EACf,CAAC,EACD;IACEI,MAAM,EAAE,MAAM;IACdJ,WAAW,EAAE;EACf,CAAC,EACD;IACEI,MAAM,EAAE,KAAK;IACbJ,WAAW,EAAE;EACf,CAAC,CACF;EACDK,QAAQ,EAAE,CACR;IACEZ,KAAK,EAAE,WAAW;IAClBO,WAAW,EAAE,4BAA4B;IACzCM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;IAC5CJ,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,CAAC;IACpDK,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEf,KAAK,EAAE,WAAW;IAClBO,WAAW,EAAE,4BAA4B;IACzCM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;IAChCJ,YAAY,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,kBAAkB,CAAC;IACrDK,OAAO,EAAE,qBAAqB;IAC9BC,KAAK,EAAE;EACT,CAAC;AAEL,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}