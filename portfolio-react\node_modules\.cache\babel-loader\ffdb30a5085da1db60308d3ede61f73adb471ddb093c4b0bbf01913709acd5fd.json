{"ast": null, "code": "export const jobsData=[{id:1,slug:\"frontend-receeto\",title:\"Frontend Developer Angular\",company:\"Company : Receeto\",companyLink:\"https://receeto.com\",duration:\"2/2025 - 6/2025\",logo:\"/Receeto_logo.jpg\",logoAlt:\"Receeto Logo\",summary:\"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",roleOverview:\"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",responsibilities:[\"Develop responsive web applications using Angular and modern frontend technologies\",\"Implement financial data visualizations and interactive charts\",\"Optimize application performance through lazy loading and build optimization\",\"Create expense tracking and budgeting tools with real-time data processing\",\"Build responsive interfaces for both mobile and desktop platforms\",\"Implement Angular reactive state management using signals\"],skills:{\"Frontend\":[\"Angular\",\"TypeScript\",\"RxJS\",\"Angular Signals\",\"Angular CLI\"],\"Styling\":[\"CSS3\",\"SASS/SCSS\",\"Angular Material\",\"Responsive Design\",\"Bootstrap\"],\"Tools & Testing\":[\"Git\",\"Angular CLI\",\"Webpack\",\"Lighthouse (for performance auditing)\",\"Figma\"]},accomplishments:[{metric:\"40%\",description:\"Improved application performance through lazy loading and build optimization\"},{metric:\"100%\",description:\"Responsive design compatibility across mobile and desktop platforms\"},{metric:\"NDA\",description:\"Confidential project delivered successfully while maintaining client privacy\"}],projects:[{title:\"Img 1\",description:\"NDA - details confidential\",images:[\"../NDA.jpg\",\"../NDA.jpg\",\"../NDA.jpg\"],technologies:[\"Angular\",\"TypeScript\",\"Charts.js\"],liveUrl:\"https://receeto.com\"},{title:\"Img 2\",description:\"NDA - details confidential\",images:[\"../NDA.jpg\",\"../NDA.jpg\"],technologies:[\"Angular\",\"RxJS\",\"Angular Material\"],liveUrl:\"https://receeto.com\"}]},{id:2,slug:\"3d-ecommerce-platform\",title:\"3D-ecommerce platform UI/UX Designer\",company:\"DigitalStudio Creative\",companyLink:\"https://threed-e-commerce.onrender.com\",duration:\"2022 - 2023\",logo:\"/3D E Logo.png\",logoAlt:\"DigitalStudio Creative Logo\",summary:\"Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.\",roleOverview:\"As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\\n\\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\\n\\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.\",responsibilities:[\"Design user interfaces and experiences for the 3D e-commerce platform\",\"Develop responsive frontend using React.js and modern web technologies\",\"Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions\",\"Create comprehensive design systems for consistent user experience across devices\",\"Conduct usability testing to refine the shopping experience and 3D interactions\",\"Optimize application performance across various devices and network conditions\",\"Collaborate with clients to understand business requirements and technical constraints\",\"Develop fallback mechanisms and error boundaries for graceful failure handling\",\"Implement progressive enhancement and mobile-first responsive design principles\"],skills:{\"Frontend\":[\"React.js\",\"Three.js/WebGL\",\"JavaScript\",\"CSS3\",\"React Router\",\"Axios\"],\"3D Technologies\":[\"Model Viewer\",\"GLTF/GLB\",\"OrbitControls\",\"Real-time Rendering\"],\"Backend\":[\"Node.js\",\"Express.js\",\"MongoDB\",\"RESTful APIs\"],\"Design & UX\":[\"Figma\",\"Responsive Design\",\"Mobile-First Design\",\"User Testing\",\"Performance Optimization\"],\"Tools & Deployment\":[\"Git\",\"Render\",\"Webpack\",\"Font Awesome\",\"Chrome DevTools\"]},accomplishments:[{metric:\"40%\",description:\"Increased user engagement through improved UX design and 3D interactions\"},{metric:\"95%\",description:\"Client satisfaction rate based on project feedback\"}],projects:[{title:\"3D Product Visualization Engine\",description:\"Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.\",images:[\"../3D E Commerce Home.PNG\",\"../home mobil mode.PNG\"],technologies:[\"Three.js\",\"WebGL\",\"GLTF/GLB\",\"OrbitControls\"],liveUrl:\"https://threed-e-commerce.onrender.com\"},{title:\"Mobile-Responsive Shopping Interface\",description:\"Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.\",images:[\"../All product pc.PNG\",\"../all products mobil mode.PNG\"],technologies:[\"React.js\",\"CSS3\",\"Responsive Design\",\"Mobile UX\"],liveUrl:\"https://threed-e-commerce.onrender.com\"},{title:\"Performance-Optimized Architecture\",description:\"Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.\",images:[\"../air pods pc mode.PNG\",\"../air pods mobil mode.PNG\"],technologies:[\"Node.js\",\"Express.js\",\"MongoDB\",\"Render\",\"API Design\"],liveUrl:\"https://threed-e-commerce-backend.onrender.com\"}]}/*\n  /* Job ID 3 and 4 are currently hidden\n  {\n    id: 3,\n    slug: \"junior-web-developer\",\n    title: \"Junior Web Developer\",\n    company: \"WebDev Agency\",\n    duration: \"2021 - 2022\",\n    logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n    logoAlt: \"WebDev Agency Logo\",\n    summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n    roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n    responsibilities: [\n      \"Develop custom WordPress themes and plugins\",\n      \"Build responsive websites using HTML, CSS, and JavaScript\",\n      \"Create e-commerce solutions using WooCommerce and Shopify\",\n      \"Collaborate with designers to implement pixel-perfect designs\",\n      \"Optimize websites for performance and SEO\",\n      \"Provide technical support and maintenance for client websites\"\n    ],\n    skills: {\n      \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n      \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n      \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n    },\n    accomplishments: [\n      {\n        metric: \"30+\",\n        description: \"Websites successfully developed and launched\"\n      },\n      {\n        metric: \"50%\",\n        description: \"Improvement in page load speeds through optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Client satisfaction rate for delivered projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Restaurant Chain Website\",\n        description: \"Built a multi-location restaurant website with online ordering system\",\n        images: [\n          \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n          \"https://via.placeholder.com/400x250/008B8B/FFFFFF?text=Menu+System\",\n          \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=Order+Management\"\n        ],\n        technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"],\n        liveUrl: \"https://example-restaurant.com\"\n      },\n      {\n        title: \"Real Estate Portal\",\n        description: \"Developed property listing website with advanced search functionality\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Property+Search\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Listing+Details\"\n        ],\n        technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"],\n        liveUrl: \"https://example-realestate.com\"\n      }\n    ]\n  },\n  {\n    id: 4,\n    slug: \"freelance-designer\",\n    title: \"Freelance Designer\",\n    company: \"Self-Employed\",\n    duration: \"2020 - 2021\",\n    logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n    logoAlt: \"Freelance Logo\",\n    summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n    roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n    responsibilities: [\n      \"Design logos and brand identities for small businesses\",\n      \"Create marketing materials including flyers, brochures, and business cards\",\n      \"Develop social media graphics and digital marketing assets\",\n      \"Collaborate directly with business owners to understand their vision\",\n      \"Manage multiple projects simultaneously while meeting deadlines\",\n      \"Handle client communications and project billing\"\n    ],\n    skills: {\n      \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n      \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n      \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"20+\",\n        description: \"Local businesses served with design solutions\"\n      },\n      {\n        metric: \"4.9/5\",\n        description: \"Average client rating on freelance platforms\"\n      },\n      {\n        metric: \"90%\",\n        description: \"Client retention rate for ongoing projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Local Coffee Shop Branding\",\n        description: \"Complete brand identity including logo, menu design, and signage\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Logo+Design\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Menu+Design\"\n        ],\n        technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"],\n        liveUrl: \"https://example-coffeeshop.com\"\n      },\n      {\n        title: \"Fitness Studio Marketing Kit\",\n        description: \"Comprehensive marketing materials for new fitness studio launch\",\n        images: [\n          \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n          \"https://via.placeholder.com/400x250/DC143C/FFFFFF?text=Brochure+Design\",\n          \"https://via.placeholder.com/400x250/FF4500/FFFFFF?text=Social+Media+Kit\"\n        ],\n        technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"],\n        liveUrl: \"https://example-fitness.com\"\n      }\n    ]\n  }\n  */];", "map": {"version": 3, "names": ["jobsData", "id", "slug", "title", "company", "companyLink", "duration", "logo", "logoAlt", "summary", "roleOverview", "responsibilities", "skills", "accomplishments", "metric", "description", "projects", "images", "technologies", "liveUrl"], "sources": ["C:/Users/<USER>/Desktop/Portfulio/portfolio-react/src/data/jobsData.js"], "sourcesContent": ["export const jobsData = [\n  {\n    id: 1,\n    slug: \"frontend-receeto\",\n    title: \"Frontend Developer Angular\",\n    company: \"Company : Receeto\",\n    companyLink: \"https://receeto.com\",\n    duration: \"2/2025 - 6/2025\",\n    logo: \"/Receeto_logo.jpg\",\n    logoAlt: \"Receeto Logo\",\n    summary: \"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\",\n    roleOverview: \"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\\n\\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\\n\\nKey Highlights:\\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\\n\\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\\n\\nPerformance Optimization:\\n• Lazy loading for improved speed\\n• Critical CSS and production build optimization\\n\\nTech Stack & Architecture:\\n• Angular SPA with reactive state (signals)\\n• Fully responsive design for mobile and desktop\\n• Custom financial data visualizations using charts\\n\\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces—while respecting the confidentiality of the client's program.\",\n    responsibilities: [\n      \"Develop responsive web applications using Angular and modern frontend technologies\",\n      \"Implement financial data visualizations and interactive charts\",\n      \"Optimize application performance through lazy loading and build optimization\",\n      \"Create expense tracking and budgeting tools with real-time data processing\",\n      \"Build responsive interfaces for both mobile and desktop platforms\",\n      \"Implement Angular reactive state management using signals\"\n    ],\n    skills: {\n      \"Frontend\": [\"Angular\", \"TypeScript\", \"RxJS\", \"Angular Signals\", \"Angular CLI\"],\n      \"Styling\": [\"CSS3\", \"SASS/SCSS\", \"Angular Material\", \"Responsive Design\", \"Bootstrap\"],\n      \"Tools & Testing\": [\"Git\", \"Angular CLI\", \"Webpack\", \"Lighthouse (for performance auditing)\", \"Figma\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Improved application performance through lazy loading and build optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Responsive design compatibility across mobile and desktop platforms\"\n      },\n      {\n        metric: \"NDA\",\n        description: \"Confidential project delivered successfully while maintaining client privacy\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Img 1\",\n        description: \"NDA - details confidential\",\n        images: [\"../NDA.jpg\", \"../NDA.jpg\", \"../NDA.jpg\"],\n        technologies: [\"Angular\", \"TypeScript\", \"Charts.js\"],\n        liveUrl: \"https://receeto.com\"\n      },\n      {\n        title: \"Img 2\",\n        description: \"NDA - details confidential\",\n        images: [\"../NDA.jpg\", \"../NDA.jpg\"],\n        technologies: [\"Angular\", \"RxJS\", \"Angular Material\"],\n        liveUrl: \"https://receeto.com\"\n      }\n    ]\n  },\n  {\n    id: 2,\n    slug: \"3d-ecommerce-platform\",\n    title: \"3D-ecommerce platform UI/UX Designer\",\n    company: \"DigitalStudio Creative\",\n    companyLink: \"https://threed-e-commerce.onrender.com\",\n    duration: \"2022 - 2023\",\n    logo: \"/3D E Logo.png\",\n    logoAlt: \"DigitalStudio Creative Logo\",\n    summary: \"Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.\",\n    roleOverview: \"As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\\n\\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\\n\\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.\",\n    responsibilities: [\n      \"Design user interfaces and experiences for the 3D e-commerce platform\",\n      \"Develop responsive frontend using React.js and modern web technologies\",\n      \"Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions\",\n      \"Create comprehensive design systems for consistent user experience across devices\",\n      \"Conduct usability testing to refine the shopping experience and 3D interactions\",\n      \"Optimize application performance across various devices and network conditions\",\n      \"Collaborate with clients to understand business requirements and technical constraints\",\n      \"Develop fallback mechanisms and error boundaries for graceful failure handling\",\n      \"Implement progressive enhancement and mobile-first responsive design principles\"\n    ],\n    skills: {\n      \"Frontend\": [\"React.js\", \"Three.js/WebGL\", \"JavaScript\", \"CSS3\", \"React Router\", \"Axios\"],\n      \"3D Technologies\": [\"Model Viewer\", \"GLTF/GLB\", \"OrbitControls\", \"Real-time Rendering\"],\n      \"Backend\": [\"Node.js\", \"Express.js\", \"MongoDB\", \"RESTful APIs\"],\n      \"Design & UX\": [\"Figma\", \"Responsive Design\", \"Mobile-First Design\", \"User Testing\", \"Performance Optimization\"],\n      \"Tools & Deployment\": [\"Git\", \"Render\", \"Webpack\", \"Font Awesome\", \"Chrome DevTools\"]\n    },\n    accomplishments: [\n      {\n        metric: \"40%\",\n        description: \"Increased user engagement through improved UX design and 3D interactions\"\n      },\n      {\n        metric: \"95%\",\n        description: \"Client satisfaction rate based on project feedback\"\n      }\n    ],\n    projects: [\n      {\n        title: \"3D Product Visualization Engine\",\n        description: \"Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.\",\n        images: [\n          \"../3D E Commerce Home.PNG\",\n          \"../home mobil mode.PNG\",\n        ],\n        technologies: [\"Three.js\", \"WebGL\", \"GLTF/GLB\", \"OrbitControls\"],\n        liveUrl: \"https://threed-e-commerce.onrender.com\"\n      },\n      {\n        title: \"Mobile-Responsive Shopping Interface\",\n        description: \"Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.\",\n        images: [\n          \"../All product pc.PNG\",\n          \"../all products mobil mode.PNG\"\n        ],\n        technologies: [\"React.js\", \"CSS3\", \"Responsive Design\", \"Mobile UX\"],\n        liveUrl: \"https://threed-e-commerce.onrender.com\"\n      },\n      {\n        title: \"Performance-Optimized Architecture\",\n        description: \"Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.\",\n        images: [\n          \"../air pods pc mode.PNG\",\n          \"../air pods mobil mode.PNG\",\n          \n        ],\n        technologies: [\"Node.js\", \"Express.js\", \"MongoDB\", \"Render\", \"API Design\"],\n        liveUrl: \"https://threed-e-commerce-backend.onrender.com\"\n      }\n    ]\n  },\n  /*\n  /* Job ID 3 and 4 are currently hidden\n  {\n    id: 3,\n    slug: \"junior-web-developer\",\n    title: \"Junior Web Developer\",\n    company: \"WebDev Agency\",\n    duration: \"2021 - 2022\",\n    logo: \"https://via.placeholder.com/120x120/00CED1/FFFFFF?text=WD\",\n    logoAlt: \"WebDev Agency Logo\",\n    summary: \"Developed custom WordPress themes and e-commerce solutions. Gained expertise in HTML, CSS, JavaScript, and PHP while working on diverse client projects ranging from small businesses to enterprise solutions.\",\n    roleOverview: \"As a Junior Web Developer at WebDev Agency, I focused on building custom websites and e-commerce solutions for a diverse range of clients. This role provided me with a solid foundation in web development fundamentals and client communication skills.\",\n    responsibilities: [\n      \"Develop custom WordPress themes and plugins\",\n      \"Build responsive websites using HTML, CSS, and JavaScript\",\n      \"Create e-commerce solutions using WooCommerce and Shopify\",\n      \"Collaborate with designers to implement pixel-perfect designs\",\n      \"Optimize websites for performance and SEO\",\n      \"Provide technical support and maintenance for client websites\"\n    ],\n    skills: {\n      \"Frontend\": [\"HTML5\", \"CSS3\", \"JavaScript\", \"jQuery\", \"Bootstrap\"],\n      \"Backend\": [\"PHP\", \"MySQL\", \"WordPress\", \"WooCommerce\"],\n      \"Tools\": [\"Git\", \"Photoshop\", \"Chrome DevTools\", \"FTP\", \"cPanel\"]\n    },\n    accomplishments: [\n      {\n        metric: \"30+\",\n        description: \"Websites successfully developed and launched\"\n      },\n      {\n        metric: \"50%\",\n        description: \"Improvement in page load speeds through optimization\"\n      },\n      {\n        metric: \"100%\",\n        description: \"Client satisfaction rate for delivered projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Restaurant Chain Website\",\n        description: \"Built a multi-location restaurant website with online ordering system\",\n        images: [\n          \"https://via.placeholder.com/400x250/00CED1/FFFFFF?text=Restaurant+Website\",\n          \"https://via.placeholder.com/400x250/008B8B/FFFFFF?text=Menu+System\",\n          \"https://via.placeholder.com/400x250/20B2AA/FFFFFF?text=Order+Management\"\n        ],\n        technologies: [\"WordPress\", \"WooCommerce\", \"PHP\"],\n        liveUrl: \"https://example-restaurant.com\"\n      },\n      {\n        title: \"Real Estate Portal\",\n        description: \"Developed property listing website with advanced search functionality\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Real+Estate+Portal\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Property+Search\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Listing+Details\"\n        ],\n        technologies: [\"HTML\", \"CSS\", \"JavaScript\", \"PHP\"],\n        liveUrl: \"https://example-realestate.com\"\n      }\n    ]\n  },\n  {\n    id: 4,\n    slug: \"freelance-designer\",\n    title: \"Freelance Designer\",\n    company: \"Self-Employed\",\n    duration: \"2020 - 2021\",\n    logo: \"https://via.placeholder.com/120x120/32CD32/FFFFFF?text=FL\",\n    logoAlt: \"Freelance Logo\",\n    summary: \"Started my journey as a freelance graphic designer, creating logos, branding materials, and marketing collateral for local businesses. Built a strong foundation in design principles and client communication.\",\n    roleOverview: \"Beginning my career as a freelance designer, I worked with local businesses to create compelling visual identities and marketing materials. This experience taught me the importance of understanding client needs and translating business objectives into effective design solutions.\",\n    responsibilities: [\n      \"Design logos and brand identities for small businesses\",\n      \"Create marketing materials including flyers, brochures, and business cards\",\n      \"Develop social media graphics and digital marketing assets\",\n      \"Collaborate directly with business owners to understand their vision\",\n      \"Manage multiple projects simultaneously while meeting deadlines\",\n      \"Handle client communications and project billing\"\n    ],\n    skills: {\n      \"Design Software\": [\"Adobe Illustrator\", \"Adobe Photoshop\", \"Adobe InDesign\", \"Canva\"],\n      \"Design Skills\": [\"Logo Design\", \"Brand Identity\", \"Print Design\", \"Digital Graphics\"],\n      \"Business Skills\": [\"Client Communication\", \"Project Management\", \"Time Management\", \"Pricing\"]\n    },\n    accomplishments: [\n      {\n        metric: \"20+\",\n        description: \"Local businesses served with design solutions\"\n      },\n      {\n        metric: \"4.9/5\",\n        description: \"Average client rating on freelance platforms\"\n      },\n      {\n        metric: \"90%\",\n        description: \"Client retention rate for ongoing projects\"\n      }\n    ],\n    projects: [\n      {\n        title: \"Local Coffee Shop Branding\",\n        description: \"Complete brand identity including logo, menu design, and signage\",\n        images: [\n          \"https://via.placeholder.com/400x250/32CD32/FFFFFF?text=Coffee+Shop+Brand\",\n          \"https://via.placeholder.com/400x250/228B22/FFFFFF?text=Logo+Design\",\n          \"https://via.placeholder.com/400x250/90EE90/FFFFFF?text=Menu+Design\"\n        ],\n        technologies: [\"Illustrator\", \"Photoshop\", \"InDesign\"],\n        liveUrl: \"https://example-coffeeshop.com\"\n      },\n      {\n        title: \"Fitness Studio Marketing Kit\",\n        description: \"Comprehensive marketing materials for new fitness studio launch\",\n        images: [\n          \"https://via.placeholder.com/400x250/FF6347/FFFFFF?text=Fitness+Marketing\",\n          \"https://via.placeholder.com/400x250/DC143C/FFFFFF?text=Brochure+Design\",\n          \"https://via.placeholder.com/400x250/FF4500/FFFFFF?text=Social+Media+Kit\"\n        ],\n        technologies: [\"Photoshop\", \"Illustrator\", \"Print Design\"],\n        liveUrl: \"https://example-fitness.com\"\n      }\n    ]\n  }\n  */\n];\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,QAAQ,CAAG,CACtB,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,kBAAkB,CACxBC,KAAK,CAAE,4BAA4B,CACnCC,OAAO,CAAE,mBAAmB,CAC5BC,WAAW,CAAE,qBAAqB,CAClCC,QAAQ,CAAE,iBAAiB,CAC3BC,IAAI,CAAE,mBAAmB,CACzBC,OAAO,CAAE,cAAc,CACvBC,OAAO,CAAE,gJAAgJ,CACzJC,YAAY,CAAE,4jCAA4jC,CAC1kCC,gBAAgB,CAAE,CAChB,oFAAoF,CACpF,gEAAgE,CAChE,8EAA8E,CAC9E,4EAA4E,CAC5E,mEAAmE,CACnE,2DAA2D,CAC5D,CACDC,MAAM,CAAE,CACN,UAAU,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,MAAM,CAAE,iBAAiB,CAAE,aAAa,CAAC,CAC/E,SAAS,CAAE,CAAC,MAAM,CAAE,WAAW,CAAE,kBAAkB,CAAE,mBAAmB,CAAE,WAAW,CAAC,CACtF,iBAAiB,CAAE,CAAC,KAAK,CAAE,aAAa,CAAE,SAAS,CAAE,uCAAuC,CAAE,OAAO,CACvG,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,8EACf,CAAC,CACD,CACED,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,qEACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,8EACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEb,KAAK,CAAE,OAAO,CACdY,WAAW,CAAE,4BAA4B,CACzCE,MAAM,CAAE,CAAC,YAAY,CAAE,YAAY,CAAE,YAAY,CAAC,CAClDC,YAAY,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,WAAW,CAAC,CACpDC,OAAO,CAAE,qBACX,CAAC,CACD,CACEhB,KAAK,CAAE,OAAO,CACdY,WAAW,CAAE,4BAA4B,CACzCE,MAAM,CAAE,CAAC,YAAY,CAAE,YAAY,CAAC,CACpCC,YAAY,CAAE,CAAC,SAAS,CAAE,MAAM,CAAE,kBAAkB,CAAC,CACrDC,OAAO,CAAE,qBACX,CAAC,CAEL,CAAC,CACD,CACElB,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,uBAAuB,CAC7BC,KAAK,CAAE,sCAAsC,CAC7CC,OAAO,CAAE,wBAAwB,CACjCC,WAAW,CAAE,wCAAwC,CACrDC,QAAQ,CAAE,aAAa,CACvBC,IAAI,CAAE,gBAAgB,CACtBC,OAAO,CAAE,6BAA6B,CACtCC,OAAO,CAAE,gPAAgP,CACzPC,YAAY,CAAE,g8BAAg8B,CAC98BC,gBAAgB,CAAE,CAChB,uEAAuE,CACvE,wEAAwE,CACxE,wFAAwF,CACxF,mFAAmF,CACnF,iFAAiF,CACjF,gFAAgF,CAChF,wFAAwF,CACxF,gFAAgF,CAChF,iFAAiF,CAClF,CACDC,MAAM,CAAE,CACN,UAAU,CAAE,CAAC,UAAU,CAAE,gBAAgB,CAAE,YAAY,CAAE,MAAM,CAAE,cAAc,CAAE,OAAO,CAAC,CACzF,iBAAiB,CAAE,CAAC,cAAc,CAAE,UAAU,CAAE,eAAe,CAAE,qBAAqB,CAAC,CACvF,SAAS,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,SAAS,CAAE,cAAc,CAAC,CAC/D,aAAa,CAAE,CAAC,OAAO,CAAE,mBAAmB,CAAE,qBAAqB,CAAE,cAAc,CAAE,0BAA0B,CAAC,CAChH,oBAAoB,CAAE,CAAC,KAAK,CAAE,QAAQ,CAAE,SAAS,CAAE,cAAc,CAAE,iBAAiB,CACtF,CAAC,CACDC,eAAe,CAAE,CACf,CACEC,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,0EACf,CAAC,CACD,CACED,MAAM,CAAE,KAAK,CACbC,WAAW,CAAE,oDACf,CAAC,CACF,CACDC,QAAQ,CAAE,CACR,CACEb,KAAK,CAAE,iCAAiC,CACxCY,WAAW,CAAE,2KAA2K,CACxLE,MAAM,CAAE,CACN,2BAA2B,CAC3B,wBAAwB,CACzB,CACDC,YAAY,CAAE,CAAC,UAAU,CAAE,OAAO,CAAE,UAAU,CAAE,eAAe,CAAC,CAChEC,OAAO,CAAE,wCACX,CAAC,CACD,CACEhB,KAAK,CAAE,sCAAsC,CAC7CY,WAAW,CAAE,qKAAqK,CAClLE,MAAM,CAAE,CACN,uBAAuB,CACvB,gCAAgC,CACjC,CACDC,YAAY,CAAE,CAAC,UAAU,CAAE,MAAM,CAAE,mBAAmB,CAAE,WAAW,CAAC,CACpEC,OAAO,CAAE,wCACX,CAAC,CACD,CACEhB,KAAK,CAAE,oCAAoC,CAC3CY,WAAW,CAAE,uJAAuJ,CACpKE,MAAM,CAAE,CACN,yBAAyB,CACzB,4BAA4B,CAE7B,CACDC,YAAY,CAAE,CAAC,SAAS,CAAE,YAAY,CAAE,SAAS,CAAE,QAAQ,CAAE,YAAY,CAAC,CAC1EC,OAAO,CAAE,gDACX,CAAC,CAEL,CACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IA9HE,CA+HD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}