{"version": 3, "file": "static/css/main.cdb40a90.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAKF,CAEA,KACE,uEAEF,CCXA,EAGI,qBACJ,CAEA,OALI,QAAS,CACT,SAYJ,CARA,KAEI,qBAAuB,CACvB,UAAc,CAFd,iCAAqC,CAGrC,eAAgB,CAChB,iBAGJ,CAGA,OAGI,kBAAmB,CAKnB,0BAAoC,CACpC,qBAAsB,CARtB,YAAa,CACb,6BAA8B,CAE9B,iBAAkB,CAElB,KAAM,CADN,UAAW,CAEX,WAGJ,CAKA,yBACI,OAGI,4BAA8B,CAG9B,0BAA4B,CAF5B,uCAAyC,CAGzC,eAAgB,CAGhB,eAAgB,CAFhB,iBAAkB,CAPlB,iBAAkB,CAQlB,oBAEJ,CACA,aAPI,4BAA8B,CAH9B,sBAeJ,CALA,MACI,iBAAkB,CAGlB,aACJ,CACA,UAEI,qBAAuB,CADvB,wBAEJ,CACA,WAUI,+BAAiC,CAHjC,oBAAqB,CAFrB,wBAA0B,CAJ1B,gBAAiB,CAEjB,aAAc,CAKd,eAAgB,CAJhB,2BAA6B,CAE7B,iBAAkB,CAGlB,sBAAuB,CAPvB,4BASJ,CACJ,CAGA,yBACI,OACI,cACJ,CACA,KACI,gBACJ,CACJ,CAEA,UAII,uBAAyB,CADzB,iBAAkB,CAElB,+BAA6C,CAJ7C,WAAY,CAMZ,gBAAiB,CACjB,0BAA2B,CAF3B,6BAA+B,CAJ/B,UAOJ,CAEA,gBACI,oBACJ,CAEA,WACI,uBAAyB,CAIzB,kBAAmB,CAHnB,UAAc,CAId,eAAgB,CAFhB,iBAAkB,CADlB,oBAAqB,CAIrB,wBAAyB,CACzB,oCACJ,CAEA,iBACI,qBAAyB,CACzB,YACJ,CAGA,OAGI,eAAgB,CADhB,kBAAmB,CADnB,iBAGJ,CAEA,UACI,cAAe,CACf,eAAgB,CAChB,kBACJ,CAEA,kBACI,YACJ,CAEA,SACI,cAAe,CACf,aAAc,CACd,wBACJ,CAGA,gBAQI,aAAS,CAFT,kBAAmB,CAJnB,YAAa,CAMb,QAAS,CAJT,mCACmB,CAFnB,6BAA8B,CAI9B,oBAAqB,CAGrB,aAAc,CADd,gBAAiB,CARjB,iBAAkB,CAUlB,UACJ,CAEA,kBACI,iBAAkB,CAElB,cAAe,CADf,iBAEJ,CAEA,qBACI,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,wBAEJ,CAEA,oBACI,cAAe,CAEf,kBAAmB,CADnB,eAEJ,CAEA,uCACI,YAAa,CAEb,cAAe,CACf,QAAS,CAFT,sBAAuB,CAIvB,aAAc,CADd,cAEJ,CAEA,UACI,YAAa,CACb,QACJ,CAEA,cAEI,eAAgB,CADhB,iBAEJ,CAEA,kBAII,uBAAyB,CADzB,iBAAkB,CAFlB,WAAY,CACZ,kBAGJ,CAEA,iBACI,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,wBAEJ,CAEA,gBACI,cACJ,CAEA,YAEI,kBAAmB,CAInB,YAAc,CALd,YAAa,CAGb,cAAe,CADf,QAAS,CAIT,cAAe,CAFf,wBAGJ,CAEA,qBAII,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,eAAgB,CAKhB,cAAe,CADf,iBAEJ,CAEA,aACI,kBACJ,CAEA,iBASI,gBAAiB,CAJjB,uBAAyB,CADzB,iBAAkB,CAElB,+BAA6C,CAH7C,WAAY,CADZ,eAAgB,CAKhB,gBAAiB,CACjB,0BAA2B,CAP3B,UASJ,CAEA,cACI,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,kBACJ,CAEA,kBACI,WAAY,CACZ,6BACJ,CAEA,wBACI,oBACJ,CAGA,0BACI,gBAGI,QAAS,CAFT,6BAA8B,CAC9B,iBAEJ,CACA,iBAII,gBAAiB,CAHjB,eAAgB,CAChB,gBAAiB,CACjB,0BAEJ,CACA,qBACI,cACJ,CACA,oBACI,cACJ,CACA,kBACI,WACJ,CACA,iBACI,cACJ,CACA,gBACI,cACJ,CACA,kBACI,WACJ,CACA,YACI,cAAe,CACf,OACJ,CACJ,CAEA,yBACI,gBAMI,QAAS,CAJT,qCAEa,CAHb,yBAA0B,CAI1B,iBAEJ,CACA,iBAII,gBAAiB,CAHjB,eAAgB,CAChB,gBAAiB,CACjB,0BAEJ,CACA,qBACI,cACJ,CACA,oBACI,cACJ,CACA,uCAEI,kBAAmB,CADnB,qBAEJ,CACA,UACI,qBAAsB,CACtB,QACJ,CACA,cACI,cACJ,CACA,kBACI,WACJ,CACA,iBACI,cACJ,CACA,gBACI,cACJ,CACA,kBACI,WACJ,CACA,YACI,cAAe,CACf,OACJ,CACJ,CAEA,yBACI,gBAEI,QAAS,CADT,iBAEJ,CACA,iBAII,gBAAiB,CAHjB,eAAgB,CAChB,gBAAiB,CACjB,0BAEJ,CACA,qBACI,cACJ,CACA,oBACI,cACJ,CACA,kBACI,WACJ,CACA,iBACI,cACJ,CACA,gBACI,cACJ,CACA,kBACI,WACJ,CACA,cACI,QACJ,CACA,YAGI,cAAe,CAFf,cAAe,CACf,OAEJ,CACJ,CAGA,YACI,iBAAkB,CAElB,iBAAkB,CADlB,iBAEJ,CAEA,eAKI,UAAc,CAJd,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,wBAGJ,CAEA,UAGI,aAAc,CADd,gBAAiB,CADjB,iBAGJ,CAEA,iBAOI,wDAA8D,CAF9D,QAAS,CAJT,UAAW,CAEX,QAAS,CADT,iBAAkB,CAElB,KAAM,CAIN,0BAA2B,CAF3B,SAAU,CAGV,SACJ,CAEA,eAII,oCAAsC,CAFtC,aAAc,CACd,SAAU,CAFV,iBAIJ,CAEA,2BAA8B,mBAAuB,CACrD,4BAA8B,mBAAuB,CACrD,4BAA8B,mBAAuB,CACrD,4BAA8B,mBAAuB,CAErD,gDACI,eAAgB,CAChB,eACJ,CAEA,+CACI,gBAAiB,CACjB,gBACJ,CAEA,cAMI,iBAAmB,CACnB,qBAAyB,CACzB,iBAAkB,CAHlB,WAAY,CAHZ,QAAS,CADT,iBAAkB,CAElB,QAAS,CAMT,0BAA2B,CAE3B,uBAAyB,CAPzB,UAAW,CAMX,SAEJ,CAEA,mCACI,kBAAmB,CAEnB,6BAA2C,CAD3C,qCAEJ,CAEA,kBACI,kCAA2B,CAA3B,0BAA2B,CAI3B,sBAA0C,CAH1C,kBAAmB,CAEnB,+BAA4C,CAD5C,YAAa,CAIb,iBAAkB,CADlB,uBAEJ,CAEA,wBAEI,gCAA6C,CAD7C,0BAEJ,CAGA,uBAGI,aAAc,CACd,uBACJ,CAEA,oDALI,aAAc,CADd,oBASJ,CAEA,cACI,eAAgB,CAChB,SAAU,CACV,0BAA2B,CAC3B,uBACJ,CAEA,mBACI,aAAc,CACd,iCAAqC,CAErC,cAAe,CADf,eAAgB,CAGhB,kBAAmB,CADnB,wBAEJ,CAEA,2CACI,SAAU,CACV,uBACJ,CAEA,gDACI,UACJ,CAEA,cAII,uBAAyB,CADzB,iBAAkB,CADlB,WAAY,CAGZ,kBAAmB,CACnB,gBAAiB,CACjB,uBAAyB,CANzB,UAOJ,CAEA,mCACI,oBAAqB,CACrB,oBACJ,CAEA,WAGI,YAAc,CAFd,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CADnB,wBAAyB,CAEzB,yBACJ,CAEA,gCACI,aACJ,CAEA,cAGI,UAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACJ,CAEA,cAEI,aAAc,CADd,cAAe,CAIf,eAAgB,CADhB,kBAAmB,CADnB,wBAGJ,CAEA,iBAGI,eAAgC,CAFhC,cAAe,CAGf,eAAgB,CAFhB,eAAgB,CAGhB,+BACJ,CAEA,oBACI,GACI,SAAU,CACV,0BACJ,CACA,GACI,SAAU,CACV,uBACJ,CACJ,CAGA,YACI,iBAAkB,CAClB,iBACJ,CAEA,eACI,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,wBAEJ,CAEA,YAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,gBAEJ,CAEA,SAGI,YAAc,CAFd,cAAe,CACf,eAEJ,CAEA,QACI,cAAe,CACf,wBACJ,CAEA,iBAGI,WAAY,CACZ,aAAc,CAFd,eAAgB,CADhB,UAIJ,CAEA,eAEI,uBAAyB,CAIzB,kBAAmB,CAHnB,UAAc,CAFd,oBAAqB,CAMrB,eAAgB,CAFhB,iBAAkB,CADlB,oBAAqB,CAIrB,wBAAyB,CACzB,oCACJ,CAEA,qBACI,qBAAyB,CACzB,YACJ,CAiCA,gBAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,aAAc,CADd,gBAEJ,CAEA,oBAEI,WAAY,CADZ,UAEJ,CAEA,kBACI,cAAe,CACf,eAAgB,CAChB,aACJ,CAEA,qBAEI,YAAc,CADd,cAAe,CAEf,wBACJ,CAEA,kBAGI,aAAc,CAFd,aAAc,CACd,oBAEJ,CAEA,wBACI,UACJ,CAIA,iBACI,iBAAkB,CAClB,iBACJ,CAEA,gBACI,WAAY,CACZ,kBACJ,CAEA,oBACI,cAIJ,CAEA,wCALI,eAAgB,CAEhB,kBAAmB,CADnB,wBASJ,CALA,oBACI,cAIJ,CAEA,mBACI,cAAe,CAEf,kBAAmB,CADnB,eAEJ,CAEA,aAGI,YAAc,CAFd,cAAe,CACf,wBAEJ,CAEA,cACI,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,eACJ,CAEA,kBACI,WACJ,CAEA,iCAII,iBAAkB,CAClB,8BAA4C,CAH5C,WAAY,CAIZ,kBAAmB,CAHnB,6BAA+B,CAF/B,WAMJ,CAEA,uCACI,oBACJ,CAEA,qCACI,8BAA4C,CAC5C,6BACJ,CAEA,2CACI,qBACJ,CAGA,SAUI,kBAAmB,CANnB,gGAAoE,CACpE,YAAa,CAEb,mCACmB,CAFnB,6BAA8B,CAJ9B,gBAAiB,CAOjB,eAAgB,CANhB,iBAAkB,CAFlB,iBAUJ,CAEA,iBAWI,oBAA8B,CAP9B,2BAA4B,CAI5B,YAAa,CACb,qBAAsB,CARtB,iBAAkB,CASlB,sBAAuB,CALvB,eAAgB,CAFhB,YAAa,CAIb,iBAAkB,CADlB,UAAW,CAJX,SAUJ,CAEA,YACI,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CADnB,kBAEJ,CAEA,2BALI,UAAc,CADd,wBAcJ,CARA,eACI,aAAc,CACd,cAAe,CAGf,iBAAkB,CAElB,UAAY,CADZ,eAEJ,CAEA,iCAKI,0BAA0C,CAC1C,sBAA0C,CAG1C,iBAAkB,CAClB,oCAA8C,CAH9C,UAAc,CACd,cAAe,CAJf,kBAAmB,CADnB,YAAa,CAQb,gCAAkC,CATlC,UAUJ,CAEA,6CAEI,mBAAqB,CACrB,YACJ,CAEA,kBACI,YAAa,CACb,WACJ,CAEA,2DAEI,UAAc,CACd,UACJ,CAEA,eAcI,kBAAmB,CAbnB,uBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,UAAc,CAMd,cAAe,CAIf,YAAa,CAIb,cAAe,CAVf,eAAgB,CAKhB,WAAY,CAEZ,sBAAuB,CAEvB,kBAAmB,CAXnB,SAAU,CAGV,wBAAyB,CAEzB,uDAA2D,CAC3D,UAOJ,CAEA,qBACI,qBAAyB,CACzB,YAAc,CACd,oBACJ,CAGA,0BACI,SACI,6BAA8B,CAC9B,iBACJ,CACA,iBACI,eAAgB,CAChB,YACJ,CACA,YACI,cACJ,CACA,iCAGI,cAAe,CADf,YAEJ,CACA,eAGI,cAAe,CADf,WAAY,CADZ,UAGJ,CACJ,CAEA,yBACI,SAMI,kBAAmB,CACnB,qBAAsB,CALtB,6BACa,CAFb,yBAA0B,CAI1B,eAAgB,CADhB,iBAIJ,CACA,iBAKI,gBAA8B,CAF9B,kBAAmB,CACnB,aAAc,CAHd,eAAgB,CAChB,YAIJ,CACA,YACI,cACJ,CACA,eACI,cACJ,CACA,iCAGI,cAAe,CADf,WAEJ,CACA,kBACI,YACJ,CACA,eAGI,cAAe,CADf,WAAY,CADZ,UAGJ,CACJ,CAEA,yBACI,SACI,iBACJ,CACA,iBACI,eAAgB,CAChB,YACJ,CACA,YACI,cAAe,CACf,kBACJ,CACA,eACI,cACJ,CACA,iCAGI,cAAe,CACf,kBAAmB,CAFnB,WAGJ,CACA,kBACI,WACJ,CACA,eAGI,cAAe,CADf,WAAY,CAEZ,eAAgB,CAHhB,UAIJ,CACJ,CAGA,OAGI,cAAe,CADf,YAAa,CADb,iBAGJ,CAGA,aAWI,kBAAmB,CAPnB,uBAAyB,CAQzB,iBAAkB,CAVlB,WAAY,CAGZ,UAAc,CAId,YAAa,CAHb,cAAe,CAEf,WAAY,CAEZ,sBAAuB,CATvB,cAAe,CAEf,UAAW,CAUX,oBAAqB,CACrB,oCAAsC,CAPtC,UAQJ,CAEA,mBACI,qBAAyB,CACzB,YACJ,CAGA,0BACI,YACI,iBACJ,CAEA,gDACI,eACJ,CAEA,+CACI,gBACJ,CAEA,kBACI,YACJ,CAEA,WACI,cACJ,CAEA,cACI,cACJ,CACJ,CAEA,yBACI,YACI,iBACJ,CAEA,eACI,cAAe,CACf,kBACJ,CAGA,iBAII,gEAAuE,CACvE,6BAA0C,CAJ1C,QAAS,CACT,0BAA2B,CAC3B,SAGJ,CAGA,gDACI,eAAgB,CAChB,eAAgB,CAEhB,iBAAkB,CADlB,eAEJ,CAEA,+CAEI,cAAe,CADf,gBAAiB,CAGjB,iBAAkB,CADlB,gBAEJ,CAGA,uDAOI,gDAAoD,CACpD,iBAAkB,CAPlB,UAAW,CAKX,UAAW,CAHX,UAAW,CADX,iBAAkB,CAElB,QAAS,CACT,UAIJ,CAEA,sDAOI,gDAAoD,CACpD,iBAAkB,CAPlB,UAAW,CAKX,UAAW,CAJX,iBAAkB,CAClB,WAAY,CACZ,QAAS,CACT,UAIJ,CAEA,cAKI,qBAAyB,CACzB,6BAA0C,CAF1C,WAAY,CAHZ,QAAS,CACT,0BAA2B,CAC3B,UAIJ,CAEA,mCAEI,6BAA2C,CAD3C,qCAEJ,CAEA,kBAGI,kCAA2B,CAA3B,0BAA2B,CAC3B,0BAA2C,CAF3C,kBAAmB,CAGnB,oDAEqC,CACrC,gBAAiB,CAPjB,iBAQJ,CAUA,uEAEI,sDAEsC,CAHtC,sCAIJ,CAEA,cAII,uBAAyB,CACzB,+BAA4C,CAH5C,WAAY,CACZ,kBAAmB,CAFnB,UAKJ,CAEA,mCACI,oBAAqB,CAErB,+BAA6C,CAD7C,qBAEJ,CAEA,WACI,cAAe,CACf,iBACJ,CAEA,cACI,cAAe,CACf,iBACJ,CAEA,cACI,cAAe,CAEf,eAAgB,CADhB,kBAEJ,CAEA,iBAGI,eAAgC,CAFhC,cAAe,CAGf,eAAgB,CAFhB,eAAgB,CAGhB,2BACJ,CACJ,CAEA,yBACI,YACI,iBACJ,CAEA,eACI,cAAe,CACf,kBACJ,CAEA,eACI,aACJ,CAGA,gDACI,eAAgB,CAChB,eAAgB,CAChB,eACJ,CAEA,+CAEI,cAAe,CADf,gBAAiB,CAEjB,gBACJ,CAGA,uDACI,UAAW,CAEX,QAAS,CADT,UAEJ,CAEA,sDACI,WAAY,CAEZ,QAAS,CADT,UAEJ,CAEA,iBAEI,4BAAyC,CADzC,SAEJ,CAEA,cAGI,qBAAyB,CADzB,WAAY,CADZ,UAGJ,CAEA,mCACI,qCACJ,CAEA,kBAEI,kBAAmB,CACnB,gBAAiB,CAFjB,iBAGJ,CAOA,uEACI,sCACJ,CAEA,cAII,uBAAyB,CAFzB,WAAY,CACZ,kBAAmB,CAFnB,UAIJ,CAEA,mCACI,oBACJ,CAEA,WACI,cAAe,CACf,iBACJ,CAEA,cACI,cAAe,CACf,iBACJ,CAEA,cACI,cAAe,CAEf,eAAgB,CADhB,kBAEJ,CAEA,iBAGI,eAAgC,CAFhC,cAAe,CAGf,eAAgB,CAFhB,eAAgB,CAGhB,+BACJ,CACJ,CAGA,yBACI,UACI,cACJ,CAEA,YACI,mCACJ,CACJ,CAEA,yBACI,OACI,qBAAsB,CACtB,QACJ,CAEA,UACI,cACJ,CAEA,YACI,yBACJ,CACJ,CAGA,WACI,iBAAkB,CAClB,iBACJ,CAEA,cACI,cAAe,CAGf,kBACJ,CAEA,+BALI,eAAgB,CAChB,wBAeJ,CAXA,iBAEI,uBAAyB,CAIzB,kBAAmB,CAHnB,UAAc,CAFd,oBAAqB,CAQrB,kBAAmB,CAJnB,iBAAkB,CADlB,oBAAqB,CAMrB,oCACJ,CAEA,uBACI,qBAAyB,CACzB,YACJ,CAEA,oBAGI,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAChB,iBAAkB,CAClB,wBAAiB,CAAjB,gBAAiB,CALjB,UAMJ,CAEA,gBAII,gCAAiC,CACjC,WAAY,CAJZ,YAAa,CAEb,iBAAkB,CADlB,UAIJ,CAEA,yBACI,eACJ,CAEA,gCASI,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAFrC,kBAAmB,CAInB,4EAG0C,CAZ1C,cAAe,CACf,iBAAkB,CAElB,eAAgB,CAGhB,YAAa,CAJb,iBAAkB,CAElB,iDASJ,CAEA,sCAEI,6FAI0C,CAL1C,uCAMJ,CAEA,oCAGI,kBAAmB,CAEnB,+BAAyC,CAHzC,WAAY,CAEZ,6CAAiD,CAHjD,UAKJ,CAEA,0CAEI,oCAAqC,CADrC,qBAEJ,CAEA,kCACI,cAAe,CACf,eAAgB,CAChB,kBAAmB,CACnB,wBAAyB,CACzB,yBACJ,CAEA,wCACI,aAAc,CACd,+BACJ,CAGA,yBACI,gBACI,UACJ,CACA,gCACI,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,YACJ,CAEA,sCACI,sCACJ,CAEA,kCACI,cAAe,CACf,iBACJ,CACJ,CAKA,eAEI,uBAAyB,CAKzB,aAAc,CAJd,eAAgB,CAChB,cAAe,CAIf,iBAAkB,CAHlB,sBAAuB,CACvB,uBAAwB,CALxB,UAQJ,CAEA,cAGI,0CAA2C,CAF3C,YAAa,CACb,kBAEJ,CAEA,mBAII,UAAc,CAGd,oBAAqB,CANrB,cAAe,CACf,eAAgB,CAGhB,aAAc,CAFd,wBAAyB,CAGzB,uBAEJ,CAGA,wBACI,GACI,uBACJ,CACA,GACI,0BACJ,CACJ,CAGA,oBACI,2BACJ,CAGA,yBACI,eAEI,cAAe,CADf,sBAEJ,CACA,mBACI,uBACJ,CACJ,CC96CA,WACE,wBAA2B,CAG3B,iBAAkB,CADlB,eAAgB,CADhB,4rEAGF,CAEA,MACE,4BAKF,CACA,MAEE,aAAc,CACd,gBAAiB,CACjB,iBAAkB,CAHlB,iBAAkB,CAIlB,SACF,CACA,QASE,aAAc,CAJd,eAAgB,CAJhB,gBAAiB,CACjB,iBAAkB,CAElB,eAAgB,CAEhB,SAAU,CAHV,iBAAkB,CAKlB,SAEF,CACA,iCACE,qBACF,CACA,gBAQE,kBAAuB,CAHvB,YAAa,CAFb,WAAY,CAFZ,iBAAkB,CAKlB,6BAA8B,CAC9B,+BAAqF,CAArF,mFAAqF,CALrF,UAAW,CAEX,SAKF,CACA,wEAGE,uBACF,CACA,mBACE,kBACF,CACA,iBACE,kBACF,CACA,cAME,aAAc,CALd,aAAc,CAEd,WAAY,CACZ,iBAAkB,CAClB,6BAA8B,CAH9B,UAKF,CACA,8BACE,iBACF,CAEA,oDAEE,WACF,CACA,mCACE,sBAAuB,CACvB,oCACF,CACA,sCAEE,kCAAmC,CAC3B,0BAA2B,CAFnC,uBAGF,CAEA,2CACE,kBACF,CACA,2BACE,2BACF,CACA,WACE,kBACF,CACA,wDAEE,2BACF,CAEA,iCAIE,uBAAwB,CAHxB,aAAc,CACd,oBAIF,CACA,oDACE,YACF,CACA,+CACE,6BACF,CACA,mDACE,4BACF,CACA,iDACE,4BACF,CACA,kDACE,qBACF,CACA,gEACE,sBACF,CACA,wDACE,UAAW,CACX,aAAc,CACd,UACF,CACA,+DACE,+BAAgC,CAChC,uBACF,CACA,6FACE,yDAAyD,CAAzD,wDACF,CACA,0EACE,WAAY,CACZ,cAAe,CACf,yCACF,CACA,2FACE,0DAAwD,CAAxD,uDACF,CACA,wEAGE,0CAA2C,CAD3C,aAAc,CADd,UAGF,CAGA,sLAcE,WAAY,CAHZ,MAAO,CAIP,mBAAoB,CALpB,iBAAkB,CAElB,KAAM,CACN,UAAW,CAGX,UACF,CACA,gCACE,oBACF,CACA,qCACE,wDACF,CACA,sCACE,uDACF,CACA,oCACE,sDACF,CACA,uCACE,wDACF,CACA,uBAWE,wBAA0E,CAE1E,wEAA6B,CAD7B,iBAAkB,CAClB,0BAA6B,CAH7B,qBAAsB,CARtB,WAAY,CAEZ,QAAS,CAET,iBAAkB,CAClB,gBAAiB,CAJjB,iBAAkB,CAElB,OAAQ,CAIR,oBAAqB,CARrB,UAAW,CAOX,UAMF,CACA,+HAEE,kDACF,CACA,6BACE,6BACF,CACA,6BACE,6BACF,CACA,iCACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CClOA,MACE,6BAMF,CACA,wCAUE,kBAAmB,CAEnB,+BAAgE,CAAhE,8DAAgE,CAJhE,cAAe,CACf,YAAa,CAJb,WAAqC,CAArC,oCAAqC,CAMrC,sBAAuB,CALvB,gBAA2D,CAA3D,sDAA2D,CAJ3D,iBAAkB,CAClB,OAA6C,CAA7C,2CAA6C,CAC7C,UAAoD,CAApD,+CAAoD,CAGpD,UAMF,CACA,sFAGE,WAAY,CADZ,WAAa,CAEb,mBACF,CACA,kFAGE,WAAY,CADZ,SAAU,CAEV,mBACF,CACA,gGAEE,sBACF,CACA,gDAGE,WAAY,CACZ,kBAAmB,CACnB,uBAAwB,CAHxB,UAIF,CACA,wEAEE,wBACF,CACA,oDAEE,SAAiD,CAAjD,+CAAiD,CACjD,UACF,CAMA,oBACE,YACF,CAEA,oDAEE,wBAAyB,CACzB,cAAwC,CAAxC,uCAAwC,CAGxC,mBAAqB,CADrB,gBAAiB,CAEjB,aAAc,CAHd,6BAIF,CACA,gEAEE,cACF,CACA,oDAGE,SAAU,CADV,UAAkD,CAAlD,gDAEF,CACA,gEAEE,cACF,CC/DA,mBACE,iBAAkB,CAClB,iBAAkB,CAElB,uBAA+B,CAD/B,sBAAyB,CAEzB,UACF,CACA,4CACE,SACF,CACA,6FAEE,sBACF,CAEA,4JAIE,UAA4C,CAA5C,0CAA4C,CAE5C,MAAO,CADP,QAAuC,CAAvC,qCAAuC,CAEvC,UACF,CAEA,mCAEE,WAAY,CADZ,eAEF,CACA,6DAEE,iBAAkB,CADlB,oBAEF,CAIA,6IACE,kBACF,CACA,yEACE,oBACF,CACA,8EACE,oBACF,CACA,yEACE,oBACF,CACA,8EACE,oBACF,CACA,0BAKE,eAAgE,CAAhE,8DAAgE,CADhE,iBAAiE,CAAjE,+DAAiE,CADjE,oBAAqB,CADrB,UAAyF,CAAzF,sFAAyF,CAIzF,UAA8D,CAA9D,2DAA8D,CAL9D,SAAuF,CAAvF,oFAMF,CACA,gCAKE,uBAAwB,CAChB,eAAgB,CALxB,WAAY,CAGZ,eAAgB,CAFhB,QAAS,CACT,SAIF,CACA,uDACE,cACF,CACA,qCACE,sBACF,CACA,iCAEE,oCAAqE,CAArE,mEAAqE,CADrE,SAAmD,CAAnD,iDAEF,CACA,kGAGE,SAAyC,CAAzC,uCAAyC,CADzC,SAA0C,CAA1C,wCAA0C,CAE1C,OAAQ,CACR,+BACF,CACA,sJAGE,aAAc,CADd,YAA2D,CAA3D,yDAEF,CACA,sKAEE,OAAQ,CACR,0BAA2B,CAC3B,SACF,CACA,0NAEE,oBAAqB,CACrB,gCAEF,CACA,0JAEE,YAA6D,CAA7D,2DACF,CACA,0KAEE,QAAS,CACT,0BAA2B,CAC3B,kBACF,CACA,8NAEE,iCAEF,CACA,2FACE,kCAEF,CAEA,4BACE,aAAuD,CAAvD,qDACF,CAEA,+BACE,oBAA8E,CAA9E,kEAA8E,CAC9E,iBACF,CACA,mEACE,oCAAqE,CAArE,mEAAqE,CAKrE,WAAY,CAHZ,MAAO,CADP,iBAAkB,CAElB,KAAM,CAGN,kBAAmB,CACnB,yBAA0B,CAH1B,UAIF,CACA,+EACE,0BACF,CACA,sSAKE,UAAsD,CAAtD,oDAAsD,CACtD,MAAO,CACP,KAAM,CAHN,UAIF,CACA,sSAKE,WAAY,CACZ,MAAO,CACP,KAAM,CAHN,SAAqD,CAArD,mDAIF,CACA,wBACE,YACF,CCvLA,4CACE,mCACF,CACA,2BACE,mBAAoB,CACpB,2BACF,CACA,yCACE,mBACF,CAIA,yFACE,mBACF,CCdA,aAUE,kBAAmB,CAGnB,gCAAkC,CANlC,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAA8B,CAM9B,cAAe,CAHf,YAAa,CAJb,YAAa,CAMb,sBAAuB,CARvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,WAAY,CAIZ,YAMF,CAEA,aAYE,iCAAmC,CAVnC,kCAA2B,CAA3B,0BAA2B,CAD3B,sDAAoF,CASpF,sBAA0C,CAP1C,kBAAmB,CAQnB,gCAA0C,CAF1C,cAAe,CAFf,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,YAAa,CASb,iBAAkB,CAPlB,UAQF,CAEA,UAGE,8BAA+B,CAF/B,cAAe,CACf,kBAEF,CAEA,WAIE,UAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CAFnB,eAAkB,CAClB,wBAEF,CAEA,wBATE,iCAgBF,CAPA,aAIE,eAA+B,CAF/B,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CACnB,eACF,CAEA,aACE,oBAA8B,CAI9B,0BAA0C,CAH1C,kBAAmB,CAEnB,kBAAmB,CADnB,YAGF,CAEA,gBAIE,UAAc,CAHd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,eAAkB,CAClB,eACF,CAEA,gBACE,eAAgB,CAEhB,QAAS,CADT,SAAU,CAEV,eACF,CAEA,gBAIE,WAA+B,CAH/B,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,iBAAkB,CAClB,iBAAkB,CAClB,iBACF,CAEA,uBAIE,UAAc,CAHd,WAAY,CAKZ,cAAe,CADf,eAAiB,CAFjB,MAAO,CADP,iBAKF,CAEA,aACE,oBAAoC,CAIpC,sBAA0C,CAH1C,kBAAmB,CAEnB,kBAAmB,CADnB,YAGF,CAEA,eAGE,eAA+B,CAD/B,cAAe,CAGf,iBAAkB,CADlB,QAEF,CAEA,8BAPE,iCAqBF,CAdA,eACE,kDAAqD,CACrD,WAAY,CACZ,kBAAmB,CAUnB,+BAA6C,CAR7C,UAAc,CAId,cAAe,CAFf,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CARnB,iBAAkB,CAOlB,wBAAyB,CADzB,uBAIF,CAEA,qBACE,kDAAqD,CAErD,+BAA6C,CAD7C,0BAEF,CAEA,sBACE,uBACF,CAGA,qBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,sBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,oBACE,MACE,kBACF,CACA,IACE,oBACF,CACF,CAGA,yBACE,aAEE,cAAe,CADf,iBAEF,CAEA,UACE,cAAe,CACf,kBACF,CAEA,WACE,cAAe,CACf,kBACF,CAEA,aACE,cAAe,CACf,kBACF,CAEA,aAEE,kBAAmB,CADnB,YAEF,CAEA,gBACE,cAAe,CACf,kBACF,CAEA,gBACE,cAAe,CACf,iBACF,CAEA,aAEE,kBAAmB,CADnB,YAEF,CAEA,eACE,cACF,CAEA,eAEE,cAAe,CADf,iBAEF,CACF,CAEA,yBACE,aAEE,cAAe,CADf,iBAEF,CAEA,UACE,cACF,CAEA,WACE,cACF,CAEA,aACE,cACF,CAEA,gBACE,cACF,CAEA,gBACE,cACF,CACF,CCvPA,iBAEI,oBAA8B,CAC9B,eAAgB,CAFhB,sBAGJ,CAEA,aAEI,kBAAmB,CAYnB,kCAA2B,CAA3B,0BAA2B,CAF3B,oBAAiC,CACjC,sBAA0C,CAF1C,kBAAmB,CAPnB,UAAc,CAHd,mBAAoB,CAKpB,iCAAqC,CAErC,cAAe,CADf,eAAgB,CAJhB,QAAS,CAOT,iBAAkB,CALlB,oBAAqB,CAIrB,uBAMJ,CAEA,mBACI,oBAAkC,CAClC,oBAAqB,CACrB,0BACJ,CAEA,YACI,cAAe,CACf,6BACJ,CAEA,+BACI,0BACJ,CAGA,UACI,eAAmB,CAInB,eAAgB,CAHhB,iBAAkB,CAElB,iBAAkB,CADlB,iBAGJ,CAEA,iBAOI,4HAC0F,CAF1F,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,SACJ,CAEA,kBAII,aAAc,CADd,gBAAiB,CAFjB,iBAAkB,CAClB,SAGJ,CAEA,kBAEI,kBAAmB,CADnB,YAAa,CAKb,cAAe,CAFf,QAAS,CADT,sBAAuB,CAEvB,kBAEJ,CAEA,mBAII,qBAAyB,CADzB,kBAAmB,CAEnB,gCAA6C,CAH7C,YAAa,CAIb,uBAAyB,CALzB,WAMJ,CAEA,yBAEI,oBAAqB,CACrB,gCAA8C,CAF9C,qBAGJ,CAEA,cACI,eACJ,CAEA,gBAII,UAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CADnB,wBAEJ,CAEA,mCATI,iCAAqC,CAIrC,eAWJ,CANA,mBAII,aAAc,CAFd,cAAe,CACf,eAGJ,CAEA,mBAII,YAAa,CAHb,iCAAqC,CACrC,cAAe,CAGf,0BAA2B,CAF3B,aAGJ,CAEA,qBAEI,kBAAmB,CAYnB,kCAA2B,CAA3B,0BAA2B,CAJ3B,kBAAmB,CANnB,YAAc,CAHd,mBAAoB,CAKpB,iCAAqC,CAErC,cAAe,CADf,eAAgB,CAJhB,OAAQ,CAUR,kBAAmB,CAGnB,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CAVlB,oBAAqB,CAOrB,wBAAyB,CADzB,uBAMJ,CAEA,4BACI,YAAa,CACb,cACJ,CAEA,2BAOI,uDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIJ,CAEA,2BACI,sDAAoF,CACpF,sBAAoC,CAGpC,gCAA+C,CAF/C,aAAc,CACd,sCAEJ,CAEA,iCACI,SACJ,CAEA,mBAII,WAA+B,CAH/B,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,QACJ,CAEA,aAKI,sBAA0C,CAD1C,kBAAmB,CAFnB,aAAc,CADd,eAAgB,CAEhB,YAGJ,CAEA,eAII,UAAc,CAHd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,QACJ,CAGA,aACI,iBACJ,CAEA,cAKI,aAAS,CACT,4HAC0F,CAJ1F,YAAa,CAEb,QAAS,CADT,yBAA0B,CAF1B,aAAc,CADd,gBAOJ,CAEA,cACI,kCAA2B,CAA3B,0BAA2B,CAG3B,sBAA0C,CAF1C,kBAAmB,CAGnB,gCAA6C,CAF7C,YAAa,CAGb,uBACJ,CAEA,oBAGI,sBAAoC,CADpC,gCAA6C,CAD7C,0BAGJ,CAEA,iBAII,aAAc,CAHd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,eAAkB,CAClB,wBACJ,CAEA,iBAII,UAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACJ,CAEA,iCAPI,iCAaJ,CANA,gBAII,eAA+B,CAF/B,cAAe,CACf,eAAgB,CAEhB,kBACJ,CAEA,iBACI,eAAgB,CAEhB,QAAS,CADT,SAEJ,CAEA,iBAII,eAA+B,CAH/B,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CACnB,iBAAkB,CAClB,iBACJ,CAEA,wBAII,aAAc,CAHd,WAAY,CAIZ,eAAiB,CAFjB,MAAO,CADP,iBAIJ,CAGA,aAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACJ,CAEA,mBACI,YAAc,CAEd,cAAe,CADf,kBAEJ,CAEA,YACI,YAAa,CACb,cAAe,CACf,QACJ,CAEA,WACI,iDAAqD,CAGrD,kBAAmB,CAFnB,UAAc,CAGd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAJhB,gBAAiB,CAKjB,uBACJ,CAEA,iBAEI,+BAA6C,CAD7C,0BAEJ,CAGA,sBAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACJ,CAEA,qBAGI,kCAA2B,CAA3B,0BAA2B,CAE3B,0BAAwC,CADxC,kBAAmB,CAGnB,+BAA4C,CAL5C,iBAAkB,CADlB,iBAAkB,CAKlB,uBAEJ,CAEA,2BAGI,sBAAoC,CADpC,gCAA6C,CAD7C,0BAGJ,CAEA,QAII,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBACJ,CAEA,4BAPI,iCAYJ,CALA,oBAGI,WAA+B,CAD/B,cAAe,CAEf,eACJ,CAGA,eAEI,sDAAkF,CADlF,iBAEJ,CAEA,kBAII,UAAc,CAHd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CADnB,iBAAkB,CAElB,wBACJ,CAEA,eAKI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAF3D,aAAc,CADd,gBAKJ,CAEA,cAEI,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAA8B,CAI9B,sBAA0C,CAF1C,kBAAmB,CACnB,eAAgB,CAEhB,uBACJ,CAEA,oBAEI,gCAA6C,CAD7C,2BAEJ,CAEA,eAEI,YAAa,CACb,eAAgB,CAFhB,UAGJ,CAEA,mBAEI,WAAY,CACZ,gBAAiB,CACjB,6BAA+B,CAH/B,UAIJ,CAEA,uCACI,qBACJ,CAEA,cAGI,2BAA4B,CAF5B,YAAa,CACb,uBAEJ,CAEA,oBACI,oBAAiC,CACjC,0BACJ,CAEA,iBAII,UAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,eACJ,CAEA,iCAPI,iCAaJ,CANA,gBAGI,WAA+B,CAD/B,cAAe,CAEf,eAAgB,CAChB,kBACJ,CAEA,cACI,YAAa,CACb,cAAe,CACf,QACJ,CAEA,mBACI,iDAAqD,CAGrD,kBAAmB,CAFnB,UAAc,CAGd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAJhB,gBAKJ,CAGA,cAGI,8BAA8C,CAF9C,eAAgB,CAIhB,SAAU,CAHV,gBAAiB,CAEjB,iBAAkB,CAElB,0BAA2B,CAC3B,uBACJ,CAEA,kCACI,SAAU,CACV,uBACJ,CAEA,mBAQI,kBAAmB,CAPnB,aAAc,CAMd,mBAAoB,CALpB,iCAAqC,CAErC,cAAe,CADf,eAAgB,CAMhB,OAAQ,CAHR,kBAAmB,CADnB,wBAKJ,CAEA,yBACI,YAAa,CACb,cAAe,CACf,6BACJ,CAEA,6CACI,yBACJ,CAGA,wCACI,0CACJ,CAEA,8CAGI,sBAAoC,CADpC,gCAA6C,CAD7C,uCAGJ,CAEA,+CACI,uCACJ,CAKA,yBACI,iBACI,iBACJ,CAEA,UACI,iBACJ,CAEA,kBACI,qBAAsB,CACtB,QACJ,CAEA,cACI,iBACJ,CAEA,gBACI,cACJ,CAEA,mBACI,cACJ,CAEA,qBAGI,kBAAmB,CAFnB,cAAe,CACf,iBAEJ,CAEA,4BACI,cACJ,CAEA,aACI,YACJ,CAEA,aACI,iBACJ,CAEA,cACI,YACJ,CAEA,iBACI,cACJ,CAEA,aAEI,QAAS,CADT,yBAEJ,CAEA,sBAEI,QAAS,CADT,wDAEJ,CAEA,QACI,cACJ,CAEA,eACI,iBACJ,CAEA,kBACI,cACJ,CAEA,eAEI,QAAS,CADT,yBAEJ,CACJ,CAEA,yBACI,gBACI,cACJ,CAEA,mBACI,cACJ,CAEA,qBAGI,kBAAmB,CAFnB,cAAe,CAGf,OAAQ,CAFR,gBAGJ,CAEA,4BACI,cACJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CAEA,cACI,YACJ,CAMA,qCACI,yBACJ,CACJ,CAGA,iBAEI,oBAAoC,CADpC,6BAEJ,CAEA,oBACI,uBACJ,CAEA,gBAEI,oBAAqC,CADrC,6BAEJ,CAEA,mBACI,uBACJ,CAEA,wBAEI,oBAAoC,CADpC,6BAEJ,CAEA,2BACI,uBACJ,CAEA,gBAEI,oBAAqC,CADrC,6BAEJ,CAEA,mBACI,uBACJ,CAEA,uBAEI,oBAAoC,CADpC,6BAEJ,CAEA,0BACI,uBACJ,CAEA,qBAEI,oBAAqC,CADrC,6BAEJ,CAEA,wBACI,uBACJ,CAEA,6BAEI,oBAAqC,CADrC,6BAEJ,CAEA,gCACI,uBACJ,CAEA,oBAEI,oBAAoC,CADpC,6BAEJ,CAEA,uBACI,uBACJ,CAGA,gBAII,oBAAqC,CAErC,0BAA0C,CAH1C,kBAAmB,CADnB,kBAAmB,CADnB,YAAa,CAIb,uBAEJ,CAEA,sBACI,oBAAqC,CAErC,4BAA0C,CAD1C,0BAEJ,CAGA,mCAGI,wBAAyB,CAFzB,qBAAsB,CACtB,UAAY,CAEZ,iBACJ,CAEA,uCAEI,oBAAqC,CADrC,0BAEJ,CAEA,6CACI,sBAAsC,CACtC,gCACJ,CAEA,4DAGI,kDAAqD,CAGrD,kBAAmB,CAFnB,UAAY,CAHZ,0BAA2B,CAC3B,oBAAqB,CAKrB,cAAe,CACf,eAAgB,CAGhB,mBAAqB,CAFrB,kBAAmB,CAJnB,eAAgB,CAKhB,wBAEJ,CCluBA,sBAKE,2BAA4B,CAD5B,eAAgB,CAHhB,iBAKF,CAEA,sCALE,WAAY,CADZ,UASF,CAEA,sBAGE,iBAEF,CAEA,oCAHE,cAAe,CAFf,WAAY,CADZ,UAYF,CANA,cAGE,gBAAiB,CACjB,6BAEF,CAEA,kCACE,qBACF,CAGA,sDAUE,kBAAmB,CAMnB,kCAA2B,CAA3B,0BAA2B,CAT3B,oBAA8B,CAU9B,sBAA0C,CAT1C,iBAAkB,CAIlB,cAAe,CAHf,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CAIvB,SAAU,CAbV,iBAAkB,CAClB,OAAQ,CACR,0BAA2B,CAU3B,uBAAyB,CATzB,UAAW,CAQX,UAKF,CAEA,2BACE,SACF,CAEA,2BACE,UACF,CAEA,8FAEE,SACF,CAEA,kEAEE,oBAAiC,CACjC,oBAAqB,CACrB,qCACF,CAEA,gEAEE,UAAc,CACd,cAAe,CACf,eAAiB,CACjB,aACF,CAGA,mCACE,WAAY,CACZ,QAAS,CACT,0BAA2B,CAC3B,UACF,CAEA,0CACE,oBAAoC,CAGpC,UAAW,CACX,YAAa,CAHb,SAAU,CAIV,uBAAyB,CAHzB,SAIF,CAEA,iDACE,kBAAmB,CAEnB,6BAA2C,CAD3C,oBAEF,CAGA,iBASE,kBAAmB,CAJnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAW9B,sBAA0C,CAT1C,kBAAmB,CAEnB,YAAa,CAEb,OAAQ,CAER,SAAU,CALV,gBAAiB,CANjB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAWT,2BAA4B,CAC5B,uBAAyB,CAHzB,UAKF,CAEA,qCACE,SAAU,CACV,uBACF,CAMA,YACE,UAAc,CACd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CADnB,wBAEF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,OAEF,CAEA,WAKE,oCAAqC,CAFrC,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAEA,wBACE,mBACF,CAEA,wBACE,mBACF,CAEA,0BACE,UACE,UAAY,CACZ,kBACF,CACA,IACE,SAAU,CACV,oBACF,CACF,CAGA,iBAUE,kBAAmB,CAHnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAW9B,sBAA0C,CAT1C,iBAAkB,CANlB,WAAY,CAgBZ,UAAc,CANd,cAAe,CAHf,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CAIvB,SAAU,CAdV,iBAAkB,CAElB,UAAW,CAaX,0BAA2B,CAF3B,uBAAyB,CAVzB,UAAW,CASX,UAMF,CAEA,qCACE,SAAU,CACV,uBACF,CAEA,uBACE,oBAAiC,CAEjC,+BAA4C,CAD5C,kCAEF,CAGA,kBAYE,yBAA2B,CAL3B,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA+B,CAD/B,YAAa,CAFb,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,WAAY,CAOZ,aAEF,CAEA,sCANE,kBAAmB,CADnB,YAAa,CAEb,sBAYF,CAPA,oBAGE,eAAgB,CADhB,cAAe,CADf,iBAMF,CAEA,kBAIE,kBAAmB,CACnB,gCAA0C,CAH1C,eAAgB,CADhB,cAAe,CAEf,kBAGF,CAEA,kBAUE,kBAAmB,CAJnB,oBAAkC,CAClC,WAAY,CACZ,iBAAkB,CAOlB,UAAc,CAHd,cAAe,CAHf,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CAVvB,iBAAkB,CAElB,WAAY,CADZ,SAAU,CAYV,uBAAyB,CAVzB,UAAW,CASX,cAGF,CAEA,wBACE,kBAAgC,CAEhC,+BAA6C,CAD7C,oBAEF,CAEA,uBAME,YAAa,CACb,6BAA8B,CAJ9B,MAAO,CAMP,cAAe,CADf,mBAAoB,CAPpB,iBAAkB,CAGlB,OAAQ,CAFR,OAAQ,CAGR,0BAKF,CAEA,oBAQE,kBAAmB,CAJnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAE9B,sBAA0C,CAC1C,iBAAkB,CAMlB,UAAc,CAFd,cAAe,CAHf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CAIvB,kBAAmB,CAFnB,uBAAyB,CAVzB,UAaF,CAEA,0BACE,oBAAiC,CAEjC,+BAA4C,CAD5C,oBAEF,CAEA,oBAME,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAE9B,sBAA0C,CAC1C,kBAAmB,CANnB,YAAa,CAQb,UAAc,CACd,iCAAqC,CACrC,cAAe,CACf,eAAgB,CAVhB,QAAS,CAMT,gBAAiB,CARjB,iBAAkB,CAGlB,0BAUF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAGA,yBACE,iBAGE,WAAY,CADZ,WAAY,CAEZ,UAAW,CAHX,UAIF,CAEA,kBAIE,WAAY,CAFZ,UAAW,CADX,SAAU,CAEV,UAEF,CAEA,uBACE,cACF,CAEA,oBAEE,WAAY,CADZ,UAEF,CAEA,oBACE,YAAa,CACb,cAAe,CACf,gBACF,CAEA,oBAEE,eAAgB,CADhB,cAEF,CAEA,sDAGE,WAAY,CADZ,UAEF,CAEA,2BACE,SACF,CAEA,2BACE,UACF,CAEA,gEAEE,cACF,CAEA,iBAGE,gBAAiB,CADjB,UAAW,CADX,QAGF,CAEA,YACE,cACF,CAEA,mCACE,WACF,CACF,CAEA,yBACE,iBAGE,UAAW,CADX,WAAY,CAEZ,SAAU,CAHV,UAIF,CAEA,kBAIE,WAAY,CAFZ,OAAQ,CADR,SAAU,CAEV,UAEF,CAEA,oBAEE,WAAY,CADZ,UAEF,CAEA,uBACE,cACF,CAEA,sDAGE,WAAY,CADZ,UAEF,CAEA,gEAEE,cACF,CAEA,0CAEE,UAAW,CACX,YAAa,CAFb,SAGF,CACF,CAGA,yCACE,wFAIE,SACF,CAEA,kCAEE,uBACF,CACF", "sources": ["index.css", "style.css", "../node_modules/swiper/swiper.css", "../node_modules/swiper/modules/navigation.css", "../node_modules/swiper/modules/pagination.css", "../node_modules/swiper/modules/effect-fade.css", "components/NDANotification.css", "job-detail.css", "components/ProjectImageSwiper.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", "/* Reset and Base Styles */\r\n* {\r\n    margin: 0;\r\n    padding: 0;\r\n    box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n    font-family: 'Montserrat', sans-serif;\r\n    background-color: black;\r\n    color: #FFFFFF;\r\n    line-height: 1.6;\r\n    overflow-x: hidden;\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n/* Header Styles */\r\nheader {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 20px 40px;\r\n    width: 100%;\r\n    top: 0;\r\n    z-index: 100;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    box-sizing: border-box;\r\n}\r\n\r\n\r\n\r\n/* Mobile devices (screen width <= 768px) - Header does NOT follow scroll, logo left, CV button right */\r\n@media (max-width: 768px) {\r\n    header {\r\n        position: relative; /* Scrolls with the page */\r\n        display: flex !important; /* Force flex display */\r\n        flex-direction: row !important; /* Force horizontal layout */\r\n        justify-content: space-between !important; /* Distribute items */\r\n        align-items: center !important; /* Vertically center items */\r\n        flex-wrap: nowrap !important; /* Prevent wrapping */\r\n        min-height: 60px; /* Ensure enough height */\r\n        padding: 10px 20px; /* Reduced padding for more space */\r\n        width: 100% !important; /* Ensure full width */\r\n        overflow: hidden; /* Prevent overflow issues */\r\n    }\r\n    .logo {\r\n        margin-right: auto; /* Pushes logo to the left */\r\n        display: flex !important; /* Ensure logo behaves as a flex item */\r\n        align-items: center !important; /* Center logo vertically */\r\n        max-width: 40%; /* Limit logo width to prevent overflow */\r\n    }\r\n    .logo-img {\r\n        max-width: 50px !important; /* Force logo size */\r\n        height: auto !important;\r\n    }\r\n    .cv-button {\r\n        margin-left: auto; /* Pushes CV button to the right */\r\n        white-space: nowrap !important; /* Prevent text wrapping */\r\n        max-width: 40%; /* Limit button width to prevent overflow */\r\n        padding: 10px 20px !important; /* Increased padding for better fit */\r\n        font-size: 14px !important; /* Ensure readable text size */\r\n        text-align: center; /* Center text in button */\r\n        display: inline-block; /* Ensure button behaves inline */\r\n        overflow: hidden; /* Prevent text overflow */\r\n        text-overflow: ellipsis; /* Add ellipsis if text overflows (optional) */\r\n        box-sizing: border-box !important; /* Include padding in width calculation */\r\n    }\r\n}\r\n\r\n/* Desktop devices (screen width > 768px) - Header follows scroll */\r\n@media (min-width: 769px) {\r\n    header {\r\n        position: fixed; /* Stays visible and follows the user */\r\n    }\r\n    body {\r\n        padding-top: 80px; /* Prevents content from hiding under the fixed header */\r\n    }\r\n}\r\n\r\n.logo img {\r\n    height: 76px;\r\n    width: 76px;\r\n    border-radius: 50%;\r\n    border: 3px solid #4B0082;\r\n    box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);\r\n    transition: transform 0.3s ease;\r\n    object-fit: cover;\r\n    object-position: center top;\r\n}\r\n\r\n.logo img:hover {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.cv-button {\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    text-decoration: none;\r\n    padding: 10px 20px;\r\n    border-radius: 25px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.cv-button:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n}\r\n\r\n/* Intro Section */\r\n.intro {\r\n    text-align: center;\r\n    padding: 100px 20px;\r\n    margin-top: 80px;\r\n}\r\n\r\n.intro h1 {\r\n    font-size: 48px;\r\n    font-weight: 700;\r\n    letter-spacing: 2px;\r\n}\r\n\r\n.intro .highlight {\r\n    color: #4B0082;\r\n}\r\n\r\n.intro p {\r\n    font-size: 18px;\r\n    margin: 20px 0;\r\n    text-transform: uppercase;\r\n}\r\n\r\n/* Combined Intro-Image and Crafting Section */\r\n.intro-crafting {\r\n    padding: 80px 20px;\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    grid-template-areas:\r\n        \"content image\";\r\n    align-items: center;\r\n    justify-items: center;\r\n    gap: 40px;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    width: 100%;\r\n}\r\n\r\n.crafting-content {\r\n    grid-area: content;\r\n    text-align: center;\r\n    max-width: 100%;\r\n}\r\n\r\n.crafting-content h2 {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.crafting-content p {\r\n    font-size: 16px;\r\n    max-width: 600px;\r\n    margin: 0 auto 40px;\r\n}\r\n\r\n.crafting-content .services-and-skills {\r\n    display: flex;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 40px;\r\n    max-width: 100%;\r\n    margin: 0 auto;\r\n}\r\n\r\n.services {\r\n    display: flex;\r\n    gap: 40px;\r\n}\r\n\r\n.service-item {\r\n    text-align: center;\r\n    max-width: 250px;\r\n}\r\n\r\n.service-item img {\r\n    height: 50px;\r\n    margin-bottom: 20px;\r\n    border-radius: 50%;\r\n    border: 2px solid #4B0082;\r\n}\r\n\r\n.service-item h3 {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.service-item p {\r\n    font-size: 14px;\r\n}\r\n\r\n.skills-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    font-size: 14px;\r\n    text-transform: uppercase;\r\n    color: #4B0082;\r\n    padding: 10px 0;\r\n}\r\n\r\n.intro-image-wrapper {\r\n    grid-area: image;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n    max-width: 100%;\r\n}\r\n\r\n.intro-image {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.intro-image img {\r\n    width: 100%;\r\n    max-width: 400px;\r\n    height: auto;\r\n    border-radius: 50%;\r\n    border: 3px solid #4B0082;\r\n    box-shadow: 0 4px 15px rgba(255, 45, 85, 0.3);\r\n    object-fit: cover;\r\n    object-position: center top; /* This will keep the top part (hair) visible */\r\n    aspect-ratio: 1/1; /* Forces a perfect circle */\r\n}\r\n\r\n.social-links {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 20px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.social-links img {\r\n    height: 30px;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.social-links img:hover {\r\n    transform: scale(1.1);\r\n}\r\n\r\n/* Responsive Design for Intro-Crafting Section */\r\n@media (max-width: 1024px) {\r\n    .intro-crafting {\r\n        grid-template-columns: 1fr 1fr;\r\n        padding: 60px 15px;\r\n        gap: 30px;\r\n    }\r\n    .intro-image img {\r\n        max-width: 350px;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n        aspect-ratio: 1/1;\r\n    }\r\n    .crafting-content h2 {\r\n        font-size: 32px;\r\n    }\r\n    .crafting-content p {\r\n        font-size: 15px;\r\n    }\r\n    .service-item img {\r\n        height: 45px;\r\n    }\r\n    .service-item h3 {\r\n        font-size: 16px;\r\n    }\r\n    .service-item p {\r\n        font-size: 13px;\r\n    }\r\n    .social-links img {\r\n        height: 28px;\r\n    }\r\n    .skills-bar {\r\n        font-size: 13px;\r\n        gap: 8px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .intro-crafting {\r\n        grid-template-columns: 1fr;\r\n        grid-template-areas:\r\n            \"image\"\r\n            \"content\"; /* Reordered to show image first */\r\n        padding: 40px 10px;\r\n        gap: 20px;\r\n    }\r\n    .intro-image img {\r\n        max-width: 300px;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n        aspect-ratio: 1/1;\r\n    }\r\n    .crafting-content h2 {\r\n        font-size: 28px;\r\n    }\r\n    .crafting-content p {\r\n        font-size: 14px;\r\n    }\r\n    .crafting-content .services-and-skills {\r\n        flex-direction: column;\r\n        align-items: center;\r\n    }\r\n    .services {\r\n        flex-direction: column;\r\n        gap: 20px;\r\n    }\r\n    .service-item {\r\n        max-width: 100%;\r\n    }\r\n    .service-item img {\r\n        height: 40px;\r\n    }\r\n    .service-item h3 {\r\n        font-size: 15px;\r\n    }\r\n    .service-item p {\r\n        font-size: 12px;\r\n    }\r\n    .social-links img {\r\n        height: 25px;\r\n    }\r\n    .skills-bar {\r\n        font-size: 12px;\r\n        gap: 6px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .intro-crafting {\r\n        padding: 30px 10px;\r\n        gap: 15px;\r\n    }\r\n    .intro-image img {\r\n        max-width: 250px;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n        aspect-ratio: 1/1;\r\n    }\r\n    .crafting-content h2 {\r\n        font-size: 24px;\r\n    }\r\n    .crafting-content p {\r\n        font-size: 13px;\r\n    }\r\n    .service-item img {\r\n        height: 35px;\r\n    }\r\n    .service-item h3 {\r\n        font-size: 14px;\r\n    }\r\n    .service-item p {\r\n        font-size: 11px;\r\n    }\r\n    .social-links img {\r\n        height: 20px;\r\n    }\r\n    .social-links {\r\n        gap: 15px;\r\n    }\r\n    .skills-bar {\r\n        font-size: 11px;\r\n        gap: 5px;\r\n        flex-wrap: wrap;\r\n    }\r\n}\r\n\r\n/* Experience Timeline Section */\r\n.experience {\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n    position: relative;\r\n}\r\n\r\n.experience h2 {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 60px;\r\n    color: #FFFFFF;\r\n}\r\n\r\n.timeline {\r\n    position: relative;\r\n    max-width: 1000px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.timeline::before {\r\n    content: '';\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 0;\r\n    bottom: 0;\r\n    width: 3px;\r\n    background: linear-gradient(180deg, #4B0082, #FF2D55, #4B0082);\r\n    transform: translateX(-50%);\r\n    z-index: 1;\r\n}\r\n\r\n.timeline-item {\r\n    position: relative;\r\n    margin: 60px 0;\r\n    opacity: 0;\r\n    animation: fadeInUp 0.8s ease forwards;\r\n}\r\n\r\n.timeline-item:nth-child(1) { animation-delay: 0.2s; }\r\n.timeline-item:nth-child(2) { animation-delay: 0.4s; }\r\n.timeline-item:nth-child(3) { animation-delay: 0.6s; }\r\n.timeline-item:nth-child(4) { animation-delay: 0.8s; }\r\n\r\n.timeline-item:nth-child(odd) .timeline-content {\r\n    margin-left: 60%;\r\n    text-align: left;\r\n}\r\n\r\n.timeline-item:nth-child(even) .timeline-content {\r\n    margin-right: 60%;\r\n    text-align: right;\r\n}\r\n\r\n.timeline-dot {\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 20px;\r\n    width: 20px;\r\n    height: 20px;\r\n    background: #4B0082;\r\n    border: 4px solid #FFFFFF;\r\n    border-radius: 50%;\r\n    transform: translateX(-50%);\r\n    z-index: 2;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.timeline-item:hover .timeline-dot {\r\n    background: #FF2D55;\r\n    transform: translateX(-50%) scale(1.3);\r\n    box-shadow: 0 0 20px rgba(255, 45, 85, 0.5);\r\n}\r\n\r\n.timeline-content {\r\n    backdrop-filter: blur(10px);\r\n    border-radius: 15px;\r\n    padding: 30px;\r\n    box-shadow: 0 8px 32px rgba(75, 0, 130, 0.2);\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n}\r\n\r\n.timeline-content:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);\r\n}\r\n\r\n/* Timeline Content Link Styles */\r\n.timeline-content-link {\r\n    text-decoration: none;\r\n    color: inherit;\r\n    display: block;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.timeline-content-link:hover {\r\n    text-decoration: none;\r\n    color: inherit;\r\n}\r\n\r\n.view-details {\r\n    margin-top: 15px;\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.view-details span {\r\n    color: #FF2D55;\r\n    font-family: 'Montserrat', sans-serif;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n    text-transform: uppercase;\r\n    letter-spacing: 1px;\r\n}\r\n\r\n.timeline-content-link:hover .view-details {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n}\r\n\r\n.timeline-content-link:hover .view-details span {\r\n    color: #FFFFFF;\r\n}\r\n\r\n.company-logo {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    border: 3px solid #4B0082;\r\n    margin-bottom: 20px;\r\n    object-fit: cover;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.timeline-item:hover .company-logo {\r\n    border-color: #FF2D55;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.job-title {\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    color: #4B0082;\r\n    text-transform: uppercase;\r\n    margin-bottom: 10px;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.timeline-item:hover .job-title {\r\n    color: #FF2D55;\r\n}\r\n\r\n.company-name {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    color: #FFFFFF;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.job-duration {\r\n    font-size: 14px;\r\n    color: #FF2D55;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n    font-weight: 600;\r\n}\r\n\r\n.job-description {\r\n    font-size: 14px;\r\n    line-height: 1.6;\r\n    color: rgba(255, 255, 255, 0.95);\r\n    font-weight: 400;\r\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(30px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Other Sections Remain Unchanged */\r\n.statistics {\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n}\r\n\r\n.statistics h2 {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 40px;\r\n}\r\n\r\n.stats-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\r\n    gap: 40px;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.stat h3 {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    color: #4B0082;\r\n}\r\n\r\n.stat p {\r\n    font-size: 14px;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.stats-image img {\r\n    width: 100%;\r\n    max-width: 600px;\r\n    height: auto;\r\n    margin: 40px 0;\r\n}\r\n\r\n.action-button {\r\n    display: inline-block;\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    text-decoration: none;\r\n    padding: 15px 30px;\r\n    border-radius: 25px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.action-button:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n}\r\n\r\n/* Portfolio Section */\r\n.portfolio {\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n}\r\n\r\n.portfolio h2 {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.discover-button {\r\n    display: inline-block;\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    text-decoration: none;\r\n    padding: 15px 30px;\r\n    border-radius: 25px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 40px;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.discover-button:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n}\r\n\r\n.portfolio-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: 40px;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.portfolio-item img {\r\n    width: 100%;\r\n    height: auto;\r\n}\r\n\r\n.portfolio-item p {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    margin: 10px 0;\r\n}\r\n\r\n.portfolio-item span {\r\n    font-size: 14px;\r\n    color: #4B0082;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.portfolio-item a {\r\n    display: block; /* Makes the entire item clickable */\r\n    text-decoration: none; /* Removes underline from link */\r\n    color: inherit; /* Inherits text color */\r\n}\r\n\r\n.portfolio-item a:hover {\r\n    opacity: 0.8; /* Optional: Add hover effect */\r\n}\r\n\r\n\r\n/* Client Thoughts Section */\r\n.client-thoughts {\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n}\r\n\r\n.quote-icon img {\r\n    height: 40px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.client-thoughts h2 {\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.client-thoughts h3 {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.client-thoughts p {\r\n    font-size: 16px;\r\n    max-width: 600px;\r\n    margin: 0 auto 20px;\r\n}\r\n\r\n.client-name {\r\n    font-size: 14px;\r\n    text-transform: uppercase;\r\n    color: #4B0082;\r\n}\r\n\r\n.client-logos {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 40px;\r\n    margin-top: 40px;\r\n}\r\n\r\n.client-logos img {\r\n    height: 50px;\r\n}\r\n\r\n.client-thoughts .quote-icon img {\r\n    width: 120px; /* Larger size, overriding inline style if needed */\r\n    height: auto;\r\n    transition: transform 0.3s ease; /* Smooth hover effect */\r\n    border-radius: 50%; /* Circular shape for a modern, attractive look */\r\n    box-shadow: 0 4px 8px rgba(255, 45, 85, 0.5); /* Pink shadow for depth */\r\n    margin-bottom: 20px; /* Space below the image */\r\n}\r\n\r\n.client-thoughts .quote-icon img:hover {\r\n    transform: scale(1.1); /* Slight zoom on hover for interactivity */\r\n}\r\n\r\n.client-thoughts .thoughts-image img {\r\n    box-shadow: 0 4px 8px rgba(255, 45, 85, 0.5);\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.client-thoughts .thoughts-image img:hover {\r\n    transform: scale(1.05);\r\n}\r\n\r\n/* Contact Section */\r\n.contact {\r\n    position: relative;\r\n    min-height: 100vh; /* Full viewport height */\r\n    padding: 80px 20px;\r\n    background: url('./contact01.png') no-repeat center center/contain; /* Background image for desktop */\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr; /* Two equal columns on desktop */\r\n    grid-template-areas:\r\n        \"overlay image\"; /* Define areas */\r\n    overflow: hidden;\r\n    align-items: center; /* Vertically center content */\r\n}\r\n\r\n.contact-overlay {\r\n    grid-area: overlay; /* Position on the left */\r\n    z-index: 1; /* Ensure content is above the background */\r\n    padding: 40px;\r\n    border-radius: 10px 0 0 10px; /* Rounded corners on the left side */\r\n    max-width: 500px; /* Limit width to ensure all content fits */\r\n    width: 100%;\r\n    text-align: center;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    background: rgba(0, 0, 0, 0.5); /* Optional: Semi-transparent background for contrast on desktop */\r\n}\r\n\r\n.contact h2 {\r\n    font-size: 48px; /* Bold, large title */\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    color: #FFFFFF;\r\n    margin-bottom: 40px;\r\n    letter-spacing: 2px;\r\n}\r\n\r\n.contact label {\r\n    display: block;\r\n    font-size: 12px; /* Smaller labels */\r\n    text-transform: uppercase;\r\n    color: #FFFFFF;\r\n    margin-bottom: 5px;\r\n    text-align: left;\r\n    opacity: 0.8;\r\n}\r\n\r\n.contact input,\r\n.contact textarea {\r\n    width: 100%;\r\n    padding: 12px;\r\n    margin-bottom: 20px;\r\n    background-color: rgba(255, 255, 255, 0.1); /* Transparent white */\r\n    border: 1px solid rgba(255, 255, 255, 0.2);\r\n    color: #FFFFFF;\r\n    font-size: 14px;\r\n    border-radius: 5px;\r\n    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);\r\n    transition: border-color 0.3s ease;\r\n}\r\n\r\n.contact input:focus,\r\n.contact textarea:focus {\r\n    border-color: #4B0082; /* Pink focus outline */\r\n    outline: none;\r\n}\r\n\r\n.contact textarea {\r\n    height: 120px; /* Adjusted height */\r\n    resize: none;\r\n}\r\n\r\n.contact input::placeholder,\r\n.contact textarea::placeholder {\r\n    color: #CCCCCC; /* Light gray placeholders */\r\n    opacity: 0.7;\r\n}\r\n\r\n.submit-button {\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    border: none;\r\n    padding: 0;\r\n    border-radius: 50%;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    cursor: pointer;\r\n    transition: background-color 0.3s ease, transform 0.3s ease;\r\n    width: 80px;\r\n    height: 80px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 20px auto 0;\r\n    font-size: 16px;\r\n}\r\n\r\n.submit-button:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n    transform: scale(1.1); /* Slight zoom */\r\n}\r\n\r\n/* Responsive Design for Contact Section */\r\n@media (max-width: 1024px) {\r\n    .contact {\r\n        grid-template-columns: 1fr 1fr; /* Keep two columns but adjust sizes */\r\n        padding: 60px 15px;\r\n    }\r\n    .contact-overlay {\r\n        max-width: 450px;\r\n        padding: 30px;\r\n    }\r\n    .contact h2 {\r\n        font-size: 40px;\r\n    }\r\n    .contact input,\r\n    .contact textarea {\r\n        padding: 10px;\r\n        font-size: 13px;\r\n    }\r\n    .submit-button {\r\n        width: 70px;\r\n        height: 70px;\r\n        font-size: 15px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .contact {\r\n        grid-template-columns: 1fr; /* Stack on smaller screens */\r\n        grid-template-areas:\r\n            \"overlay\"; /* Only overlay area */\r\n        padding: 40px 10px;\r\n        min-height: auto; /* Allow content to dictate height */\r\n        background: #1a1a1a; /* Solid background color for mobile (no image) */\r\n        background-image: none; /* Explicitly remove background image */\r\n    }\r\n    .contact-overlay {\r\n        max-width: 400px;\r\n        padding: 20px;\r\n        border-radius: 10px; /* Full border radius on mobile */\r\n        margin: 0 auto;\r\n        background: rgba(0, 0, 0, 0.8); /* Darker overlay for mobile */\r\n    }\r\n    .contact h2 {\r\n        font-size: 36px;\r\n    }\r\n    .contact label {\r\n        font-size: 11px;\r\n    }\r\n    .contact input,\r\n    .contact textarea {\r\n        padding: 8px;\r\n        font-size: 12px;\r\n    }\r\n    .contact textarea {\r\n        height: 100px;\r\n    }\r\n    .submit-button {\r\n        width: 60px;\r\n        height: 60px;\r\n        font-size: 14px;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .contact {\r\n        padding: 30px 10px;\r\n    }\r\n    .contact-overlay {\r\n        max-width: 300px;\r\n        padding: 15px;\r\n    }\r\n    .contact h2 {\r\n        font-size: 28px;\r\n        margin-bottom: 30px;\r\n    }\r\n    .contact label {\r\n        font-size: 10px;\r\n    }\r\n    .contact input,\r\n    .contact textarea {\r\n        padding: 6px;\r\n        font-size: 11px;\r\n        margin-bottom: 15px;\r\n    }\r\n    .contact textarea {\r\n        height: 80px;\r\n    }\r\n    .submit-button {\r\n        width: 50px;\r\n        height: 50px;\r\n        font-size: 12px;\r\n        margin-top: 15px;\r\n    }\r\n}\r\n\r\n/* Footer */\r\nfooter {\r\n    text-align: center;\r\n    padding: 20px;\r\n    font-size: 14px;\r\n}\r\n\r\n/* Back to Top Button */\r\n.back-to-top {\r\n    position: fixed;\r\n    bottom: 20px;\r\n    right: 20px;\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    font-size: 24px;\r\n    width: 50px;\r\n    height: 50px;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    border-radius: 50%;\r\n    text-decoration: none;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.back-to-top:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n}\r\n\r\n/* Experience Section Responsive Design */\r\n@media (max-width: 1024px) {\r\n    .experience {\r\n        padding: 60px 15px;\r\n    }\r\n\r\n    .timeline-item:nth-child(odd) .timeline-content {\r\n        margin-left: 55%;\r\n    }\r\n\r\n    .timeline-item:nth-child(even) .timeline-content {\r\n        margin-right: 55%;\r\n    }\r\n\r\n    .timeline-content {\r\n        padding: 25px;\r\n    }\r\n\r\n    .job-title {\r\n        font-size: 18px;\r\n    }\r\n\r\n    .company-name {\r\n        font-size: 16px;\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .experience {\r\n        padding: 40px 15px;\r\n    }\r\n\r\n    .experience h2 {\r\n        font-size: 28px;\r\n        margin-bottom: 40px;\r\n    }\r\n\r\n    /* Keep the centered timeline but make it more prominent */\r\n    .timeline::before {\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 4px;\r\n        background: linear-gradient(180deg, #4B0082, #FF2D55, #4B0082, #FF2D55);\r\n        box-shadow: 0 0 10px rgba(75, 0, 130, 0.3);\r\n    }\r\n\r\n    /* Create a zigzag pattern for mobile with wider cards */\r\n    .timeline-item:nth-child(odd) .timeline-content {\r\n        margin-left: 52%;\r\n        margin-right: 2%;\r\n        text-align: left;\r\n        position: relative;\r\n    }\r\n\r\n    .timeline-item:nth-child(even) .timeline-content {\r\n        margin-right: 52%;\r\n        margin-left: 2%;\r\n        text-align: right;\r\n        position: relative;\r\n    }\r\n\r\n    /* Add connecting lines from dots to cards */\r\n    .timeline-item:nth-child(odd) .timeline-content::before {\r\n        content: '';\r\n        position: absolute;\r\n        left: -18px;\r\n        top: 25px;\r\n        width: 18px;\r\n        height: 2px;\r\n        background: linear-gradient(90deg, #4B0082, #FF2D55);\r\n        border-radius: 2px;\r\n    }\r\n\r\n    .timeline-item:nth-child(even) .timeline-content::before {\r\n        content: '';\r\n        position: absolute;\r\n        right: -18px;\r\n        top: 25px;\r\n        width: 18px;\r\n        height: 2px;\r\n        background: linear-gradient(90deg, #FF2D55, #4B0082);\r\n        border-radius: 2px;\r\n    }\r\n\r\n    .timeline-dot {\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 16px;\r\n        height: 16px;\r\n        border: 3px solid #FFFFFF;\r\n        box-shadow: 0 0 10px rgba(75, 0, 130, 0.4);\r\n    }\r\n\r\n    .timeline-item:hover .timeline-dot {\r\n        transform: translateX(-50%) scale(1.4);\r\n        box-shadow: 0 0 20px rgba(255, 45, 85, 0.6);\r\n    }\r\n\r\n    .timeline-content {\r\n        padding: 30px 25px;\r\n        border-radius: 20px;\r\n        backdrop-filter: blur(15px);\r\n        border: 1px solid rgba(255, 255, 255, 0.25);\r\n        box-shadow:\r\n            0 8px 32px rgba(75, 0, 130, 0.25),\r\n            0 4px 16px rgba(255, 45, 85, 0.1);\r\n        min-height: 200px;\r\n    }\r\n\r\n    .timeline-content:hover {\r\n        transform: translateY(-8px) scale(1.02);\r\n        box-shadow:\r\n            0 20px 40px rgba(75, 0, 130, 0.35),\r\n            0 10px 25px rgba(255, 45, 85, 0.2);\r\n    }\r\n\r\n    /* Mobile timeline link styles */\r\n    .timeline-content-link:hover .timeline-content {\r\n        transform: translateY(-8px) scale(1.02);\r\n        box-shadow:\r\n            0 20px 40px rgba(75, 0, 130, 0.35),\r\n            0 10px 25px rgba(255, 45, 85, 0.2);\r\n    }\r\n\r\n    .company-logo {\r\n        width: 50px;\r\n        height: 50px;\r\n        margin-bottom: 15px;\r\n        border: 2px solid #4B0082;\r\n        box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);\r\n    }\r\n\r\n    .timeline-item:hover .company-logo {\r\n        border-color: #FF2D55;\r\n        transform: scale(1.15);\r\n        box-shadow: 0 6px 20px rgba(255, 45, 85, 0.4);\r\n    }\r\n\r\n    .job-title {\r\n        font-size: 16px;\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .company-name {\r\n        font-size: 15px;\r\n        margin-bottom: 8px;\r\n    }\r\n\r\n    .job-duration {\r\n        font-size: 12px;\r\n        margin-bottom: 15px;\r\n        font-weight: 700;\r\n    }\r\n\r\n    .job-description {\r\n        font-size: 13px;\r\n        line-height: 1.5;\r\n        color: rgba(255, 255, 255, 0.95);\r\n        font-weight: 400;\r\n        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .experience {\r\n        padding: 30px 10px;\r\n    }\r\n\r\n    .experience h2 {\r\n        font-size: 24px;\r\n        margin-bottom: 30px;\r\n    }\r\n\r\n    .timeline-item {\r\n        margin: 35px 0;\r\n    }\r\n\r\n    /* Maintain the alternating pattern with optimized margins for small screens */\r\n    .timeline-item:nth-child(odd) .timeline-content {\r\n        margin-left: 55%;\r\n        margin-right: 2%;\r\n        text-align: left;\r\n    }\r\n\r\n    .timeline-item:nth-child(even) .timeline-content {\r\n        margin-right: 55%;\r\n        margin-left: 2%;\r\n        text-align: right;\r\n    }\r\n\r\n    /* Adjust connecting lines for smaller screens */\r\n    .timeline-item:nth-child(odd) .timeline-content::before {\r\n        left: -25px;\r\n        width: 25px;\r\n        top: 20px;\r\n    }\r\n\r\n    .timeline-item:nth-child(even) .timeline-content::before {\r\n        right: -25px;\r\n        width: 25px;\r\n        top: 20px;\r\n    }\r\n\r\n    .timeline::before {\r\n        width: 3px;\r\n        box-shadow: 0 0 8px rgba(75, 0, 130, 0.4);\r\n    }\r\n\r\n    .timeline-dot {\r\n        width: 14px;\r\n        height: 14px;\r\n        border: 2px solid #FFFFFF;\r\n    }\r\n\r\n    .timeline-item:hover .timeline-dot {\r\n        transform: translateX(-50%) scale(1.3);\r\n    }\r\n\r\n    .timeline-content {\r\n        padding: 25px 20px;\r\n        border-radius: 15px;\r\n        min-height: 180px;\r\n    }\r\n\r\n    .timeline-content:hover {\r\n        transform: translateY(-6px) scale(1.01);\r\n    }\r\n\r\n    /* Small screen timeline link styles */\r\n    .timeline-content-link:hover .timeline-content {\r\n        transform: translateY(-6px) scale(1.01);\r\n    }\r\n\r\n    .company-logo {\r\n        width: 40px;\r\n        height: 40px;\r\n        margin-bottom: 10px;\r\n        border: 2px solid #4B0082;\r\n    }\r\n\r\n    .timeline-item:hover .company-logo {\r\n        transform: scale(1.1);\r\n    }\r\n\r\n    .job-title {\r\n        font-size: 14px;\r\n        margin-bottom: 6px;\r\n    }\r\n\r\n    .company-name {\r\n        font-size: 13px;\r\n        margin-bottom: 6px;\r\n    }\r\n\r\n    .job-duration {\r\n        font-size: 11px;\r\n        margin-bottom: 12px;\r\n        font-weight: 700;\r\n    }\r\n\r\n    .job-description {\r\n        font-size: 12px;\r\n        line-height: 1.4;\r\n        color: rgba(255, 255, 255, 0.95);\r\n        font-weight: 400;\r\n        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);\r\n    }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .intro h1 {\r\n        font-size: 36px;\r\n    }\r\n\r\n    .stats-grid {\r\n        grid-template-columns: repeat(2, 1fr);\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    header {\r\n        flex-direction: column;\r\n        gap: 20px;\r\n    }\r\n\r\n    .intro h1 {\r\n        font-size: 28px;\r\n    }\r\n\r\n    .stats-grid {\r\n        grid-template-columns: 1fr;\r\n    }\r\n}\r\n\r\n/* Portfolio Carousel Styles */\r\n.portfolio {\r\n    padding: 80px 20px;\r\n    text-align: center;\r\n}\r\n\r\n.portfolio h2 {\r\n    font-size: 36px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.discover-button {\r\n    display: inline-block;\r\n    background-color: #4B0082;\r\n    color: #FFFFFF;\r\n    text-decoration: none;\r\n    padding: 15px 30px;\r\n    border-radius: 25px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    margin-bottom: 40px;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n.discover-button:hover {\r\n    background-color: #FFFFFF;\r\n    color: #4B0082;\r\n}\r\n\r\n.portfolio-carousel {\r\n    width: 100%;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    overflow: hidden;\r\n    position: relative;\r\n    user-select: none; /* Prevents text selection while dragging */\r\n}\r\n\r\n.carousel-track {\r\n    display: flex;\r\n    width: 200%; /* Double width to accommodate duplicates */\r\n    overflow-x: hidden; /* Controlled by JS */\r\n    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */\r\n    cursor: grab;\r\n}\r\n\r\n.carousel-track.dragging {\r\n    cursor: grabbing;\r\n}\r\n\r\n.carousel-track .portfolio-item {\r\n    flex: 0 0 300px;\r\n    margin-right: 40px;\r\n    text-align: center;\r\n    min-width: 300px;\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n    border-radius: 15px;\r\n    padding: 15px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    backdrop-filter: blur(10px);\r\n    box-shadow:\r\n        0 8px 32px rgba(75, 0, 130, 0.2),\r\n        0 4px 16px rgba(255, 45, 85, 0.1),\r\n        inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.carousel-track .portfolio-item:hover {\r\n    transform: translateY(-10px) scale(1.02);\r\n    box-shadow:\r\n        0 20px 40px rgba(75, 0, 130, 0.3),\r\n        0 10px 25px rgba(255, 45, 85, 0.2),\r\n        0 0 20px rgba(75, 0, 130, 0.1),\r\n        inset 0 1px 0 rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.carousel-track .portfolio-item img {\r\n    width: 100%;\r\n    height: auto;\r\n    border-radius: 10px;\r\n    transition: transform 0.3s ease, filter 0.3s ease;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.carousel-track .portfolio-item:hover img {\r\n    transform: scale(1.05);\r\n    filter: brightness(1.1) saturate(1.1);\r\n}\r\n\r\n.carousel-track .portfolio-item p {\r\n    font-size: 18px;\r\n    font-weight: 700;\r\n    margin: 15px 0 10px;\r\n    text-transform: uppercase;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.carousel-track .portfolio-item:hover p {\r\n    color: #FF2D55;\r\n    text-shadow: 0 2px 4px rgba(255, 45, 85, 0.3);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .carousel-track {\r\n        width: 200%; /* Maintain double width for infinite loop */\r\n    }\r\n    .carousel-track .portfolio-item {\r\n        flex: 0 0 200px;\r\n        margin-right: 20px;\r\n        min-width: 200px;\r\n        padding: 10px;\r\n    }\r\n\r\n    .carousel-track .portfolio-item:hover {\r\n        transform: translateY(-5px) scale(1.01);\r\n    }\r\n\r\n    .carousel-track .portfolio-item p {\r\n        font-size: 14px;\r\n        margin: 10px 0 5px;\r\n    }\r\n}\r\n\r\n\r\n\r\n/* Skills Ticker Styles */\r\n.skills-ticker {\r\n    width: 100%;\r\n    background-color: #4B0082;\r\n    overflow: hidden;\r\n    padding: 15px 0;\r\n    transform: rotate(3deg);\r\n    transform-origin: center;\r\n    margin: 20px 0;\r\n    position: relative;\r\n}\r\n\r\n.ticker-track {\r\n    display: flex;\r\n    white-space: nowrap;\r\n    animation: tickerScroll 30s linear infinite;\r\n}\r\n\r\n.ticker-track span {\r\n    font-size: 14px;\r\n    font-weight: 700;\r\n    text-transform: uppercase;\r\n    color: #FFFFFF;\r\n    margin: 0 10px;\r\n    transform: rotate(-2deg);\r\n    display: inline-block;\r\n}\r\n\r\n/* Animation for scrolling from right to left */\r\n@keyframes tickerScroll {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n    100% {\r\n        transform: translateX(-50%);\r\n    }\r\n}\r\n\r\n/* Pause animation on hover (optional) */\r\n.ticker-track:hover {\r\n    animation-play-state: paused;\r\n}\r\n\r\n/* Responsive adjustments */\r\n@media (max-width: 768px) {\r\n    .skills-ticker {\r\n        transform: rotate(3deg);\r\n        padding: 10px 0;\r\n    }\r\n    .ticker-track span {\r\n        transform: rotate(-1deg);\r\n    }\r\n}", "/**\n * Swiper 11.2.8\n * Most modern mobile touch slider and framework with hardware accelerated transitions\n * https://swiperjs.com\n *\n * Copyright 2014-2025 Vladimir <PERSON>harlampidi\n *\n * Released under the MIT License\n *\n * Released on: May 23, 2025\n */\n\n/* FONT_START */\n@font-face {\n  font-family: 'swiper-icons';\n  src: url('data:application/font-woff;charset=utf-8;base64, 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');\n  font-weight: 400;\n  font-style: normal;\n}\n/* FONT_END */\n:root {\n  --swiper-theme-color: #007aff;\n  /*\n  --swiper-preloader-color: var(--swiper-theme-color);\n  --swiper-wrapper-transition-timing-function: initial;\n  */\n}\n:host {\n  position: relative;\n  display: block;\n  margin-left: auto;\n  margin-right: auto;\n  z-index: 1;\n}\n.swiper {\n  margin-left: auto;\n  margin-right: auto;\n  position: relative;\n  overflow: hidden;\n  list-style: none;\n  padding: 0;\n  /* Fix of Webkit flickering */\n  z-index: 1;\n  display: block;\n}\n.swiper-vertical > .swiper-wrapper {\n  flex-direction: column;\n}\n.swiper-wrapper {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  display: flex;\n  transition-property: transform;\n  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);\n  box-sizing: content-box;\n}\n.swiper-android .swiper-slide,\n.swiper-ios .swiper-slide,\n.swiper-wrapper {\n  transform: translate3d(0px, 0, 0);\n}\n.swiper-horizontal {\n  touch-action: pan-y;\n}\n.swiper-vertical {\n  touch-action: pan-x;\n}\n.swiper-slide {\n  flex-shrink: 0;\n  width: 100%;\n  height: 100%;\n  position: relative;\n  transition-property: transform;\n  display: block;\n}\n.swiper-slide-invisible-blank {\n  visibility: hidden;\n}\n/* Auto Height */\n.swiper-autoheight,\n.swiper-autoheight .swiper-slide {\n  height: auto;\n}\n.swiper-autoheight .swiper-wrapper {\n  align-items: flex-start;\n  transition-property: transform, height;\n}\n.swiper-backface-hidden .swiper-slide {\n  transform: translateZ(0);\n  -webkit-backface-visibility: hidden;\n          backface-visibility: hidden;\n}\n/* 3D Effects */\n.swiper-3d.swiper-css-mode .swiper-wrapper {\n  perspective: 1200px;\n}\n.swiper-3d .swiper-wrapper {\n  transform-style: preserve-3d;\n}\n.swiper-3d {\n  perspective: 1200px;\n}\n.swiper-3d .swiper-slide,\n.swiper-3d .swiper-cube-shadow {\n  transform-style: preserve-3d;\n}\n/* CSS Mode */\n.swiper-css-mode > .swiper-wrapper {\n  overflow: auto;\n  scrollbar-width: none;\n  /* For Firefox */\n  -ms-overflow-style: none;\n  /* For Internet Explorer and Edge */\n}\n.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {\n  display: none;\n}\n.swiper-css-mode > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: start start;\n}\n.swiper-css-mode.swiper-horizontal > .swiper-wrapper {\n  scroll-snap-type: x mandatory;\n}\n.swiper-css-mode.swiper-vertical > .swiper-wrapper {\n  scroll-snap-type: y mandatory;\n}\n.swiper-css-mode.swiper-free-mode > .swiper-wrapper {\n  scroll-snap-type: none;\n}\n.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: none;\n}\n.swiper-css-mode.swiper-centered > .swiper-wrapper::before {\n  content: '';\n  flex-shrink: 0;\n  order: 9999;\n}\n.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {\n  scroll-snap-align: center center;\n  scroll-snap-stop: always;\n}\n.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {\n  margin-inline-start: var(--swiper-centered-offset-before);\n}\n.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {\n  height: 100%;\n  min-height: 1px;\n  width: var(--swiper-centered-offset-after);\n}\n.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {\n  margin-block-start: var(--swiper-centered-offset-before);\n}\n.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {\n  width: 100%;\n  min-width: 1px;\n  height: var(--swiper-centered-offset-after);\n}\n/* Slide styles start */\n/* 3D Shadows */\n.swiper-3d .swiper-slide-shadow,\n.swiper-3d .swiper-slide-shadow-left,\n.swiper-3d .swiper-slide-shadow-right,\n.swiper-3d .swiper-slide-shadow-top,\n.swiper-3d .swiper-slide-shadow-bottom,\n.swiper-3d .swiper-slide-shadow,\n.swiper-3d .swiper-slide-shadow-left,\n.swiper-3d .swiper-slide-shadow-right,\n.swiper-3d .swiper-slide-shadow-top,\n.swiper-3d .swiper-slide-shadow-bottom {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 10;\n}\n.swiper-3d .swiper-slide-shadow {\n  background: rgba(0, 0, 0, 0.15);\n}\n.swiper-3d .swiper-slide-shadow-left {\n  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-right {\n  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-top {\n  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-3d .swiper-slide-shadow-bottom {\n  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));\n}\n.swiper-lazy-preloader {\n  width: 42px;\n  height: 42px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin-left: -21px;\n  margin-top: -21px;\n  z-index: 10;\n  transform-origin: 50%;\n  box-sizing: border-box;\n  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));\n  border-radius: 50%;\n  border-top-color: transparent;\n}\n.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,\n.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {\n  animation: swiper-preloader-spin 1s infinite linear;\n}\n.swiper-lazy-preloader-white {\n  --swiper-preloader-color: #fff;\n}\n.swiper-lazy-preloader-black {\n  --swiper-preloader-color: #000;\n}\n@keyframes swiper-preloader-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n/* Slide styles end */\n", ":root {\n  --swiper-navigation-size: 44px;\n  /*\n  --swiper-navigation-top-offset: 50%;\n  --swiper-navigation-sides-offset: 10px;\n  --swiper-navigation-color: var(--swiper-theme-color);\n  */\n}\n.swiper-button-prev,\n.swiper-button-next {\n  position: absolute;\n  top: var(--swiper-navigation-top-offset, 50%);\n  width: calc(var(--swiper-navigation-size) / 44 * 27);\n  height: var(--swiper-navigation-size);\n  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));\n  z-index: 10;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--swiper-navigation-color, var(--swiper-theme-color));\n}\n.swiper-button-prev.swiper-button-disabled,\n.swiper-button-next.swiper-button-disabled {\n  opacity: 0.35;\n  cursor: auto;\n  pointer-events: none;\n}\n.swiper-button-prev.swiper-button-hidden,\n.swiper-button-next.swiper-button-hidden {\n  opacity: 0;\n  cursor: auto;\n  pointer-events: none;\n}\n.swiper-navigation-disabled .swiper-button-prev,\n.swiper-navigation-disabled .swiper-button-next {\n  display: none !important;\n}\n.swiper-button-prev svg,\n.swiper-button-next svg {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  transform-origin: center;\n}\n.swiper-rtl .swiper-button-prev svg,\n.swiper-rtl .swiper-button-next svg {\n  transform: rotate(180deg);\n}\n.swiper-button-prev,\n.swiper-rtl .swiper-button-next {\n  left: var(--swiper-navigation-sides-offset, 10px);\n  right: auto;\n}\n.swiper-button-next,\n.swiper-rtl .swiper-button-prev {\n  right: var(--swiper-navigation-sides-offset, 10px);\n  left: auto;\n}\n.swiper-button-lock {\n  display: none;\n}\n/* Navigation font start */\n.swiper-button-prev:after,\n.swiper-button-next:after {\n  font-family: swiper-icons;\n  font-size: var(--swiper-navigation-size);\n  text-transform: none !important;\n  letter-spacing: 0;\n  font-variant: initial;\n  line-height: 1;\n}\n.swiper-button-prev:after,\n.swiper-rtl .swiper-button-next:after {\n  content: 'prev';\n}\n.swiper-button-next,\n.swiper-rtl .swiper-button-prev {\n  right: var(--swiper-navigation-sides-offset, 10px);\n  left: auto;\n}\n.swiper-button-next:after,\n.swiper-rtl .swiper-button-prev:after {\n  content: 'next';\n}\n/* Navigation font end */\n", ":root {\n  /*\n  --swiper-pagination-color: var(--swiper-theme-color);\n  --swiper-pagination-left: auto;\n  --swiper-pagination-right: 8px;\n  --swiper-pagination-bottom: 8px;\n  --swiper-pagination-top: auto;\n  --swiper-pagination-fraction-color: inherit;\n  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);\n  --swiper-pagination-progressbar-size: 4px;\n  --swiper-pagination-bullet-size: 8px;\n  --swiper-pagination-bullet-width: 8px;\n  --swiper-pagination-bullet-height: 8px;\n  --swiper-pagination-bullet-border-radius: 50%;\n  --swiper-pagination-bullet-inactive-color: #000;\n  --swiper-pagination-bullet-inactive-opacity: 0.2;\n  --swiper-pagination-bullet-opacity: 1;\n  --swiper-pagination-bullet-horizontal-gap: 4px;\n  --swiper-pagination-bullet-vertical-gap: 6px;\n  */\n}\n.swiper-pagination {\n  position: absolute;\n  text-align: center;\n  transition: 300ms opacity;\n  transform: translate3d(0, 0, 0);\n  z-index: 10;\n}\n.swiper-pagination.swiper-pagination-hidden {\n  opacity: 0;\n}\n.swiper-pagination-disabled > .swiper-pagination,\n.swiper-pagination.swiper-pagination-disabled {\n  display: none !important;\n}\n/* Common Styles */\n.swiper-pagination-fraction,\n.swiper-pagination-custom,\n.swiper-horizontal > .swiper-pagination-bullets,\n.swiper-pagination-bullets.swiper-pagination-horizontal {\n  bottom: var(--swiper-pagination-bottom, 8px);\n  top: var(--swiper-pagination-top, auto);\n  left: 0;\n  width: 100%;\n}\n/* Bullets */\n.swiper-pagination-bullets-dynamic {\n  overflow: hidden;\n  font-size: 0;\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {\n  transform: scale(0.33);\n  position: relative;\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {\n  transform: scale(1);\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {\n  transform: scale(1);\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {\n  transform: scale(0.66);\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {\n  transform: scale(0.33);\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {\n  transform: scale(0.66);\n}\n.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {\n  transform: scale(0.33);\n}\n.swiper-pagination-bullet {\n  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));\n  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));\n  display: inline-block;\n  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);\n  background: var(--swiper-pagination-bullet-inactive-color, #000);\n  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);\n}\nbutton.swiper-pagination-bullet {\n  border: none;\n  margin: 0;\n  padding: 0;\n  box-shadow: none;\n  -webkit-appearance: none;\n          appearance: none;\n}\n.swiper-pagination-clickable .swiper-pagination-bullet {\n  cursor: pointer;\n}\n.swiper-pagination-bullet:only-child {\n  display: none !important;\n}\n.swiper-pagination-bullet-active {\n  opacity: var(--swiper-pagination-bullet-opacity, 1);\n  background: var(--swiper-pagination-color, var(--swiper-theme-color));\n}\n.swiper-vertical > .swiper-pagination-bullets,\n.swiper-pagination-vertical.swiper-pagination-bullets {\n  right: var(--swiper-pagination-right, 8px);\n  left: var(--swiper-pagination-left, auto);\n  top: 50%;\n  transform: translate3d(0px, -50%, 0);\n}\n.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,\n.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {\n  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;\n  display: block;\n}\n.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,\n.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {\n  top: 50%;\n  transform: translateY(-50%);\n  width: 8px;\n}\n.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,\n.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {\n  display: inline-block;\n  transition: 200ms transform,\n        200ms top;\n}\n.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,\n.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {\n  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);\n}\n.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,\n.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {\n  left: 50%;\n  transform: translateX(-50%);\n  white-space: nowrap;\n}\n.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,\n.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {\n  transition: 200ms transform,\n        200ms left;\n}\n.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {\n  transition: 200ms transform,\n    200ms right;\n}\n/* Fraction */\n.swiper-pagination-fraction {\n  color: var(--swiper-pagination-fraction-color, inherit);\n}\n/* Progress */\n.swiper-pagination-progressbar {\n  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));\n  position: absolute;\n}\n.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {\n  background: var(--swiper-pagination-color, var(--swiper-theme-color));\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n  transform: scale(0);\n  transform-origin: left top;\n}\n.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {\n  transform-origin: right top;\n}\n.swiper-horizontal > .swiper-pagination-progressbar,\n.swiper-pagination-progressbar.swiper-pagination-horizontal,\n.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,\n.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {\n  width: 100%;\n  height: var(--swiper-pagination-progressbar-size, 4px);\n  left: 0;\n  top: 0;\n}\n.swiper-vertical > .swiper-pagination-progressbar,\n.swiper-pagination-progressbar.swiper-pagination-vertical,\n.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,\n.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {\n  width: var(--swiper-pagination-progressbar-size, 4px);\n  height: 100%;\n  left: 0;\n  top: 0;\n}\n.swiper-pagination-lock {\n  display: none;\n}\n", ".swiper-fade.swiper-free-mode .swiper-slide {\n  transition-timing-function: ease-out;\n}\n.swiper-fade .swiper-slide {\n  pointer-events: none;\n  transition-property: opacity;\n}\n.swiper-fade .swiper-slide .swiper-slide {\n  pointer-events: none;\n}\n.swiper-fade .swiper-slide-active {\n  pointer-events: auto;\n}\n.swiper-fade .swiper-slide-active .swiper-slide-active {\n  pointer-events: auto;\n}\n", "/* NDA Notification Styles */\n.nda-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(10px);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  animation: ndaFadeIn 0.3s ease-out;\n}\n\n.nda-content {\n  background: linear-gradient(135deg, rgba(75, 0, 130, 0.95), rgba(255, 45, 85, 0.95));\n  backdrop-filter: blur(20px);\n  border-radius: 20px;\n  padding: 40px;\n  max-width: 500px;\n  width: 90vw;\n  max-height: 80vh;\n  overflow-y: auto;\n  cursor: default;\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);\n  animation: ndaSlideIn 0.4s ease-out;\n  text-align: center;\n}\n\n.nda-icon {\n  font-size: 60px;\n  margin-bottom: 20px;\n  animation: ndaPulse 2s infinite;\n}\n\n.nda-title {\n  font-family: 'Montserrat', sans-serif;\n  font-size: 28px;\n  font-weight: 700;\n  color: #FFFFFF;\n  margin: 0 0 20px 0;\n  text-transform: uppercase;\n  letter-spacing: 2px;\n}\n\n.nda-message {\n  font-family: 'Montserrat', sans-serif;\n  font-size: 16px;\n  line-height: 1.6;\n  color: rgba(255, 255, 255, 0.9);\n  margin-bottom: 25px;\n  text-align: left;\n}\n\n.nda-details {\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 15px;\n  padding: 20px;\n  margin-bottom: 25px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.nda-details h3 {\n  font-family: 'Montserrat', sans-serif;\n  font-size: 18px;\n  font-weight: 600;\n  color: #FFFFFF;\n  margin: 0 0 15px 0;\n  text-align: left;\n}\n\n.nda-details ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n  text-align: left;\n}\n\n.nda-details li {\n  font-family: 'Montserrat', sans-serif;\n  font-size: 14px;\n  line-height: 1.5;\n  color: rgba(255, 255, 255, 0.8);\n  margin-bottom: 8px;\n  padding-left: 20px;\n  position: relative;\n}\n\n.nda-details li::before {\n  content: '✓';\n  position: absolute;\n  left: 0;\n  color: #00FF88;\n  font-weight: bold;\n  font-size: 16px;\n}\n\n.nda-contact {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 15px;\n  margin-bottom: 25px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.nda-contact p {\n  font-family: 'Montserrat', sans-serif;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.9);\n  margin: 0;\n  font-style: italic;\n}\n\n.nda-close-btn {\n  background: linear-gradient(135deg, #FF2D55, #FF6B6B);\n  border: none;\n  border-radius: 25px;\n  padding: 12px 30px;\n  color: #FFFFFF;\n  font-family: 'Montserrat', sans-serif;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  box-shadow: 0 5px 15px rgba(255, 45, 85, 0.3);\n}\n\n.nda-close-btn:hover {\n  background: linear-gradient(135deg, #FF6B6B, #FF2D55);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(255, 45, 85, 0.4);\n}\n\n.nda-close-btn:active {\n  transform: translateY(0);\n}\n\n/* Animations */\n@keyframes ndaFadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes ndaSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-30px) scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes ndaPulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n\n/* Mobile Responsiveness */\n@media (max-width: 768px) {\n  .nda-content {\n    padding: 30px 25px;\n    max-width: 95vw;\n  }\n  \n  .nda-icon {\n    font-size: 50px;\n    margin-bottom: 15px;\n  }\n  \n  .nda-title {\n    font-size: 24px;\n    margin-bottom: 15px;\n  }\n  \n  .nda-message {\n    font-size: 15px;\n    margin-bottom: 20px;\n  }\n  \n  .nda-details {\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n  \n  .nda-details h3 {\n    font-size: 16px;\n    margin-bottom: 12px;\n  }\n  \n  .nda-details li {\n    font-size: 13px;\n    margin-bottom: 6px;\n  }\n  \n  .nda-contact {\n    padding: 12px;\n    margin-bottom: 20px;\n  }\n  \n  .nda-contact p {\n    font-size: 13px;\n  }\n  \n  .nda-close-btn {\n    padding: 10px 25px;\n    font-size: 14px;\n  }\n}\n\n@media (max-width: 480px) {\n  .nda-content {\n    padding: 25px 20px;\n    max-width: 98vw;\n  }\n  \n  .nda-icon {\n    font-size: 40px;\n  }\n  \n  .nda-title {\n    font-size: 20px;\n  }\n  \n  .nda-message {\n    font-size: 14px;\n  }\n  \n  .nda-details h3 {\n    font-size: 15px;\n  }\n  \n  .nda-details li {\n    font-size: 12px;\n  }\n}\n", "/* Job Detail Pages Styles */\n\n/* Back Navigation */\n.back-navigation {\n    padding: 40px 40px 20px 40px;\n    background: rgba(0, 0, 0, 0.9);\n    margin-top: 20px;\n}\n\n.back-button {\n    display: inline-flex;\n    align-items: center;\n    gap: 10px;\n    color: #FFFFFF;\n    text-decoration: none;\n    font-family: 'Montserrat', sans-serif;\n    font-weight: 400;\n    font-size: 16px;\n    transition: all 0.3s ease;\n    padding: 10px 20px;\n    border-radius: 25px;\n    background: rgba(75, 0, 130, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    backdrop-filter: blur(10px);\n}\n\n.back-button:hover {\n    background: rgba(255, 45, 85, 0.2);\n    border-color: #FF2D55;\n    transform: translateX(-5px);\n}\n\n.back-arrow {\n    font-size: 18px;\n    transition: transform 0.3s ease;\n}\n\n.back-button:hover .back-arrow {\n    transform: translateX(-3px);\n}\n\n/* Job Hero Section */\n.job-hero {\n    background: #000000;\n    padding: 80px 40px;\n    text-align: center;\n    position: relative;\n    overflow: hidden;\n}\n\n.job-hero::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: radial-gradient(circle at 30% 70%, rgba(75, 0, 130, 0.3) 0%, transparent 50%),\n                radial-gradient(circle at 70% 30%, rgba(255, 45, 85, 0.3) 0%, transparent 50%);\n    z-index: 1;\n}\n\n.job-hero-content {\n    position: relative;\n    z-index: 2;\n    max-width: 1000px;\n    margin: 0 auto;\n}\n\n.company-branding {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 30px;\n    margin-bottom: 40px;\n    flex-wrap: wrap;\n}\n\n.hero-company-logo {\n    width: 120px;\n    height: 120px;\n    border-radius: 20px;\n    border: 3px solid #FFFFFF;\n    box-shadow: 0 10px 30px rgba(75, 0, 130, 0.3);\n    transition: all 0.3s ease;\n}\n\n.hero-company-logo:hover {\n    transform: scale(1.05);\n    border-color: #FF2D55;\n    box-shadow: 0 15px 40px rgba(255, 45, 85, 0.4);\n}\n\n.company-info {\n    text-align: left;\n}\n\n.job-title-hero {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 48px;\n    font-weight: 700;\n    color: #FFFFFF;\n    margin: 0 0 10px 0;\n    text-transform: uppercase;\n    letter-spacing: 2px;\n}\n\n.company-name-hero {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 24px;\n    font-weight: 400;\n    color: #FF2D55;\n    margin: 0 0 10px 0;\n}\n\n.company-link-hero {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 16px;\n    margin: 15px 0;\n    display: flex;\n    justify-content: flex-start;\n}\n\n.company-link-hero a {\n    display: inline-flex;\n    align-items: center;\n    gap: 8px;\n    color: #4B0082;\n    text-decoration: none;\n    font-family: 'Montserrat', sans-serif;\n    font-weight: 700;\n    font-size: 14px;\n    padding: 12px 24px;\n    border-radius: 25px;\n    transition: all 0.3s ease;\n    text-transform: uppercase;\n    letter-spacing: 1px;\n    backdrop-filter: blur(15px);\n    position: relative;\n    overflow: hidden;\n}\n\n.company-link-hero a::before {\n    content: \"🌐\";\n    font-size: 16px;\n}\n\n.company-link-hero a::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n    transition: left 0.5s ease;\n}\n\n.company-link-hero a:hover {\n    background: linear-gradient(135deg, rgba(75, 0, 130, 0.25), rgba(255, 45, 85, 0.25));\n    border-color: rgba(255, 45, 85, 0.6);\n    color: #FF2D55;\n    transform: translateY(-3px) scale(1.02);\n    box-shadow: 0 12px 35px rgba(255, 45, 85, 0.25);\n}\n\n.company-link-hero a:hover::after {\n    left: 100%;\n}\n\n.job-duration-hero {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 18px;\n    font-weight: 300;\n    color: rgba(255, 255, 255, 0.8);\n    margin: 0;\n}\n\n.job-summary {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 30px;\n    border-radius: 20px;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.job-summary p {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 18px;\n    line-height: 1.6;\n    color: #FFFFFF;\n    margin: 0;\n}\n\n/* Job Content Section */\n.job-content {\n    padding: 80px 40px;\n}\n\n.content-grid {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: grid;\n    grid-template-columns: 1fr;\n    gap: 40px;\n    background: radial-gradient(circle at 30% 70%, rgba(75, 0, 130, 0.3) 0%, transparent 50%),\n                radial-gradient(circle at 70% 30%, rgba(255, 45, 85, 0.3) 0%, transparent 50%);\n}\n\n.content-card {\n    backdrop-filter: blur(15px);\n    border-radius: 20px;\n    padding: 40px;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    box-shadow: 0 10px 30px rgba(75, 0, 130, 0.2);\n    transition: all 0.3s ease;\n}\n\n.content-card:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);\n    border-color: rgba(255, 45, 85, 0.3);\n}\n\n.content-card h3 {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 28px;\n    font-weight: 700;\n    color: #FF2D55;\n    margin: 0 0 20px 0;\n    text-transform: uppercase;\n}\n\n.content-card h4 {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 20px;\n    font-weight: 600;\n    color: #FFFFFF;\n    margin: 30px 0 15px 0;\n}\n\n.content-card p {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 16px;\n    line-height: 1.6;\n    color: rgba(255, 255, 255, 0.9);\n    margin-bottom: 20px;\n}\n\n.content-card ul {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n}\n\n.content-card li {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 16px;\n    line-height: 1.6;\n    color: rgba(255, 255, 255, 0.9);\n    margin-bottom: 10px;\n    padding-left: 20px;\n    position: relative;\n}\n\n.content-card li::before {\n    content: '→';\n    position: absolute;\n    left: 0;\n    color: #FF2D55;\n    font-weight: bold;\n}\n\n/* Skills Grid */\n.skills-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n    gap: 30px;\n    margin-top: 20px;\n}\n\n.skill-category h4 {\n    color: #4B0082;\n    margin-bottom: 15px;\n    font-size: 18px;\n}\n\n.skill-tags {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.skill-tag {\n    background: linear-gradient(135deg, #4B0082, #FF2D55);\n    color: #FFFFFF;\n    padding: 8px 16px;\n    border-radius: 20px;\n    font-family: 'Montserrat', sans-serif;\n    font-size: 14px;\n    font-weight: 500;\n    transition: all 0.3s ease;\n}\n\n.skill-tag:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(255, 45, 85, 0.3);\n}\n\n/* Accomplishments */\n.accomplishments-list {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 30px;\n    margin-top: 20px;\n}\n\n.accomplishment-item {\n    text-align: center;\n    padding: 30px 20px;\n    backdrop-filter: blur(15px);\n    border-radius: 15px;\n    border: 1px solid rgba(255, 45, 85, 0.2);\n    transition: all 0.3s ease;\n    box-shadow: 0 8px 32px rgba(75, 0, 130, 0.2);\n}\n\n.accomplishment-item:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);\n    border-color: rgba(255, 45, 85, 0.4);\n}\n\n.metric {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 48px;\n    font-weight: 700;\n    color: #FF2D55;\n    margin-bottom: 10px;\n}\n\n.metric-description {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 14px;\n    color: rgba(255, 255, 255, 0.8);\n    line-height: 1.4;\n}\n\n/* Role Projects Section */\n.role-projects {\n    padding: 80px 40px;\n    background: linear-gradient(135deg, rgba(75, 0, 130, 0.1), rgba(255, 45, 85, 0.1));\n}\n\n.role-projects h2 {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 36px;\n    font-weight: 700;\n    color: #FFFFFF;\n    text-align: center;\n    margin-bottom: 60px;\n    text-transform: uppercase;\n}\n\n.projects-grid {\n    max-width: 1200px;\n    margin: 0 auto;\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n    gap: 40px;\n}\n\n.project-card {\n    background: rgba(0, 0, 0, 0.8);\n    backdrop-filter: blur(15px);\n    border-radius: 20px;\n    overflow: hidden;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    transition: all 0.3s ease;\n}\n\n.project-card:hover {\n    transform: translateY(-10px);\n    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.3);\n}\n\n.project-image {\n    width: 100%;\n    height: 250px;\n    overflow: hidden;\n}\n\n.project-image img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: transform 0.3s ease;\n}\n\n.project-card:hover .project-image img {\n    transform: scale(1.05);\n}\n\n.project-info {\n    padding: 30px;\n    transition: all 0.3s ease;\n    border-radius: 0 0 20px 20px;\n}\n\n.project-info:hover {\n    background: rgba(75, 0, 130, 0.1);\n    transform: translateY(-2px);\n}\n\n.project-info h3 {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 22px;\n    font-weight: 600;\n    color: #FFFFFF;\n    margin: 0 0 15px 0;\n}\n\n.project-info p {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 16px;\n    color: rgba(255, 255, 255, 0.8);\n    line-height: 1.5;\n    margin-bottom: 20px;\n}\n\n.project-tech {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 10px;\n}\n\n.project-tech span {\n    background: linear-gradient(135deg, #4B0082, #FF2D55);\n    color: #FFFFFF;\n    padding: 5px 12px;\n    border-radius: 15px;\n    font-family: 'Montserrat', sans-serif;\n    font-size: 12px;\n    font-weight: 500;\n}\n\n/* Project Link Indicator */\n.project-link {\n    margin-top: 20px;\n    padding-top: 15px;\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\n    text-align: center;\n    opacity: 0;\n    transform: translateY(10px);\n    transition: all 0.3s ease;\n}\n\n.project-card:hover .project-link {\n    opacity: 1;\n    transform: translateY(0);\n}\n\n.project-link span {\n    color: #FF2D55;\n    font-family: 'Montserrat', sans-serif;\n    font-weight: 600;\n    font-size: 14px;\n    text-transform: uppercase;\n    letter-spacing: 1px;\n    display: inline-flex;\n    align-items: center;\n    gap: 8px;\n}\n\n.project-link span::after {\n    content: '🚀';\n    font-size: 16px;\n    transition: transform 0.3s ease;\n}\n\n.project-card:hover .project-link span::after {\n    transform: translateX(3px);\n}\n\n/* Enhanced clickable project card styles */\n.project-card[style*=\"cursor: pointer\"] {\n    transition: all 0.3s ease, transform 0.2s ease;\n}\n\n.project-card[style*=\"cursor: pointer\"]:hover {\n    transform: translateY(-15px) scale(1.02);\n    box-shadow: 0 25px 50px rgba(75, 0, 130, 0.4);\n    border-color: rgba(255, 45, 85, 0.5);\n}\n\n.project-card[style*=\"cursor: pointer\"]:active {\n    transform: translateY(-12px) scale(1.01);\n}\n\n\n\n/* Mobile Responsiveness */\n@media (max-width: 768px) {\n    .back-navigation {\n        padding: 15px 20px;\n    }\n\n    .job-hero {\n        padding: 60px 20px;\n    }\n\n    .company-branding {\n        flex-direction: column;\n        gap: 20px;\n    }\n\n    .company-info {\n        text-align: center;\n    }\n\n    .job-title-hero {\n        font-size: 32px;\n    }\n\n    .company-name-hero {\n        font-size: 20px;\n    }\n\n    .company-link-hero a {\n        font-size: 12px;\n        padding: 10px 20px;\n        border-radius: 20px;\n    }\n\n    .company-link-hero a::before {\n        font-size: 14px;\n    }\n\n    .job-summary {\n        padding: 20px;\n    }\n\n    .job-content {\n        padding: 60px 20px;\n    }\n\n    .content-card {\n        padding: 25px;\n    }\n\n    .content-card h3 {\n        font-size: 24px;\n    }\n\n    .skills-grid {\n        grid-template-columns: 1fr;\n        gap: 20px;\n    }\n\n    .accomplishments-list {\n        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n        gap: 20px;\n    }\n\n    .metric {\n        font-size: 36px;\n    }\n\n    .role-projects {\n        padding: 60px 20px;\n    }\n\n    .role-projects h2 {\n        font-size: 28px;\n    }\n\n    .projects-grid {\n        grid-template-columns: 1fr;\n        gap: 30px;\n    }\n}\n\n@media (max-width: 480px) {\n    .job-title-hero {\n        font-size: 24px;\n    }\n\n    .company-name-hero {\n        font-size: 18px;\n    }\n\n    .company-link-hero a {\n        font-size: 11px;\n        padding: 8px 16px;\n        border-radius: 18px;\n        gap: 6px;\n    }\n\n    .company-link-hero a::before {\n        font-size: 12px;\n    }\n\n    .hero-company-logo {\n        width: 80px;\n        height: 80px;\n    }\n\n    .content-card {\n        padding: 20px;\n    }\n\n    .accomplishments-list {\n        grid-template-columns: 1fr;\n    }\n\n    .projects-grid {\n        grid-template-columns: 1fr;\n    }\n}\n\n/* Custom skill category styles based on category names */\n.frontend_skills {\n    border-left: 4px solid #61DAFB; /* React blue */\n    background: rgba(97, 218, 251, 0.05);\n}\n\n.frontend_skills h4 {\n    color: #61DAFB !important;\n}\n\n.styling_skills {\n    border-left: 4px solid #FF6B6B; /* Coral red */\n    background: rgba(255, 107, 107, 0.05);\n}\n\n.styling_skills h4 {\n    color: #FF6B6B !important;\n}\n\n.tools___testing_skills {\n    border-left: 4px solid #4ECDC4; /* Teal */\n    background: rgba(78, 205, 196, 0.05);\n}\n\n.tools___testing_skills h4 {\n    color: #4ECDC4 !important;\n}\n\n.backend_skills {\n    border-left: 4px solid #68D391; /* Green */\n    background: rgba(104, 211, 145, 0.05);\n}\n\n.backend_skills h4 {\n    color: #68D391 !important;\n}\n\n.cloud___devops_skills {\n    border-left: 4px solid #F6AD55; /* Orange */\n    background: rgba(246, 173, 85, 0.05);\n}\n\n.cloud___devops_skills h4 {\n    color: #F6AD55 !important;\n}\n\n.design_tools_skills {\n    border-left: 4px solid #ED64A6; /* Pink */\n    background: rgba(237, 100, 166, 0.05);\n}\n\n.design_tools_skills h4 {\n    color: #ED64A6 !important;\n}\n\n.frontend_development_skills {\n    border-left: 4px solid #9F7AEA; /* Purple */\n    background: rgba(159, 122, 234, 0.05);\n}\n\n.frontend_development_skills h4 {\n    color: #9F7AEA !important;\n}\n\n.ux_research_skills {\n    border-left: 4px solid #38B2AC; /* Teal green */\n    background: rgba(56, 178, 172, 0.05);\n}\n\n.ux_research_skills h4 {\n    color: #38B2AC !important;\n}\n\n/* Enhanced skill category styling */\n.skill-category {\n    padding: 20px;\n    margin-bottom: 20px;\n    border-radius: 12px;\n    background: rgba(255, 255, 255, 0.03);\n    transition: all 0.3s ease;\n    border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.skill-category:hover {\n    background: rgba(255, 255, 255, 0.08);\n    transform: translateY(-3px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n}\n\n/* NDA Project Image Styling */\n.project-image img[src*=\"NDA.jpg\"] {\n    filter: grayscale(20%);\n    opacity: 0.9;\n    border: 2px solid #FF6B6B;\n    position: relative;\n}\n\n.project-card:has(img[src*=\"NDA.jpg\"]) {\n    border: 2px solid rgba(255, 107, 107, 0.3);\n    background: rgba(255, 107, 107, 0.05);\n}\n\n.project-card:has(img[src*=\"NDA.jpg\"]):hover {\n    border-color: rgba(255, 107, 107, 0.6);\n    box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);\n}\n\n.project-card:has(img[src*=\"NDA.jpg\"]) .project-info::before {\n    content: \"🔒 NDA Protected\";\n    display: inline-block;\n    background: linear-gradient(135deg, #FF6B6B, #FF8E8E);\n    color: white;\n    padding: 4px 8px;\n    border-radius: 12px;\n    font-size: 12px;\n    font-weight: 600;\n    margin-bottom: 10px;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n}\n", "/* Project Image Swiper Styles */\n.project-image-swiper {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  border-radius: 20px 20px 0 0;\n}\n\n.project-swiper {\n  width: 100%;\n  height: 100%;\n}\n\n.swiper-slide-content {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  cursor: default;\n}\n\n.swiper-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n  cursor: default;\n}\n\n.project-card:hover .swiper-image {\n  transform: scale(1.05);\n}\n\n/* Custom Navigation Buttons */\n.swiper-button-prev-custom,\n.swiper-button-next-custom {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 40px;\n  height: 40px;\n  background: rgba(0, 0, 0, 0.7);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 10;\n  transition: all 0.3s ease;\n  opacity: 0;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.swiper-button-prev-custom {\n  left: 15px;\n}\n\n.swiper-button-next-custom {\n  right: 15px;\n}\n\n.project-card:hover .swiper-button-prev-custom,\n.project-card:hover .swiper-button-next-custom {\n  opacity: 1;\n}\n\n.swiper-button-prev-custom:hover,\n.swiper-button-next-custom:hover {\n  background: rgba(75, 0, 130, 0.8);\n  border-color: #FF2D55;\n  transform: translateY(-50%) scale(1.1);\n}\n\n.swiper-button-prev-custom span,\n.swiper-button-next-custom span {\n  color: #FFFFFF;\n  font-size: 20px;\n  font-weight: bold;\n  line-height: 1;\n}\n\n/* Custom Pagination */\n.project-swiper .swiper-pagination {\n  bottom: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: auto;\n}\n\n.project-swiper .swiper-pagination-bullet {\n  background: rgba(255, 255, 255, 0.5);\n  opacity: 1;\n  width: 8px;\n  height: 8px;\n  margin: 0 4px;\n  transition: all 0.3s ease;\n}\n\n.project-swiper .swiper-pagination-bullet-active {\n  background: #FF2D55;\n  transform: scale(1.2);\n  box-shadow: 0 0 10px rgba(255, 45, 85, 0.5);\n}\n\n/* Swipe Indicator */\n.swipe-indicator {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 8px 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  z-index: 10;\n  opacity: 0;\n  transform: translateY(-10px);\n  transition: all 0.3s ease;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.project-card:hover .swipe-indicator {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n\n\n\n\n.swipe-text {\n  color: #FFFFFF;\n  font-family: 'Montserrat', sans-serif;\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.swipe-animation {\n  display: flex;\n  gap: 3px;\n  align-items: center;\n}\n\n.swipe-dot {\n  width: 4px;\n  height: 4px;\n  background: #FF2D55;\n  border-radius: 50%;\n  animation: swipeAnimation 2s infinite;\n}\n\n.swipe-dot:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.swipe-dot:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n@keyframes swipeAnimation {\n  0%, 60%, 100% {\n    opacity: 0.3;\n    transform: scale(1);\n  }\n  30% {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n}\n\n/* Fullscreen Icon */\n.fullscreen-icon {\n  position: absolute;\n  bottom: 15px;\n  right: 15px;\n  width: 40px;\n  height: 40px;\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 15;\n  transition: all 0.3s ease;\n  opacity: 0;\n  transform: translateY(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: #FFFFFF;\n}\n\n.project-card:hover .fullscreen-icon {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.fullscreen-icon:hover {\n  background: rgba(75, 0, 130, 0.8);\n  transform: translateY(0) scale(1.1);\n  box-shadow: 0 5px 15px rgba(75, 0, 130, 0.4);\n}\n\n/* Fullscreen Modal */\n.fullscreen-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.95);\n  backdrop-filter: blur(20px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 99999;\n  animation: fadeIn 0.3s ease;\n}\n\n.fullscreen-content {\n  position: relative;\n  max-width: 95vw;\n  max-height: 95vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.fullscreen-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 10px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\n}\n\n.fullscreen-close {\n  position: absolute;\n  top: -50px;\n  right: -10px;\n  width: 44px;\n  height: 44px;\n  background: rgba(255, 45, 85, 0.9);\n  border: none;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 100001;\n  transition: all 0.3s ease;\n  color: #FFFFFF;\n}\n\n.fullscreen-close:hover {\n  background: rgba(255, 45, 85, 1);\n  transform: scale(1.1);\n  box-shadow: 0 5px 15px rgba(255, 45, 85, 0.4);\n}\n\n.fullscreen-navigation {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n  padding: 0 20px;\n}\n\n.fullscreen-nav-btn {\n  width: 50px;\n  height: 50px;\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  color: #FFFFFF;\n  pointer-events: all;\n}\n\n.fullscreen-nav-btn:hover {\n  background: rgba(75, 0, 130, 0.8);\n  transform: scale(1.1);\n  box-shadow: 0 5px 15px rgba(75, 0, 130, 0.4);\n}\n\n.fullscreen-counter {\n  position: absolute;\n  bottom: -50px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.7);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 20px;\n  padding: 8px 16px;\n  color: #FFFFFF;\n  font-family: 'Montserrat', sans-serif;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n/* Mobile Responsive Styles */\n@media (max-width: 768px) {\n  .fullscreen-icon {\n    width: 35px;\n    height: 35px;\n    bottom: 10px;\n    right: 10px;\n  }\n\n  .fullscreen-close {\n    top: -40px;\n    right: -5px;\n    width: 40px;\n    height: 40px;\n  }\n\n  .fullscreen-navigation {\n    padding: 0 15px;\n  }\n\n  .fullscreen-nav-btn {\n    width: 45px;\n    height: 45px;\n  }\n\n  .fullscreen-counter {\n    bottom: -40px;\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n\n  .fullscreen-content {\n    max-width: 98vw;\n    max-height: 98vh;\n  }\n\n  .swiper-button-prev-custom,\n  .swiper-button-next-custom {\n    width: 35px;\n    height: 35px;\n  }\n\n  .swiper-button-prev-custom {\n    left: 10px;\n  }\n\n  .swiper-button-next-custom {\n    right: 10px;\n  }\n\n  .swiper-button-prev-custom span,\n  .swiper-button-next-custom span {\n    font-size: 18px;\n  }\n\n  .swipe-indicator {\n    top: 10px;\n    right: 10px;\n    padding: 6px 10px;\n  }\n\n  .swipe-text {\n    font-size: 11px;\n  }\n\n  .project-swiper .swiper-pagination {\n    bottom: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .fullscreen-icon {\n    width: 32px;\n    height: 32px;\n    bottom: 8px;\n    right: 8px;\n  }\n\n  .fullscreen-close {\n    top: -35px;\n    right: 0;\n    width: 36px;\n    height: 36px;\n  }\n\n  .fullscreen-nav-btn {\n    width: 40px;\n    height: 40px;\n  }\n\n  .fullscreen-navigation {\n    padding: 0 10px;\n  }\n\n  .swiper-button-prev-custom,\n  .swiper-button-next-custom {\n    width: 30px;\n    height: 30px;\n  }\n\n  .swiper-button-prev-custom span,\n  .swiper-button-next-custom span {\n    font-size: 16px;\n  }\n\n  .project-swiper .swiper-pagination-bullet {\n    width: 6px;\n    height: 6px;\n    margin: 0 3px;\n  }\n}\n\n/* Touch/Mobile specific styles */\n@media (hover: none) and (pointer: coarse) {\n  .swiper-button-prev-custom,\n  .swiper-button-next-custom,\n  .swipe-indicator,\n  .fullscreen-icon {\n    opacity: 1;\n  }\n\n  .swipe-indicator,\n  .fullscreen-icon {\n    transform: translateY(0);\n  }\n}\n"], "names": [], "sourceRoot": ""}