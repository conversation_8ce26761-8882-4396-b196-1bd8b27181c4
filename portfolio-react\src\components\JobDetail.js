import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { jobsData } from '../data/jobsData';

const JobDetail = () => {
  const { slug } = useParams();
  const job = jobsData.find(job => job.slug === slug);

  if (!job) {
    return (
      <div className="job-detail">
        <h2>Job not found</h2>
        <Link to="/experience">← Back to Experience</Link>
      </div>
    );
  }

  return (
    <div className="job-detail">
      <div className="job-header">
        <Link to="/experience" className="back-link">← Back to Experience</Link>
        
        <div className="job-title-section">
          <img src={job.logo} alt={job.logoAlt} className="company-logo-large" />
          <div className="job-info">
            <h1>{job.title}</h1>
            <h2>{job.company}</h2>
            {job.companyLink && (
              <a href={job.companyLink} target="_blank" rel="noopener noreferrer" className="company-link">
                Visit Company Website →
              </a>
            )}
            <p className="duration">{job.duration}</p>
          </div>
        </div>
      </div>

      <div className="job-content">
        <section className="description-section">
          <h3>About the Role</h3>
          <p>{job.description}</p>
        </section>

        <section className="responsibilities-section">
          <h3>Key Responsibilities</h3>
          <ul>
            {job.responsibilities.map((responsibility, index) => (
              <li key={index}>{responsibility}</li>
            ))}
          </ul>
        </section>

        <section className="technologies-section">
          <h3>Technologies Used</h3>
          <div className="tech-tags">
            {job.technologies.map((tech, index) => (
              <span key={index} className="tech-tag">{tech}</span>
            ))}
          </div>
        </section>

        <section className="accomplishments-section">
          <h3>Key Accomplishments</h3>
          <div className="accomplishments-grid">
            {job.accomplishments.map((accomplishment, index) => (
              <div key={index} className="accomplishment-card">
                <div className="metric">{accomplishment.metric}</div>
                <div className="description">{accomplishment.description}</div>
              </div>
            ))}
          </div>
        </section>

        {job.projects && job.projects.length > 0 && (
          <section className="projects-section">
            <h3>Projects</h3>
            <div className="projects-grid">
              {job.projects.map((project, index) => (
                <div key={index} className="project-card">
                  <h4>{project.title}</h4>
                  <p>{project.description}</p>
                  <div className="project-tech">
                    {project.technologies.map((tech, techIndex) => (
                      <span key={techIndex} className="tech-tag small">{tech}</span>
                    ))}
                  </div>
                  {!project.isNDA && project.liveUrl && (
                    <a href={project.liveUrl} target="_blank" rel="noopener noreferrer" className="project-link">
                      View Project →
                    </a>
                  )}
                  {project.isNDA && (
                    <span className="nda-notice">Project details are confidential</span>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default JobDetail;
