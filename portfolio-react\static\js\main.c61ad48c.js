/*! For license information please see main.c61ad48c.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),a=n(43),l=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(s(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var p=Object.assign,f=Symbol.for("react.element"),m=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),S=Symbol.for("react.context"),x=Symbol.for("react.forward_ref"),k=Symbol.for("react.suspense"),E=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope");var P=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var L=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var N=Symbol.iterator;function j(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=N&&e[N]||e["@@iterator"])?e:null}var _=Symbol.for("react.client.reference");function O(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===_?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case v:return"StrictMode";case k:return"Suspense";case E:return"SuspenseList";case P:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case h:return"Portal";case S:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case x:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:O(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return O(e(t))}catch(n){}}return null}var M=Array.isArray,z=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,A=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D={pending:!1,data:null,method:null,action:null},R=[],I=-1;function F(e){return{current:e}}function B(e){0>I||(e.current=R[I],R[I]=null,I--)}function H(e,t){I++,R[I]=e.current,e.current=t}var U=F(null),V=F(null),G=F(null),W=F(null);function q(e,t){switch(H(G,t),H(V,e),H(U,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ld(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}B(U),H(U,e)}function $(){B(U),B(V),B(G)}function Y(e){null!==e.memoizedState&&H(W,e);var t=U.current,n=ld(t,e.type);t!==n&&(H(V,e),H(U,n))}function X(e){V.current===e&&(B(U),B(V)),W.current===e&&(B(W),Yd._currentValue=D)}var Q=Object.prototype.hasOwnProperty,K=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,le=r.unstable_NormalPriority,ie=r.unstable_LowPriority,oe=r.unstable_IdlePriority,se=r.log,ce=r.unstable_setDisableYieldValue,ue=null,de=null;function pe(e){if("function"===typeof se&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var fe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(me(e)/he|0)|0},me=Math.log,he=Math.LN2;var ge=256,ve=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var o=134217727&r;return 0!==o?0!==(r=o&~l)?a=ye(r):0!==(i&=o)?a=ye(i):n||0!==(n=o&~e)&&(a=ye(n)):0!==(o=r&~l)?a=ye(o):0!==i?a=ye(i):n||0!==(n=r&~e)&&(a=ye(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&0!==(4194048&n))?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Se(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function xe(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function ke(){var e=ve;return 0===(62914560&(ve<<=1))&&(ve=4194304),e}function Ee(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Te(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-fe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Pe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-fe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Le(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ne(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function je(){var e=A.p;return 0!==e?e:void 0===(e=window.event)?32:cp(e.type)}var _e=Math.random().toString(36).slice(2),Oe="__reactFiber$"+_e,Me="__reactProps$"+_e,ze="__reactContainer$"+_e,Ae="__reactEvents$"+_e,De="__reactListeners$"+_e,Re="__reactHandles$"+_e,Ie="__reactResources$"+_e,Fe="__reactMarker$"+_e;function Be(e){delete e[Oe],delete e[Me],delete e[Ae],delete e[De],delete e[Re]}function He(e){var t=e[Oe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ze]||n[Oe]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Oe])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function Ue(e){if(e=e[Oe]||e[ze]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ve(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function Ge(e){var t=e[Ie];return t||(t=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function We(e){e[Fe]=!0}var qe=new Set,$e={};function Ye(e,t){Xe(e,t),Xe(e+"Capture",t)}function Xe(e,t){for($e[e]=t,e=0;e<t.length;e++)qe.add(t[e])}var Qe,Ke,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,Q.call(et,a)||!Q.call(Ze,a)&&(Je.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Qe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Qe=t&&t[1]||"",Ke=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Qe+e+Ke}var lt=!1;function it(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){r=l}e.call(n.prototype)}}else{try{throw Error()}catch(i){r=i}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(o){if(o&&r&&"string"===typeof o.stack)return[o.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),i=l[0],o=l[1];if(i&&o){var s=i.split("\n"),c=o.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===c.length)for(r=s.length-1,a=c.length-1;1<=r&&0<=a&&s[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==c[a]){var u="\n"+s[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function pt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function ft(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var mt=/[\n"\\]/g;function ht(e){return e.replace(mt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,r,a,l,i,o){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?yt(e,i,ct(t)):null!=n?yt(e,i,ct(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.name=""+ct(o):e.removeAttribute("name")}function vt(e,t,n,r,a,l,i,o){if(null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.type=l),null!=t||null!=n){if(!("submit"!==l&&"reset"!==l||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,o||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=o?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function yt(e,t,n){"number"===t&&ft(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function St(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(M(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function xt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var kt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Et(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||kt.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&Et(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&Et(e,l,t[l])}function Tt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Lt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Nt(e){return Lt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var jt=null;function _t(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ot=null,Mt=null;function zt(e){var t=Ue(e);if(t&&(e=t.stateNode)){var n=e[Me]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Me]||null;if(!a)throw Error(i(90));gt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&pt(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var At=!1;function Dt(e,t,n){if(At)return e(t,n);At=!0;try{return e(t)}finally{if(At=!1,(null!==Ot||null!==Mt)&&(Hc(),Ot&&(t=Ot,e=Mt,Mt=Ot=null,zt(t),e)))for(t=0;t<e.length;t++)zt(e[t])}}function Rt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Me]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var It=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),Ft=!1;if(It)try{var Bt={};Object.defineProperty(Bt,"passive",{get:function(){Ft=!0}}),window.addEventListener("test",Bt,Bt),window.removeEventListener("test",Bt,Bt)}catch(Mp){Ft=!1}var Ht=null,Ut=null,Vt=null;function Gt(){if(Vt)return Vt;var e,t,n=Ut,r=n.length,a="value"in Ht?Ht.value:Ht.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[l-t];t++);return Vt=a.slice(e,1<t?1-t:void 0)}function Wt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function qt(){return!0}function $t(){return!1}function Yt(e){function t(t,n,r,a,l){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?qt:$t,this.isPropagationStopped=$t,this}return p(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=qt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=qt)},persist:function(){},isPersistent:qt}),t}var Xt,Qt,Kt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Yt(Jt),en=p({},Jt,{view:0,detail:0}),tn=Yt(en),nn=p({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Kt&&(Kt&&"mousemove"===e.type?(Xt=e.screenX-Kt.screenX,Qt=e.screenY-Kt.screenY):Qt=Xt=0,Kt=e),Xt)},movementY:function(e){return"movementY"in e?e.movementY:Qt}}),rn=Yt(nn),an=Yt(p({},nn,{dataTransfer:0})),ln=Yt(p({},en,{relatedTarget:0})),on=Yt(p({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Yt(p({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Yt(p({},Jt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},pn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=pn[e])&&!!t[e]}function mn(){return fn}var hn=Yt(p({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Wt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mn,charCode:function(e){return"keypress"===e.type?Wt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Wt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Yt(p({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),vn=Yt(p({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mn})),yn=Yt(p({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Yt(p({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Yt(p({},Jt,{newState:0,oldState:0})),Sn=[9,13,27,32],xn=It&&"CompositionEvent"in window,kn=null;It&&"documentMode"in document&&(kn=document.documentMode);var En=It&&"TextEvent"in window&&!kn,Cn=It&&(!xn||kn&&8<kn&&11>=kn),Tn=String.fromCharCode(32),Pn=!1;function Ln(e,t){switch(e){case"keyup":return-1!==Sn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Nn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var jn=!1;var _n={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function On(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!_n[e.type]:"textarea"===t}function Mn(e,t,n,r){Ot?Mt?Mt.push(r):Mt=[r]:Ot=r,0<(t=Gu(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zn=null,An=null;function Dn(e){Du(e,0)}function Rn(e){if(pt(Ve(e)))return e}function In(e,t){if("change"===e)return t}var Fn=!1;if(It){var Bn;if(It){var Hn="oninput"in document;if(!Hn){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),Hn="function"===typeof Un.oninput}Bn=Hn}else Bn=!1;Fn=Bn&&(!document.documentMode||9<document.documentMode)}function Vn(){zn&&(zn.detachEvent("onpropertychange",Gn),An=zn=null)}function Gn(e){if("value"===e.propertyName&&Rn(An)){var t=[];Mn(t,An,e,_t(e)),Dt(Dn,t)}}function Wn(e,t,n){"focusin"===e?(Vn(),An=n,(zn=t).attachEvent("onpropertychange",Gn)):"focusout"===e&&Vn()}function qn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Rn(An)}function $n(e,t){if("click"===e)return Rn(t)}function Yn(e,t){if("input"===e||"change"===e)return Rn(t)}var Xn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Qn(e,t){if(Xn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Q.call(t,a)||!Xn(e[a],t[a]))return!1}return!0}function Kn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,r=Kn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Kn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=ft((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=ft((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=It&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,lr=null,ir=!1;function or(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==ft(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Qn(lr,r)||(lr=r,0<(r=Gu(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},ur={},dr={};function pr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}It&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var fr=pr("animationend"),mr=pr("animationiteration"),hr=pr("animationstart"),gr=pr("transitionrun"),vr=pr("transitionstart"),yr=pr("transitioncancel"),br=pr("transitionend"),wr=new Map,Sr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xr(e,t){wr.set(e,t),Ye(t,[e])}Sr.push("scrollEnd");var kr=new WeakMap;function Er(e,t){if("object"===typeof e&&null!==e){var n=kr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},kr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Cr=[],Tr=0,Pr=0;function Lr(){for(var e=Tr,t=Pr=Tr=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var a=Cr[t];Cr[t++]=null;var l=Cr[t];if(Cr[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==l&&Or(n,a,l)}}function Nr(e,t,n,r){Cr[Tr++]=e,Cr[Tr++]=t,Cr[Tr++]=n,Cr[Tr++]=r,Pr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function jr(e,t,n,r){return Nr(e,t,n,r),Mr(e)}function _r(e,t){return Nr(e,null,null,t),Mr(e)}function Or(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-fe(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Mr(e){if(50<Oc)throw Oc=0,Mc=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var zr={};function Ar(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Dr(e,t,n,r){return new Ar(e,t,n,r)}function Rr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,t){var n=e.alternate;return null===n?((n=Dr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Fr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Br(e,t,n,r,a,l){var o=0;if(r=e,"function"===typeof e)Rr(e)&&(o=1);else if("string"===typeof e)o=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,U.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case P:return(e=Dr(31,n,t,a)).elementType=P,e.lanes=l,e;case g:return Hr(n.children,a,l,t);case v:o=8,a|=24;break;case y:return(e=Dr(12,n,t,2|a)).elementType=y,e.lanes=l,e;case k:return(e=Dr(13,n,t,a)).elementType=k,e.lanes=l,e;case E:return(e=Dr(19,n,t,a)).elementType=E,e.lanes=l,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case b:case S:o=10;break e;case w:o=9;break e;case x:o=11;break e;case C:o=14;break e;case T:o=16,r=null;break e}o=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=Dr(o,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Hr(e,t,n,r){return(e=Dr(7,e,r,t)).lanes=n,e}function Ur(e,t,n){return(e=Dr(6,e,null,t)).lanes=n,e}function Vr(e,t,n){return(t=Dr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Gr=[],Wr=0,qr=null,$r=0,Yr=[],Xr=0,Qr=null,Kr=1,Jr="";function Zr(e,t){Gr[Wr++]=$r,Gr[Wr++]=qr,qr=e,$r=t}function ea(e,t,n){Yr[Xr++]=Kr,Yr[Xr++]=Jr,Yr[Xr++]=Qr,Qr=e;var r=Kr;e=Jr;var a=32-fe(r)-1;r&=~(1<<a),n+=1;var l=32-fe(t)+a;if(30<l){var i=a-a%5;l=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Kr=1<<32-fe(t)+a|n<<a|r,Jr=l+e}else Kr=1<<l|n<<a|r,Jr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===qr;)qr=Gr[--Wr],Gr[Wr]=null,$r=Gr[--Wr],Gr[Wr]=null;for(;e===Qr;)Qr=Yr[--Xr],Yr[Xr]=null,Jr=Yr[--Xr],Yr[Xr]=null,Kr=Yr[--Xr],Yr[Xr]=null}var ra=null,aa=null,la=!1,ia=null,oa=!1,sa=Error(i(519));function ca(e){throw ha(Er(Error(i(418,"")),e)),sa}function ua(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Oe]=e,t[Me]=r,n){case"dialog":Ru("cancel",t),Ru("close",t);break;case"iframe":case"object":case"embed":Ru("load",t);break;case"video":case"audio":for(n=0;n<zu.length;n++)Ru(zu[n],t);break;case"source":Ru("error",t);break;case"img":case"image":case"link":Ru("error",t),Ru("load",t);break;case"details":Ru("toggle",t);break;case"input":Ru("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Ru("invalid",t);break;case"textarea":Ru("invalid",t),St(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Qu(t.textContent,n)?(null!=r.popover&&(Ru("beforetoggle",t),Ru("toggle",t)),null!=r.onScroll&&Ru("scroll",t),null!=r.onScrollEnd&&Ru("scrollend",t),null!=r.onClick&&(t.onclick=Ku),t=!0):t=!1,t||ca(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(oa=!1);case 27:case 3:return void(oa=!0);default:ra=ra.return}}function pa(e){if(e!==ra)return!1;if(!la)return da(e),la=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||id(e.type,e.memoizedProps)),t=!t),t&&aa&&ca(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=vd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,fd(e.type)?(e=yd,yd=null,aa=e):aa=n):aa=ra?vd(e.stateNode.nextSibling):null;return!0}function fa(){aa=ra=null,la=!1}function ma(){var e=ia;return null!==e&&(null===bc?bc=e:bc.push.apply(bc,e),ia=null),e}function ha(e){null===ia?ia=[e]:ia.push(e)}var ga=F(null),va=null,ya=null;function ba(e,t,n){H(ga,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ga.current,B(ga)}function Sa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var o=a.child;l=l.firstContext;e:for(;null!==l;){var s=l;l=a;for(var c=0;c<t.length;c++)if(s.context===t[c]){l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),Sa(l.return,n,e),r||(o=null);break e}l=s.next}}else if(18===a.tag){if(null===(o=a.return))throw Error(i(341));o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),Sa(o,n,e),o=null}else o=a.child;if(null!==o)o.return=a;else for(o=a;null!==o;){if(o===e){o=null;break}if(null!==(a=o.sibling)){a.return=o.return,o=a;break}o=o.return}a=o}}function ka(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(0!==(524288&a.flags))l=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var o=a.alternate;if(null===o)throw Error(i(387));if(null!==(o=o.memoizedProps)){var s=a.type;Xn(a.pendingProps.value,o.value)||(null!==e?e.push(s):e=[s])}}else if(a===W.current){if(null===(o=a.alternate))throw Error(i(387));o.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Yd):e=[Yd])}a=a.return}null!==e&&xa(t,e,n,r),t.flags|=262144}function Ea(e){for(e=e.firstContext;null!==e;){if(!Xn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){va=e,ya=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ta(e){return La(va,e)}function Pa(e,t){return null===va&&Ca(e),La(e,t)}function La(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ya){if(null===e)throw Error(i(308));ya=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ya=ya.next=t;return n}var Na="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},ja=r.unstable_scheduleCallback,_a=r.unstable_NormalPriority,Oa={$$typeof:S,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ma(){return{controller:new Na,data:new Map,refCount:0}}function za(e){e.refCount--,0===e.refCount&&ja(_a,function(){e.controller.abort()})}var Aa=null,Da=0,Ra=0,Ia=null;function Fa(){if(0===--Da&&null!==Aa){null!==Ia&&(Ia.status="fulfilled");var e=Aa;Aa=null,Ra=0,Ia=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ba=z.S;z.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Aa){var n=Aa=[];Da=0,Ra=Nu(),Ia={status:"pending",value:void 0,then:function(e){n.push(e)}}}Da++,t.then(Fa,Fa)}(0,t),null!==Ba&&Ba(e,t)};var Ha=F(null);function Ua(){var e=Ha.current;return null!==e?e:rc.pooledCache}function Va(e,t){H(Ha,null===t?Ha.current:t.pool)}function Ga(){var e=Ua();return null===e?null:{parent:Oa._currentValue,pool:e}}var Wa=Error(i(460)),qa=Error(i(474)),$a=Error(i(542)),Ya={then:function(){}};function Xa(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Qa(){}function Ka(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Qa,Qa),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e;default:if("string"===typeof t.status)t.then(Qa,Qa);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e}throw Ja=t,Wa}}var Ja=null;function Za(){if(null===Ja)throw Error(i(459));var e=Ja;return Ja=null,e}function el(e){if(e===Wa||e===$a)throw Error(i(483))}var tl=!1;function nl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function al(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ll(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&nc)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Mr(e),Or(e,null,n),t}return Nr(e,r,t,n),Mr(e)}function il(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}function ol(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var sl=!1;function cl(){if(sl){if(null!==Ia)throw Ia}}function ul(e,t,n,r){sl=!1;var a=e.updateQueue;tl=!1;var l=a.firstBaseUpdate,i=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,c=s.next;s.next=null,null===i?l=c:i.next=c,i=s;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==i&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(i=0,u=c=s=null,o=l;;){var f=-536870913&o.lane,m=f!==o.lane;if(m?(lc&f)===f:(r&f)===f){0!==f&&f===Ra&&(sl=!0),null!==u&&(u=u.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var h=e,g=o;f=t;var v=n;switch(g.tag){case 1:if("function"===typeof(h=g.payload)){d=h.call(v,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=g.payload)?h.call(v,d,f):h)||void 0===f)break e;d=p({},d,f);break e;case 2:tl=!0}}null!==(f=o.callback)&&(e.flags|=64,m&&(e.flags|=8192),null===(m=a.callbacks)?a.callbacks=[f]:m.push(f))}else m={lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=m,s=d):u=u.next=m,i|=f;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(m=o).next,m.next=null,a.lastBaseUpdate=m,a.shared.pending=null}}null===u&&(s=d),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===l&&(a.shared.lanes=0),fc|=i,e.lanes=i,e.memoizedState=d}}function dl(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function pl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)dl(n[e],t)}var fl=F(null),ml=F(0);function hl(e,t){H(ml,e=dc),H(fl,t),dc=e|t.baseLanes}function gl(){H(ml,dc),H(fl,fl.current)}function vl(){dc=ml.current,B(fl),B(ml)}var yl=0,bl=null,wl=null,Sl=null,xl=!1,kl=!1,El=!1,Cl=0,Tl=0,Pl=null,Ll=0;function Nl(){throw Error(i(321))}function jl(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Xn(e[n],t[n]))return!1;return!0}function _l(e,t,n,r,a,l){return yl=l,bl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=null===e||null===e.memoizedState?Wi:qi,El=!1,l=n(r,a),El=!1,kl&&(l=Ml(t,n,r,a)),Ol(e),l}function Ol(e){z.H=Gi;var t=null!==wl&&null!==wl.next;if(yl=0,Sl=wl=bl=null,xl=!1,Tl=0,Pl=null,t)throw Error(i(300));null===e||Po||null!==(e=e.dependencies)&&Ea(e)&&(Po=!0)}function Ml(e,t,n,r){bl=e;var a=0;do{if(kl&&(Pl=null),Tl=0,kl=!1,25<=a)throw Error(i(301));if(a+=1,Sl=wl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}z.H=$i,l=t(n,r)}while(kl);return l}function zl(){var e=z.H,t=e.useState()[0];return t="function"===typeof t.then?Bl(t):t,e=e.useState()[0],(null!==wl?wl.memoizedState:null)!==e&&(bl.flags|=1024),t}function Al(){var e=0!==Cl;return Cl=0,e}function Dl(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Rl(e){if(xl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}xl=!1}yl=0,Sl=wl=bl=null,kl=!1,Tl=Cl=0,Pl=null}function Il(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Sl?bl.memoizedState=Sl=e:Sl=Sl.next=e,Sl}function Fl(){if(null===wl){var e=bl.alternate;e=null!==e?e.memoizedState:null}else e=wl.next;var t=null===Sl?bl.memoizedState:Sl.next;if(null!==t)Sl=t,wl=e;else{if(null===e){if(null===bl.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(wl=e).memoizedState,baseState:wl.baseState,baseQueue:wl.baseQueue,queue:wl.queue,next:null},null===Sl?bl.memoizedState=Sl=e:Sl=Sl.next=e}return Sl}function Bl(e){var t=Tl;return Tl+=1,null===Pl&&(Pl=[]),e=Ka(Pl,e,t),t=bl,null===(null===Sl?t.memoizedState:Sl.next)&&(t=t.alternate,z.H=null===t||null===t.memoizedState?Wi:qi),e}function Hl(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Bl(e);if(e.$$typeof===S)return Ta(e)}throw Error(i(438,String(e)))}function Ul(e){var t=null,n=bl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=bl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=L;return t.index++,n}function Vl(e,t){return"function"===typeof t?t(e):t}function Gl(e){return Wl(Fl(),wl,e)}function Wl(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var o=a.next;a.next=l.next,l.next=o}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var s=o=null,c=null,u=t=a.next,d=!1;do{var p=-536870913&u.lane;if(p!==u.lane?(lc&p)===p:(yl&p)===p){var f=u.revertLane;if(0===f)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),p===Ra&&(d=!0);else{if((yl&f)===f){u=u.next,f===Ra&&(d=!0);continue}p={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=p,o=l):c=c.next=p,bl.lanes|=f,fc|=f}p=u.action,El&&n(l,p),l=u.hasEagerState?u.eagerState:n(l,p)}else f={lane:p,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=f,o=l):c=c.next=f,bl.lanes|=p,fc|=p;u=u.next}while(null!==u&&u!==t);if(null===c?o=l:c.next=s,!Xn(l,e.memoizedState)&&(Po=!0,d&&null!==(n=Ia)))throw n;e.memoizedState=l,e.baseState=o,e.baseQueue=c,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function ql(e){var t=Fl(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{l=e(l,o.action),o=o.next}while(o!==a);Xn(l,t.memoizedState)||(Po=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function $l(e,t,n){var r=bl,a=Fl(),l=la;if(l){if(void 0===n)throw Error(i(407));n=n()}else n=t();var o=!Xn((wl||a).memoizedState,n);if(o&&(a.memoizedState=n,Po=!0),a=a.queue,gi(2048,8,Ql.bind(null,r,a,e),[e]),a.getSnapshot!==t||o||null!==Sl&&1&Sl.memoizedState.tag){if(r.flags|=2048,fi(9,{destroy:void 0,resource:void 0},Xl.bind(null,r,a,n,t),null),null===rc)throw Error(i(349));l||0!==(124&yl)||Yl(r,t,n)}return n}function Yl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=bl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Xl(e,t,n,r){t.value=n,t.getSnapshot=r,Kl(t)&&Jl(e)}function Ql(e,t,n){return n(function(){Kl(t)&&Jl(e)})}function Kl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Xn(e,n)}catch(r){return!0}}function Jl(e){var t=_r(e,2);null!==t&&Dc(t,e,2)}function Zl(e){var t=Il();if("function"===typeof e){var n=e;if(e=n(),El){pe(!0);try{n()}finally{pe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:e},t}function ei(e,t,n,r){return e.baseState=n,Wl(e,wl,"function"===typeof r?r:Vl)}function ti(e,t,n,r,a){if(Hi(e))throw Error(i(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==z.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,ni(t,l)):(l.next=n.next,t.pending=n.next=l)}}function ni(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=z.T,i={};z.T=i;try{var o=n(a,r),s=z.S;null!==s&&s(i,o),ri(e,t,o)}catch(c){li(e,t,c)}finally{z.T=l}}else try{ri(e,t,l=n(a,r))}catch(u){li(e,t,u)}}function ri(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){ai(e,t,n)},function(n){return li(e,t,n)}):ai(e,t,n)}function ai(e,t,n){t.status="fulfilled",t.value=n,ii(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ni(e,n)))}function li(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,ii(t),t=t.next}while(t!==r)}e.action=null}function ii(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function oi(e,t){return t}function si(e,t){if(la){var n=rc.formState;if(null!==n){e:{var r=bl;if(la){if(aa){t:{for(var a=aa,l=oa;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=vd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){aa=vd(a.nextSibling),r="F!"===a.data;break e}}ca(r)}r=!1}r&&(t=n[0])}}return(n=Il()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:oi,lastRenderedState:t},n.queue=r,n=Ii.bind(null,bl,r),r.dispatch=n,r=Zl(!1),l=Bi.bind(null,bl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Il()).queue=a,n=ti.bind(null,bl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ci(e){return ui(Fl(),wl,e)}function ui(e,t,n){if(t=Wl(e,t,oi)[0],e=Gl(Vl)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Bl(t)}catch(i){if(i===Wa)throw $a;throw i}else r=t;var a=(t=Fl()).queue,l=a.dispatch;return n!==t.memoizedState&&(bl.flags|=2048,fi(9,{destroy:void 0,resource:void 0},di.bind(null,a,n),null)),[r,l,e]}function di(e,t){e.action=t}function pi(e){var t=Fl(),n=wl;if(null!==n)return ui(t,n,e);Fl(),t=t.memoizedState;var r=(n=Fl()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function fi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=bl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},bl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function mi(){return Fl().memoizedState}function hi(e,t,n,r){var a=Il();r=void 0===r?null:r,bl.flags|=e,a.memoizedState=fi(1|t,{destroy:void 0,resource:void 0},n,r)}function gi(e,t,n,r){var a=Fl();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==wl&&null!==r&&jl(r,wl.memoizedState.deps)?a.memoizedState=fi(t,l,n,r):(bl.flags|=e,a.memoizedState=fi(1|t,l,n,r))}function vi(e,t){hi(8390656,8,e,t)}function yi(e,t){gi(2048,8,e,t)}function bi(e,t){return gi(4,2,e,t)}function wi(e,t){return gi(4,4,e,t)}function Si(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function xi(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4,4,Si.bind(null,t,e),n)}function ki(){}function Ei(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&jl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ci(e,t){var n=Fl();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&jl(t,r[1]))return r[0];if(r=e(),El){pe(!0);try{e()}finally{pe(!1)}}return n.memoizedState=[r,t],r}function Ti(e,t,n){return void 0===n||0!==(1073741824&yl)?e.memoizedState=t:(e.memoizedState=n,e=Ac(),bl.lanes|=e,fc|=e,n)}function Pi(e,t,n,r){return Xn(n,t)?n:null!==fl.current?(e=Ti(e,n,r),Xn(e,t)||(Po=!0),e):0===(42&yl)?(Po=!0,e.memoizedState=n):(e=Ac(),bl.lanes|=e,fc|=e,t)}function Li(e,t,n,r,a){var l=A.p;A.p=0!==l&&8>l?l:8;var i=z.T,o={};z.T=o,Bi(e,!1,t,n);try{var s=a(),c=z.S;if(null!==c&&c(o,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Fi(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(s,r),zc());else Fi(e,t,r,zc())}catch(u){Fi(e,t,{then:function(){},status:"rejected",reason:u},zc())}finally{A.p=l,z.T=i}}function Ni(){}function ji(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=_i(e).queue;Li(e,a,t,D,null===n?Ni:function(){return Oi(e),n(r)})}function _i(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:D,baseState:D,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:D},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vl,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Oi(e){Fi(e,_i(e).next.queue,{},zc())}function Mi(){return Ta(Yd)}function zi(){return Fl().memoizedState}function Ai(){return Fl().memoizedState}function Di(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=zc(),r=ll(t,e=al(n),n);return null!==r&&(Dc(r,t,n),il(r,t,n)),t={cache:Ma()},void(e.payload=t)}t=t.return}}function Ri(e,t,n){var r=zc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e)?Ui(t,n):null!==(n=jr(e,t,n,r))&&(Dc(n,e,r),Vi(n,t,r))}function Ii(e,t,n){Fi(e,t,n,zc())}function Fi(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))Ui(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,o=l(i,n);if(a.hasEagerState=!0,a.eagerState=o,Xn(o,i))return Nr(e,t,a,0),null===rc&&Lr(),!1}catch(s){}if(null!==(n=jr(e,t,a,r)))return Dc(n,e,r),Vi(n,t,r),!0}return!1}function Bi(e,t,n,r){if(r={lane:2,revertLane:Nu(),action:r,hasEagerState:!1,eagerState:null,next:null},Hi(e)){if(t)throw Error(i(479))}else null!==(t=jr(e,n,r,2))&&Dc(t,e,2)}function Hi(e){var t=e.alternate;return e===bl||null!==t&&t===bl}function Ui(e,t){kl=xl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Vi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}var Gi={readContext:Ta,use:Hl,useCallback:Nl,useContext:Nl,useEffect:Nl,useImperativeHandle:Nl,useLayoutEffect:Nl,useInsertionEffect:Nl,useMemo:Nl,useReducer:Nl,useRef:Nl,useState:Nl,useDebugValue:Nl,useDeferredValue:Nl,useTransition:Nl,useSyncExternalStore:Nl,useId:Nl,useHostTransitionStatus:Nl,useFormState:Nl,useActionState:Nl,useOptimistic:Nl,useMemoCache:Nl,useCacheRefresh:Nl},Wi={readContext:Ta,use:Hl,useCallback:function(e,t){return Il().memoizedState=[e,void 0===t?null:t],e},useContext:Ta,useEffect:vi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,hi(4194308,4,Si.bind(null,t,e),n)},useLayoutEffect:function(e,t){return hi(4194308,4,e,t)},useInsertionEffect:function(e,t){hi(4,2,e,t)},useMemo:function(e,t){var n=Il();t=void 0===t?null:t;var r=e();if(El){pe(!0);try{e()}finally{pe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Il();if(void 0!==n){var a=n(t);if(El){pe(!0);try{n(t)}finally{pe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Ri.bind(null,bl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Il().memoizedState=e},useState:function(e){var t=(e=Zl(e)).queue,n=Ii.bind(null,bl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ki,useDeferredValue:function(e,t){return Ti(Il(),e,t)},useTransition:function(){var e=Zl(!1);return e=Li.bind(null,bl,e.queue,!0,!1),Il().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=bl,a=Il();if(la){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===rc)throw Error(i(349));0!==(124&lc)||Yl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,vi(Ql.bind(null,r,l,e),[e]),r.flags|=2048,fi(9,{destroy:void 0,resource:void 0},Xl.bind(null,r,l,n,t),null),n},useId:function(){var e=Il(),t=rc.identifierPrefix;if(la){var n=Jr;t="\xab"+t+"R"+(n=(Kr&~(1<<32-fe(Kr)-1)).toString(32)+n),0<(n=Cl++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Ll++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Mi,useFormState:si,useActionState:si,useOptimistic:function(e){var t=Il();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bi.bind(null,bl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ul,useCacheRefresh:function(){return Il().memoizedState=Di.bind(null,bl)}},qi={readContext:Ta,use:Hl,useCallback:Ei,useContext:Ta,useEffect:yi,useImperativeHandle:xi,useInsertionEffect:bi,useLayoutEffect:wi,useMemo:Ci,useReducer:Gl,useRef:mi,useState:function(){return Gl(Vl)},useDebugValue:ki,useDeferredValue:function(e,t){return Pi(Fl(),wl.memoizedState,e,t)},useTransition:function(){var e=Gl(Vl)[0],t=Fl().memoizedState;return["boolean"===typeof e?e:Bl(e),t]},useSyncExternalStore:$l,useId:zi,useHostTransitionStatus:Mi,useFormState:ci,useActionState:ci,useOptimistic:function(e,t){return ei(Fl(),0,e,t)},useMemoCache:Ul,useCacheRefresh:Ai},$i={readContext:Ta,use:Hl,useCallback:Ei,useContext:Ta,useEffect:yi,useImperativeHandle:xi,useInsertionEffect:bi,useLayoutEffect:wi,useMemo:Ci,useReducer:ql,useRef:mi,useState:function(){return ql(Vl)},useDebugValue:ki,useDeferredValue:function(e,t){var n=Fl();return null===wl?Ti(n,e,t):Pi(n,wl.memoizedState,e,t)},useTransition:function(){var e=ql(Vl)[0],t=Fl().memoizedState;return["boolean"===typeof e?e:Bl(e),t]},useSyncExternalStore:$l,useId:zi,useHostTransitionStatus:Mi,useFormState:pi,useActionState:pi,useOptimistic:function(e,t){var n=Fl();return null!==wl?ei(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ul,useCacheRefresh:Ai},Yi=null,Xi=0;function Qi(e){var t=Xi;return Xi+=1,null===Yi&&(Yi=[]),Ka(Yi,e,t)}function Ki(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Ji(e,t){if(t.$$typeof===f)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Zi(e){return(0,e._init)(e._payload)}function eo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Ir(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ur(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function c(e,t,n,r){var l=n.type;return l===g?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===T&&Zi(l)===t.type)?(Ki(t=a(t,n.props),n),t.return=e,t):(Ki(t=Br(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Hr(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function p(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Ur(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case m:return Ki(n=Br(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Vr(t,e.mode,n)).return=e,t;case T:return p(e,t=(0,t._init)(t._payload),n)}if(M(t)||j(t))return(t=Hr(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return p(e,Qi(t),n);if(t.$$typeof===S)return p(e,Pa(e,t),n);Ji(e,t)}return null}function f(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case m:return n.key===a?c(e,t,n,r):null;case h:return n.key===a?u(e,t,n,r):null;case T:return f(e,t,n=(a=n._init)(n._payload),r)}if(M(n)||j(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return f(e,t,Qi(n),r);if(n.$$typeof===S)return f(e,t,Pa(e,n),r);Ji(e,n)}return null}function v(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return v(e,t,n,r=(0,r._init)(r._payload),a)}if(M(r)||j(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return v(e,t,n,Qi(r),a);if(r.$$typeof===S)return v(e,t,n,Pa(t,r),a);Ji(t,r)}return null}function y(s,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case m:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===g){if(7===c.tag){n(s,c.sibling),(d=a(c,u.props.children)).return=s,s=d;break e}}else if(c.elementType===b||"object"===typeof b&&null!==b&&b.$$typeof===T&&Zi(b)===c.type){n(s,c.sibling),Ki(d=a(c,u.props),u),d.return=s,s=d;break e}n(s,c);break}t(s,c),c=c.sibling}u.type===g?((d=Hr(u.props.children,s.mode,d,u.key)).return=s,s=d):(Ki(d=Br(u.type,u.key,u.props,null,s.mode,d),u),d.return=s,s=d)}return o(s);case h:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(s,c.sibling),(d=a(c,u.children||[])).return=s,s=d;break e}n(s,c);break}t(s,c),c=c.sibling}(d=Vr(u,s.mode,d)).return=s,s=d}return o(s);case T:return y(s,c,u=(b=u._init)(u._payload),d)}if(M(u))return function(a,i,o,s){for(var c=null,u=null,d=i,m=i=0,h=null;null!==d&&m<o.length;m++){d.index>m?(h=d,d=null):h=d.sibling;var g=f(a,d,o[m],s);if(null===g){null===d&&(d=h);break}e&&d&&null===g.alternate&&t(a,d),i=l(g,i,m),null===u?c=g:u.sibling=g,u=g,d=h}if(m===o.length)return n(a,d),la&&Zr(a,m),c;if(null===d){for(;m<o.length;m++)null!==(d=p(a,o[m],s))&&(i=l(d,i,m),null===u?c=d:u.sibling=d,u=d);return la&&Zr(a,m),c}for(d=r(d);m<o.length;m++)null!==(h=v(d,a,m,o[m],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?m:h.key),i=l(h,i,m),null===u?c=h:u.sibling=h,u=h);return e&&d.forEach(function(e){return t(a,e)}),la&&Zr(a,m),c}(s,c,u,d);if(j(u)){if("function"!==typeof(b=j(u)))throw Error(i(150));return function(a,o,s,c){if(null==s)throw Error(i(151));for(var u=null,d=null,m=o,h=o=0,g=null,y=s.next();null!==m&&!y.done;h++,y=s.next()){m.index>h?(g=m,m=null):g=m.sibling;var b=f(a,m,y.value,c);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(a,m),o=l(b,o,h),null===d?u=b:d.sibling=b,d=b,m=g}if(y.done)return n(a,m),la&&Zr(a,h),u;if(null===m){for(;!y.done;h++,y=s.next())null!==(y=p(a,y.value,c))&&(o=l(y,o,h),null===d?u=y:d.sibling=y,d=y);return la&&Zr(a,h),u}for(m=r(m);!y.done;h++,y=s.next())null!==(y=v(m,a,h,y.value,c))&&(e&&null!==y.alternate&&m.delete(null===y.key?h:y.key),o=l(y,o,h),null===d?u=y:d.sibling=y,d=y);return e&&m.forEach(function(e){return t(a,e)}),la&&Zr(a,h),u}(s,c,u=b.call(u),d)}if("function"===typeof u.then)return y(s,c,Qi(u),d);if(u.$$typeof===S)return y(s,c,Pa(s,u),d);Ji(s,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(s,c.sibling),(d=a(c,u)).return=s,s=d):(n(s,c),(d=Ur(u,s.mode,d)).return=s,s=d),o(s)):n(s,c)}return function(e,t,n,r){try{Xi=0;var a=y(e,t,n,r);return Yi=null,a}catch(i){if(i===Wa||i===$a)throw i;var l=Dr(29,i,null,e.mode);return l.lanes=r,l.return=e,l}}}var to=eo(!0),no=eo(!1),ro=F(null),ao=null;function lo(e){var t=e.alternate;H(co,1&co.current),H(ro,e),null===ao&&(null===t||null!==fl.current||null!==t.memoizedState)&&(ao=e)}function io(e){if(22===e.tag){if(H(co,co.current),H(ro,e),null===ao){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ao=e)}}else oo()}function oo(){H(co,co.current),H(ro,ro.current)}function so(e){B(ro),ao===e&&(ao=null),B(co)}var co=F(0);function uo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||gd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function po(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:p({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var fo={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=zc(),a=al(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Dc(t,e,r),il(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=zc(),a=al(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Dc(t,e,r),il(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=zc(),r=al(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ll(e,r,n))&&(Dc(t,e,n),il(t,e,n))}};function mo(e,t,n,r,a,l,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!Qn(n,r)||!Qn(a,l))}function ho(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&fo.enqueueReplaceState(t,t.state,null)}function go(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=p({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var vo="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yo(e){vo(e)}function bo(e){console.error(e)}function wo(e){vo(e)}function So(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function xo(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function ko(e,t,n){return(n=al(n)).tag=3,n.payload={element:null},n.callback=function(){So(e,t)},n}function Eo(e){return(e=al(e)).tag=3,e}function Co(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){xo(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){xo(t,n,r),"function"!==typeof a&&(null===Ec?Ec=new Set([this]):Ec.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var To=Error(i(461)),Po=!1;function Lo(e,t,n,r){t.child=null===e?no(t,null,n,r):to(t,e.child,n,r)}function No(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var i={};for(var o in r)"ref"!==o&&(i[o]=r[o])}else i=r;return Ca(t),r=_l(e,t,n,i,l,a),o=Al(),null===e||Po?(la&&o&&ta(t),t.flags|=1,Lo(e,t,r,a),t.child):(Dl(e,t,a),Xo(e,t,a))}function jo(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Rr(l)||void 0!==l.defaultProps||null!==n.compare?((e=Br(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,_o(e,t,l,r,a))}if(l=e.child,!Qo(e,a)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:Qn)(i,r)&&e.ref===t.ref)return Xo(e,t,a)}return t.flags|=1,(e=Ir(l,r)).ref=t.ref,e.return=t,t.child=e}function _o(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Qn(l,r)&&e.ref===t.ref){if(Po=!1,t.pendingProps=r=l,!Qo(e,a))return t.lanes=e.lanes,Xo(e,t,a);0!==(131072&e.flags)&&(Po=!0)}}return Ao(e,t,n,r,a)}function Oo(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Mo(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Mo(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Va(0,null!==l?l.cachePool:null),null!==l?hl(t,l):gl(),io(t)}else null!==l?(Va(0,l.cachePool),hl(t,l),oo(),t.memoizedState=null):(null!==e&&Va(0,null),gl(),oo());return Lo(e,t,a,n),t.child}function Mo(e,t,n,r){var a=Ua();return a=null===a?null:{parent:Oa._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Va(0,null),gl(),io(t),null!==e&&ka(e,t,r,!0),null}function zo(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ao(e,t,n,r,a){return Ca(t),n=_l(e,t,n,r,void 0,a),r=Al(),null===e||Po?(la&&r&&ta(t),t.flags|=1,Lo(e,t,n,a),t.child):(Dl(e,t,a),Xo(e,t,a))}function Do(e,t,n,r,a,l){return Ca(t),t.updateQueue=null,n=Ml(t,r,n,a),Ol(e),r=Al(),null===e||Po?(la&&r&&ta(t),t.flags|=1,Lo(e,t,n,l),t.child):(Dl(e,t,l),Xo(e,t,l))}function Ro(e,t,n,r,a){if(Ca(t),null===t.stateNode){var l=zr,i=n.contextType;"object"===typeof i&&null!==i&&(l=Ta(i)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=fo,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},nl(t),i=n.contextType,l.context="object"===typeof i&&null!==i?Ta(i):zr,l.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(po(t,n,i,r),l.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof l.getSnapshotBeforeUpdate||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||(i=l.state,"function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),i!==l.state&&fo.enqueueReplaceState(l,l.state,null),ul(t,r,l,a),cl(),l.state=t.memoizedState),"function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var o=t.memoizedProps,s=go(n,o);l.props=s;var c=l.context,u=n.contextType;i=zr,"object"===typeof u&&null!==u&&(i=Ta(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof l.getSnapshotBeforeUpdate,o=t.pendingProps!==o,u||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o||c!==i)&&ho(t,l,r,i),tl=!1;var p=t.memoizedState;l.state=p,ul(t,r,l,a),cl(),c=t.memoizedState,o||p!==c||tl?("function"===typeof d&&(po(t,n,d,r),c=t.memoizedState),(s=tl||mo(t,n,s,r,p,c,i))?(u||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),l.props=r,l.state=c,l.context=i,r=s):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,rl(e,t),u=go(n,i=t.memoizedProps),l.props=u,d=t.pendingProps,p=l.context,c=n.contextType,s=zr,"object"===typeof c&&null!==c&&(s=Ta(c)),(c="function"===typeof(o=n.getDerivedStateFromProps)||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||p!==s)&&ho(t,l,r,s),tl=!1,p=t.memoizedState,l.state=p,ul(t,r,l,a),cl();var f=t.memoizedState;i!==d||p!==f||tl||null!==e&&null!==e.dependencies&&Ea(e.dependencies)?("function"===typeof o&&(po(t,n,o,r),f=t.memoizedState),(u=tl||mo(t,n,u,r,p,f,s)||null!==e&&null!==e.dependencies&&Ea(e.dependencies))?(c||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,f,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,f,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=f),l.props=r,l.state=f,l.context=s,r=u):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,zo(e,t),r=0!==(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=to(t,e.child,null,a),t.child=to(t,null,n,a)):Lo(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Xo(e,t,a),e}function Io(e,t,n,r){return fa(),t.flags|=256,Lo(e,t,n,r),t.child}var Fo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Bo(e){return{baseLanes:e,cachePool:Ga()}}function Ho(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=gc),e}function Uo(e,t,n){var r,a=t.pendingProps,l=!1,o=0!==(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&0!==(2&co.current)),r&&(l=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(la){if(l?lo(t):oo(),la){var s,c=aa;if(s=c){e:{for(s=c,c=oa;8!==s.nodeType;){if(!c){c=null;break e}if(null===(s=vd(s.nextSibling))){c=null;break e}}c=s}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Qr?{id:Kr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(s=Dr(18,null,null,0)).stateNode=c,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ca(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return gd(c)?t.lanes=32:t.lanes=536870912,null;so(t)}return c=a.children,a=a.fallback,l?(oo(),c=Go({mode:"hidden",children:c},l=t.mode),a=Hr(a,l,n,null),c.return=t,a.return=t,c.sibling=a,t.child=c,(l=t.child).memoizedState=Bo(n),l.childLanes=Ho(e,r,n),t.memoizedState=Fo,a):(lo(t),Vo(t,c))}if(null!==(s=e.memoizedState)&&null!==(c=s.dehydrated)){if(o)256&t.flags?(lo(t),t.flags&=-257,t=Wo(e,t,n)):null!==t.memoizedState?(oo(),t.child=e.child,t.flags|=128,t=null):(oo(),l=a.fallback,c=t.mode,a=Go({mode:"visible",children:a.children},c),(l=Hr(l,c,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,to(t,e.child,null,n),(a=t.child).memoizedState=Bo(n),a.childLanes=Ho(e,r,n),t.memoizedState=Fo,t=l);else if(lo(t),gd(c)){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(a=Error(i(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=Wo(e,t,n)}else if(Po||ka(e,t,n,!1),r=0!==(n&e.childLanes),Po||r){if(null!==(r=rc)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Le(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,_r(e,a),Dc(r,e,a),To;"$?"===c.data||$c(),t=Wo(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=vd(c.nextSibling),ra=t,la=!0,ia=null,oa=!1,null!==e&&(Yr[Xr++]=Kr,Yr[Xr++]=Jr,Yr[Xr++]=Qr,Kr=e.id,Jr=e.overflow,Qr=t),(t=Vo(t,a.children)).flags|=4096);return t}return l?(oo(),l=a.fallback,c=t.mode,u=(s=e.child).sibling,(a=Ir(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==u?l=Ir(u,l):(l=Hr(l,c,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(c=e.child.memoizedState)?c=Bo(n):(null!==(s=c.cachePool)?(u=Oa._currentValue,s=s.parent!==u?{parent:u,pool:u}:s):s=Ga(),c={baseLanes:c.baseLanes|n,cachePool:s}),l.memoizedState=c,l.childLanes=Ho(e,r,n),t.memoizedState=Fo,a):(lo(t),e=(n=e.child).sibling,(n=Ir(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Vo(e,t){return(t=Go({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Go(e,t){return(e=Dr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Wo(e,t,n){return to(t,e.child,null,n),(e=Vo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function qo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Sa(e.return,t,n)}function $o(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Yo(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(Lo(e,t,r.children,n),0!==(2&(r=co.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qo(e,n,t);else if(19===e.tag)qo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(H(co,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===uo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$o(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===uo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$o(t,!0,n,null,l);break;case"together":$o(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),fc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ka(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Ir(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ir(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Qo(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ea(e))}function Ko(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Po=!0;else{if(!Qo(e,n)&&0===(128&t.flags))return Po=!1,function(e,t,n){switch(t.tag){case 3:q(t,t.stateNode.containerInfo),ba(0,Oa,e.memoizedState.cache),fa();break;case 27:case 5:Y(t);break;case 4:q(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(lo(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Uo(e,t,n):(lo(t),null!==(e=Xo(e,t,n))?e.sibling:null);lo(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(ka(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Yo(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),H(co,co.current),r)break;return null;case 22:case 23:return t.lanes=0,Oo(e,t,n);case 24:ba(0,Oa,e.memoizedState.cache)}return Xo(e,t,n)}(e,t,n);Po=0!==(131072&e.flags)}else Po=!1,la&&0!==(1048576&t.flags)&&ea(t,$r,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===x){t.tag=11,t=No(null,t,r,e,n);break e}if(a===C){t.tag=14,t=jo(null,t,r,e,n);break e}}throw t=O(r)||r,Error(i(306,t,""))}Rr(r)?(e=go(r,e),t.tag=1,t=Ro(null,t,r,e,n)):(t.tag=0,t=Ao(null,t,r,e,n))}return t;case 0:return Ao(e,t,t.type,t.pendingProps,n);case 1:return Ro(e,t,r=t.type,a=go(r,t.pendingProps),n);case 3:e:{if(q(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,rl(e,t),ul(t,r,null,n);var o=t.memoizedState;if(r=o.cache,ba(0,Oa,r),r!==l.cache&&xa(t,[Oa],n,!0),cl(),r=o.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Io(e,t,r,n);break e}if(r!==a){ha(a=Er(Error(i(424)),t)),t=Io(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=vd(e.firstChild),ra=t,la=!0,ia=null,oa=!0,n=no(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(fa(),r===a){t=Xo(e,t,n);break e}Lo(e,t,r,n)}t=t.child}return t;case 26:return zo(e,t),null===e?(n=Ld(t.type,null,t.pendingProps,null))?t.memoizedState=n:la||(n=t.type,e=t.pendingProps,(r=rd(G.current).createElement(n))[Oe]=t,r[Me]=e,ed(r,n,e),We(r),t.stateNode=r):t.memoizedState=Ld(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Y(t),null===e&&la&&(r=t.stateNode=wd(t.type,t.pendingProps,G.current),ra=t,oa=!0,a=aa,fd(t.type)?(yd=a,aa=vd(r.firstChild)):aa=a),Lo(e,t,t.pendingProps.children,n),zo(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&la&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Fe])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=vd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,oa))?(t.stateNode=r,ra=t,aa=vd(r.firstChild),oa=!1,a=!0):a=!1),a||ca(t)),Y(t),a=t.type,l=t.pendingProps,o=null!==e?e.memoizedProps:null,r=l.children,id(a,l)?r=null:null!==o&&id(a,o)&&(t.flags|=32),null!==t.memoizedState&&(a=_l(e,t,zl,null,null,n),Yd._currentValue=a),zo(e,t),Lo(e,t,r,n),t.child;case 6:return null===e&&la&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=vd(e.nextSibling)))return null}return e}(n,t.pendingProps,oa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ca(t)),null;case 13:return Uo(e,t,n);case 4:return q(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=to(t,null,r,n):Lo(e,t,r,n),t.child;case 11:return No(e,t,t.type,t.pendingProps,n);case 7:return Lo(e,t,t.pendingProps,n),t.child;case 8:case 12:return Lo(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Lo(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ca(t),r=r(a=Ta(a)),t.flags|=1,Lo(e,t,r,n),t.child;case 14:return jo(e,t,t.type,t.pendingProps,n);case 15:return _o(e,t,t.type,t.pendingProps,n);case 19:return Yo(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Go(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Ir(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Oo(e,t,n);case 24:return Ca(t),r=Ta(Oa),null===e?(null===(a=Ua())&&(a=rc,l=Ma(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},nl(t),ba(0,Oa,a)):(0!==(e.lanes&n)&&(rl(e,t),ul(t,null,null,n),cl()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Oa,r)):(r=l.cache,ba(0,Oa,r),r!==a.cache&&xa(t,[Oa],n,!0))),Lo(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function Jo(e){e.flags|=4}function Zo(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Hd(t)){if(null!==(t=ro.current)&&((4194048&lc)===lc?null!==ao:(62914560&lc)!==lc&&0===(536870912&lc)||t!==ao))throw Ja=Ya,qa;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?ke():536870912,e.lanes|=t,vc|=t)}function ts(e,t){if(!la)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(Oa),$(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(pa(t)?Jo(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,ma())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Jo(t),null!==n?(ns(t),Zo(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Jo(t),ns(t),Zo(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Jo(t),ns(t),t.flags&=-16777217),null;case 27:X(t),n=G.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Jo(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}e=U.current,pa(t)?ua(t):(e=wd(a,r,n),t.stateNode=e,Jo(t))}return ns(t),null;case 5:if(X(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Jo(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return ns(t),null}if(e=U.current,pa(t))ua(t);else{switch(a=rd(G.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Oe]=t,e[Me]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Jo(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Jo(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=G.current,pa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Oe]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Qu(e.nodeValue,n)))||ca(t)}else(e=rd(e).createTextNode(r))[Oe]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=pa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[Oe]=t}else fa(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),a=!1}else a=ma(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(so(t),t):(so(t),null)}if(so(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return $(),null===e&&Bu(t.stateNode.containerInfo),ns(t),null;case 10:return wa(t.type),ns(t),null;case 19:if(B(co),null===(a=t.memoizedState))return ns(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)ts(a,!1);else{if(0!==pc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=uo(e))){for(t.flags|=128,ts(a,!1),e=l.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Fr(n,e),n=n.sibling;return H(co,1&co.current|2),t.child}e=e.sibling}null!==a.tail&&te()>xc&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=uo(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!la)return ns(t),null}else 2*te()-a.renderingStartTime>xc&&536870912!==n&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=co.current,H(co,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return so(t),vl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&B(Ha),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(Oa),ns(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function as(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(Oa),$(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return X(t),null;case 13:if(so(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));fa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return B(co),null;case 4:return $(),null;case 10:return wa(t.type),null;case 22:case 23:return so(t),vl(),null!==e&&B(Ha),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(Oa),null;default:return null}}function ls(e,t){switch(na(t),t.tag){case 3:wa(Oa),$();break;case 26:case 27:case 5:X(t);break;case 4:$();break;case 13:so(t);break;case 19:B(co);break;case 10:wa(t.type);break;case 22:case 23:so(t),vl(),null!==e&&B(Ha);break;case 24:wa(Oa)}}function is(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,i=n.inst;r=l(),i.destroy=r}n=n.next}while(n!==a)}}catch(o){uu(t,t.return,o)}}function os(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var i=r.inst,o=i.destroy;if(void 0!==o){i.destroy=void 0,a=t;var s=n,c=o;try{c()}catch(u){uu(a,s,u)}}}r=r.next}while(r!==l)}}catch(u){uu(t,t.return,u)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{pl(t,n)}catch(r){uu(e,e.return,r)}}}function cs(e,t,n){n.props=go(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){uu(e,t,r)}}function us(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){uu(e,t,a)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){uu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(l){uu(e,t,l)}else n.current=null}function ps(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){uu(e,e.return,a)}}function fs(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,o=null,s=null,c=null,u=null,d=null;for(m in n){var p=n[m];if(n.hasOwnProperty(m)&&null!=p)switch(m){case"checked":case"value":break;case"defaultValue":c=p;default:r.hasOwnProperty(m)||Ju(e,t,m,null,r,p)}}for(var f in r){var m=r[f];if(p=n[f],r.hasOwnProperty(f)&&(null!=m||null!=p))switch(f){case"type":l=m;break;case"name":a=m;break;case"checked":u=m;break;case"defaultChecked":d=m;break;case"value":o=m;break;case"defaultValue":s=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(i(137,t));break;default:m!==p&&Ju(e,t,f,m,r,p)}}return void gt(e,o,s,c,u,d,l,a);case"select":for(l in m=o=s=f=null,n)if(c=n[l],n.hasOwnProperty(l)&&null!=c)switch(l){case"value":break;case"multiple":m=c;default:r.hasOwnProperty(l)||Ju(e,t,l,null,r,c)}for(a in r)if(l=r[a],c=n[a],r.hasOwnProperty(a)&&(null!=l||null!=c))switch(a){case"value":f=l;break;case"defaultValue":s=l;break;case"multiple":o=l;default:l!==c&&Ju(e,t,a,l,r,c)}return t=s,n=o,r=m,void(null!=f?bt(e,!!n,f,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(s in m=f=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Ju(e,t,s,null,r,a)}for(o in r)if(a=r[o],l=n[o],r.hasOwnProperty(o)&&(null!=a||null!=l))switch(o){case"value":f=a;break;case"defaultValue":m=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==l&&Ju(e,t,o,a,r,l)}return void wt(e,f,m);case"option":for(var h in n)if(f=n[h],n.hasOwnProperty(h)&&null!=f&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else Ju(e,t,h,null,r,f);for(c in r)if(f=r[c],m=n[c],r.hasOwnProperty(c)&&f!==m&&(null!=f||null!=m))if("selected"===c)e.selected=f&&"function"!==typeof f&&"symbol"!==typeof f;else Ju(e,t,c,f,r,m);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)f=n[g],n.hasOwnProperty(g)&&null!=f&&!r.hasOwnProperty(g)&&Ju(e,t,g,null,r,f);for(u in r)if(f=r[u],m=n[u],r.hasOwnProperty(u)&&f!==m&&(null!=f||null!=m))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(i(137,t));break;default:Ju(e,t,u,f,r,m)}return;default:if(Tt(t)){for(var v in n)f=n[v],n.hasOwnProperty(v)&&void 0!==f&&!r.hasOwnProperty(v)&&Zu(e,t,v,void 0,r,f);for(d in r)f=r[d],m=n[d],!r.hasOwnProperty(d)||f===m||void 0===f&&void 0===m||Zu(e,t,d,f,r,m);return}}for(var y in n)f=n[y],n.hasOwnProperty(y)&&null!=f&&!r.hasOwnProperty(y)&&Ju(e,t,y,null,r,f);for(p in r)f=r[p],m=n[p],!r.hasOwnProperty(p)||f===m||null==f&&null==m||Ju(e,t,p,f,r,m)}(r,e.type,n,t),r[Me]=t}catch(a){uu(e,e.return,a)}}function ms(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&fd(e.type)||4===e.tag}function hs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ms(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&fd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function gs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Ku));else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(gs(e,t,n),e=e.sibling;null!==e;)gs(e,t,n),e=e.sibling}function vs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&fd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(vs(e,t,n),e=e.sibling;null!==e;)vs(e,t,n),e=e.sibling}function ys(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Oe]=e,t[Me]=n}catch(l){uu(e,e.return,l)}}var bs=!1,ws=!1,Ss=!1,xs="function"===typeof WeakSet?WeakSet:Set,ks=null;function Es(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Rs(e,n),4&r&&is(5,n);break;case 1:if(Rs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){uu(n,n.return,i)}else{var a=go(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){uu(n,n.return,o)}}64&r&&ss(n),512&r&&us(n,n.return);break;case 3:if(Rs(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{pl(e,t)}catch(i){uu(n,n.return,i)}}break;case 27:null===t&&4&r&&ys(n);case 26:case 5:Rs(e,n),null===t&&4&r&&ps(n),512&r&&us(n,n.return);break;case 12:Rs(e,n);break;case 13:Rs(e,n),4&r&&js(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=mu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||bs)){t=null!==t&&null!==t.memoizedState||ws,a=bs;var l=ws;bs=r,(ws=t)&&!l?Fs(e,n,0!==(8772&n.subtreeFlags)):Rs(e,n),bs=a,ws=l}break;case 30:break;default:Rs(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Be(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ts=null,Ps=!1;function Ls(e,t,n){for(n=n.child;null!==n;)Ns(e,t,n),n=n.sibling}function Ns(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(l){}switch(n.tag){case 26:ws||ds(n,t),Ls(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ws||ds(n,t);var r=Ts,a=Ps;fd(n.type)&&(Ts=n.stateNode,Ps=!1),Ls(e,t,n),Sd(n.stateNode),Ts=r,Ps=a;break;case 5:ws||ds(n,t);case 6:if(r=Ts,a=Ps,Ts=null,Ls(e,t,n),Ps=a,null!==(Ts=r))if(Ps)try{(9===Ts.nodeType?Ts.body:"HTML"===Ts.nodeName?Ts.ownerDocument.body:Ts).removeChild(n.stateNode)}catch(i){uu(n,t,i)}else try{Ts.removeChild(n.stateNode)}catch(i){uu(n,t,i)}break;case 18:null!==Ts&&(Ps?(md(9===(e=Ts).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Pp(e)):md(Ts,n.stateNode));break;case 4:r=Ts,a=Ps,Ts=n.stateNode.containerInfo,Ps=!0,Ls(e,t,n),Ts=r,Ps=a;break;case 0:case 11:case 14:case 15:ws||os(2,n,t),ws||os(4,n,t),Ls(e,t,n);break;case 1:ws||(ds(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&cs(n,t,r)),Ls(e,t,n);break;case 21:Ls(e,t,n);break;case 22:ws=(r=ws)||null!==n.memoizedState,Ls(e,t,n),ws=r;break;default:Ls(e,t,n)}}function js(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Pp(e)}catch(n){uu(t,t.return,n)}}function _s(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new xs),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new xs),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=hu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Os(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 27:if(fd(s.type)){Ts=s.stateNode,Ps=!1;break e}break;case 5:Ts=s.stateNode,Ps=!1;break e;case 3:case 4:Ts=s.stateNode.containerInfo,Ps=!0;break e}s=s.return}if(null===Ts)throw Error(i(160));Ns(l,o,a),Ts=null,Ps=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)zs(t,e),t=t.sibling}var Ms=null;function zs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Os(t,e),As(e),4&r&&(os(3,e,e.return),is(3,e),os(5,e,e.return));break;case 1:Os(t,e),As(e),512&r&&(ws||null===n||ds(n,n.return)),64&r&&bs&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Ms;if(Os(t,e),As(e),512&r&&(ws||null===n||ds(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[Fe]||l[Oe]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),ed(l,r,n),l[Oe]=e,We(l),r=l;break e;case"link":var o=Fd("link","href",a).get(r+(n.href||""));if(o)for(var s=0;s<o.length;s++)if((l=o[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){o.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(o=Fd("meta","content",a).get(r+(n.content||"")))for(s=0;s<o.length;s++)if((l=o[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){o.splice(s,1);break t}ed(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(i(468,r))}l[Oe]=e,We(l),r=l}e.stateNode=r}else Bd(a,e.type,e.stateNode);else e.stateNode=zd(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?Bd(a,e.type,e.stateNode):zd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&fs(e,e.memoizedProps,n.memoizedProps)}break;case 27:Os(t,e),As(e),512&r&&(ws||null===n||ds(n,n.return)),null!==n&&4&r&&fs(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Os(t,e),As(e),512&r&&(ws||null===n||ds(n,n.return)),32&e.flags){a=e.stateNode;try{xt(a,"")}catch(m){uu(e,e.return,m)}}4&r&&null!=e.stateNode&&fs(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(Ss=!0);break;case 6:if(Os(t,e),As(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(m){uu(e,e.return,m)}}break;case 3:if(Id=null,a=Ms,Ms=Ed(t.containerInfo),Os(t,e),Ms=a,As(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Pp(t.containerInfo)}catch(m){uu(e,e.return,m)}Ss&&(Ss=!1,Ds(e));break;case 4:r=Ms,Ms=Ed(e.stateNode.containerInfo),Os(t,e),As(e),Ms=r;break;case 12:default:Os(t,e),As(e);break;case 13:Os(t,e),As(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(Sc=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r)));break;case 22:a=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=bs,d=ws;if(bs=u||a,ws=d||c,Os(t,e),ws=d,bs=u,As(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||c||bs||ws||Is(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(l=c.stateNode,a)"function"===typeof(o=l.style).setProperty?o.setProperty("display","none","important"):o.display="none";else{s=c.stateNode;var p=c.memoizedProps.style,f=void 0!==p&&null!==p&&p.hasOwnProperty("display")?p.display:null;s.style.display=null==f||"boolean"===typeof f?"":(""+f).trim()}}catch(m){uu(c,c.return,m)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=a?"":c.memoizedProps}catch(m){uu(c,c.return,m)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,_s(e,n))));break;case 19:Os(t,e),As(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r)));case 30:case 21:}}function As(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(ms(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;vs(e,hs(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(xt(l,""),n.flags&=-33),vs(e,hs(e),l);break;case 3:case 4:var o=n.stateNode.containerInfo;gs(e,hs(e),o);break;default:throw Error(i(161))}}catch(s){uu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ds(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ds(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Rs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Es(e,t.alternate,t),t=t.sibling}function Is(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:os(4,t,t.return),Is(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&cs(t,t.return,n),Is(t);break;case 27:Sd(t.stateNode);case 26:case 5:ds(t,t.return),Is(t);break;case 22:null===t.memoizedState&&Is(t);break;default:Is(t)}e=e.sibling}}function Fs(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,i=l.flags;switch(l.tag){case 0:case 11:case 15:Fs(a,l,n),is(4,l);break;case 1:if(Fs(a,l,n),"function"===typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){uu(r,r.return,c)}if(null!==(a=(r=l).updateQueue)){var o=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)dl(s[a],o)}catch(c){uu(r,r.return,c)}}n&&64&i&&ss(l),us(l,l.return);break;case 27:ys(l);case 26:case 5:Fs(a,l,n),n&&null===r&&4&i&&ps(l),us(l,l.return);break;case 12:Fs(a,l,n);break;case 13:Fs(a,l,n),n&&4&i&&js(a,l);break;case 22:null===l.memoizedState&&Fs(a,l,n),us(l,l.return);break;case 30:break;default:Fs(a,l,n)}t=t.sibling}}function Bs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&za(n))}function Hs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e))}function Us(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Vs(e,t,n,r),t=t.sibling}function Vs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Us(e,t,n,r),2048&a&&is(9,t);break;case 1:case 13:default:Us(e,t,n,r);break;case 3:Us(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e)));break;case 12:if(2048&a){Us(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,i=l.id,o=l.onPostCommit;"function"===typeof o&&o(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){uu(t,t.return,s)}}else Us(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,i=t.alternate,null!==t.memoizedState?2&l._visibility?Us(e,t,n,r):Ws(e,t):2&l._visibility?Us(e,t,n,r):(l._visibility|=2,Gs(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Bs(i,t);break;case 24:Us(e,t,n,r),2048&a&&Hs(t.alternate,t)}}function Gs(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,i=t,o=n,s=r,c=i.flags;switch(i.tag){case 0:case 11:case 15:Gs(l,i,o,s,a),is(8,i);break;case 23:break;case 22:var u=i.stateNode;null!==i.memoizedState?2&u._visibility?Gs(l,i,o,s,a):Ws(l,i):(u._visibility|=2,Gs(l,i,o,s,a)),a&&2048&c&&Bs(i.alternate,i);break;case 24:Gs(l,i,o,s,a),a&&2048&c&&Hs(i.alternate,i);break;default:Gs(l,i,o,s,a)}t=t.sibling}}function Ws(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Ws(n,r),2048&a&&Bs(r.alternate,r);break;case 24:Ws(n,r),2048&a&&Hs(r.alternate,r);break;default:Ws(n,r)}t=t.sibling}}var qs=8192;function $s(e){if(e.subtreeFlags&qs)for(e=e.child;null!==e;)Ys(e),e=e.sibling}function Ys(e){switch(e.tag){case 26:$s(e),e.flags&qs&&null!==e.memoizedState&&function(e,t,n){if(null===Ud)throw Error(i(475));var r=Ud;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Nd(n.href),l=e.querySelector(jd(a));if(l)return null!==(e=l._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Gd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void We(l);l=e.ownerDocument||e,n=_d(n),(a=xd.get(a))&&Dd(n,a),We(l=l.createElement("link"));var o=l;o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),ed(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Gd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ms,e.memoizedState,e.memoizedProps);break;case 5:default:$s(e);break;case 3:case 4:var t=Ms;Ms=Ed(e.stateNode.containerInfo),$s(e),Ms=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=qs,qs=16777216,$s(e),qs=t):$s(e))}}function Xs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Qs(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ks=r,Zs(r,e)}Xs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ks(e),e=e.sibling}function Ks(e){switch(e.tag){case 0:case 11:case 15:Qs(e),2048&e.flags&&os(9,e,e.return);break;case 3:case 12:default:Qs(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Js(e)):Qs(e)}}function Js(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ks=r,Zs(r,e)}Xs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:os(8,t,t.return),Js(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Js(t));break;default:Js(t)}e=e.sibling}}function Zs(e,t){for(;null!==ks;){var n=ks;switch(n.tag){case 0:case 11:case 15:os(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:za(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,ks=r;else e:for(n=e;null!==ks;){var a=(r=ks).sibling,l=r.return;if(Cs(r),r===n){ks=null;break e}if(null!==a){a.return=l,ks=a;break e}ks=l}}}var ec={getCacheForType:function(e){var t=Ta(Oa),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tc="function"===typeof WeakMap?WeakMap:Map,nc=0,rc=null,ac=null,lc=0,ic=0,oc=null,sc=!1,cc=!1,uc=!1,dc=0,pc=0,fc=0,mc=0,hc=0,gc=0,vc=0,yc=null,bc=null,wc=!1,Sc=0,xc=1/0,kc=null,Ec=null,Cc=0,Tc=null,Pc=null,Lc=0,Nc=0,jc=null,_c=null,Oc=0,Mc=null;function zc(){if(0!==(2&nc)&&0!==lc)return lc&-lc;if(null!==z.T){return 0!==Ra?Ra:Nu()}return je()}function Ac(){0===gc&&(gc=0===(536870912&lc)||la?xe():536870912);var e=ro.current;return null!==e&&(e.flags|=32),gc}function Dc(e,t,n){(e!==rc||2!==ic&&9!==ic)&&null===e.cancelPendingCommit||(Vc(e,0),Bc(e,lc,gc,!1)),Ce(e,n),0!==(2&nc)&&e===rc||(e===rc&&(0===(2&nc)&&(mc|=n),4===pc&&Bc(e,lc,gc,!1)),xu(e))}function Rc(e,t,n){if(0!==(6&nc))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nc;nc|=2;var r=Wc(),a=qc();rc!==e||lc!==t?(kc=null,xc=te()+500,Vc(e,t)):cc=we(e,t);e:for(;;)try{if(0!==ic&&null!==ac){t=ac;var l=oc;t:switch(ic){case 1:ic=0,oc=null,Zc(e,t,l,1);break;case 2:case 9:if(Xa(l)){ic=0,oc=null,Jc(t);break}t=function(){2!==ic&&9!==ic||rc!==e||(ic=7),xu(e)},l.then(t,t);break e;case 3:ic=7;break e;case 4:ic=5;break e;case 7:Xa(l)?(ic=0,oc=null,Jc(t)):(ic=0,oc=null,Zc(e,t,l,7));break;case 5:var o=null;switch(ac.tag){case 26:o=ac.memoizedState;case 5:case 27:var s=ac;if(!o||Hd(o)){ic=0,oc=null;var c=s.sibling;if(null!==c)ac=c;else{var u=s.return;null!==u?(ac=u,eu(u)):ac=null}break t}}ic=0,oc=null,Zc(e,t,l,5);break;case 6:ic=0,oc=null,Zc(e,t,l,6);break;case 8:Uc(),pc=6;break e;default:throw Error(i(462))}}Qc();break}catch(d){Gc(e,d)}return ya=va=null,z.H=r,z.A=a,nc=n,null!==ac?0:(rc=null,lc=0,Lr(),pc)}(e,t):Yc(e,t,!0),l=r;;){if(0===a){cc&&!r&&Bc(e,t,0,!1);break}if(n=e.current.alternate,!l||Fc(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var o=0;else o=0!==(o=-536870913&e.pendingLanes)?o:536870912&o?536870912:0;if(0!==o){t=o;e:{var s=e;a=yc;var c=s.current.memoizedState.isDehydrated;if(c&&(Vc(s,o).flags|=256),2!==(o=Yc(s,o,!1))){if(uc&&!c){s.errorRecoveryDisabledLanes|=l,mc|=l,a=4;break e}l=bc,bc=a,null!==l&&(null===bc?bc=l:bc.push.apply(bc,l))}a=o}if(l=!1,2!==a)continue}}if(1===a){Vc(e,0),Bc(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Bc(r,t,gc,!sc);break e;case 2:bc=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=Sc+300-te())){if(Bc(r,t,gc,!sc),0!==be(r,0,!0))break e;r.timeoutHandle=sd(Ic.bind(null,r,n,bc,kc,wc,t,gc,mc,vc,sc,l,2,-0,0),a)}else Ic(r,n,bc,kc,wc,t,gc,mc,vc,sc,l,0,-0,0)}break}a=Yc(e,t,!1),l=!1}xu(e)}function Ic(e,t,n,r,a,l,o,s,c,u,d,p,f,m){if(e.timeoutHandle=-1,(8192&(p=t.subtreeFlags)||16785408===(16785408&p))&&(Ud={stylesheets:null,count:0,unsuspend:Vd},Ys(t),null!==(p=function(){if(null===Ud)throw Error(i(475));var e=Ud;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=p(nu.bind(null,e,t,l,n,r,a,o,s,c,d,1,f,m)),void Bc(e,l,o,!u);nu(e,t,l,n,r,a,o,s,c)}function Fc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Xn(l(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bc(e,t,n,r){t&=~hc,t&=~mc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-fe(a),i=1<<l;r[l]=-1,a&=~i}0!==n&&Te(e,n,t)}function Hc(){return 0!==(6&nc)||(ku(0,!1),!1)}function Uc(){if(null!==ac){if(0===ic)var e=ac.return;else ya=va=null,Rl(e=ac),Yi=null,Xi=0,e=ac;for(;null!==e;)ls(e.alternate,e),e=e.return;ac=null}}function Vc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,cd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Uc(),rc=e,ac=n=Ir(e.current,null),lc=t,ic=0,oc=null,sc=!1,cc=we(e,t),uc=!1,vc=gc=hc=mc=fc=pc=0,bc=yc=null,wc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-fe(r),l=1<<a;t|=e[a],r&=~l}return dc=t,Lr(),n}function Gc(e,t){bl=null,z.H=Gi,t===Wa||t===$a?(t=Za(),ic=3):t===qa?(t=Za(),ic=4):ic=t===To?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,oc=t,null===ac&&(pc=1,So(e,Er(t,e.current)))}function Wc(){var e=z.H;return z.H=Gi,null===e?Gi:e}function qc(){var e=z.A;return z.A=ec,e}function $c(){pc=4,sc||(4194048&lc)!==lc&&null!==ro.current||(cc=!0),0===(134217727&fc)&&0===(134217727&mc)||null===rc||Bc(rc,lc,gc,!1)}function Yc(e,t,n){var r=nc;nc|=2;var a=Wc(),l=qc();rc===e&&lc===t||(kc=null,Vc(e,t)),t=!1;var i=pc;e:for(;;)try{if(0!==ic&&null!==ac){var o=ac,s=oc;switch(ic){case 8:Uc(),i=6;break e;case 3:case 2:case 9:case 6:null===ro.current&&(t=!0);var c=ic;if(ic=0,oc=null,Zc(e,o,s,c),n&&cc){i=0;break e}break;default:c=ic,ic=0,oc=null,Zc(e,o,s,c)}}Xc(),i=pc;break}catch(u){Gc(e,u)}return t&&e.shellSuspendCounter++,ya=va=null,nc=r,z.H=a,z.A=l,null===ac&&(rc=null,lc=0,Lr()),i}function Xc(){for(;null!==ac;)Kc(ac)}function Qc(){for(;null!==ac&&!Z();)Kc(ac)}function Kc(e){var t=Ko(e.alternate,e,dc);e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Jc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Do(n,t,t.pendingProps,t.type,void 0,lc);break;case 11:t=Do(n,t,t.pendingProps,t.type.render,t.ref,lc);break;case 5:Rl(t);default:ls(n,t),t=Ko(n,t=ac=Fr(t,dc),dc)}e.memoizedProps=e.pendingProps,null===t?eu(e):ac=t}function Zc(e,t,n,r){ya=va=null,Rl(t),Yi=null,Xi=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&ka(t,n,a,!0),null!==(n=ro.current)){switch(n.tag){case 13:return null===ao?$c():null===n.alternate&&0===pc&&(pc=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ya?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),du(e,r,a)),!1;case 22:return n.flags|=65536,r===Ya?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),du(e,r,a)),!1}throw Error(i(435,n.tag))}return du(e,r,a),$c(),!1}if(la)return null!==(t=ro.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ha(Er(e=Error(i(422),{cause:r}),n))):(r!==sa&&ha(Er(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=Er(r,n),ol(e,a=ko(e.stateNode,r,a)),4!==pc&&(pc=2)),!1;var l=Error(i(520),{cause:r});if(l=Er(l,n),null===yc?yc=[l]:yc.push(l),4!==pc&&(pc=2),null===t)return!0;r=Er(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ol(n,e=ko(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==l&&"function"===typeof l.componentDidCatch&&(null===Ec||!Ec.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,Co(a=Eo(a),e,n,r),ol(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,lc))return pc=1,So(e,Er(n,e.current)),void(ac=null)}catch(l){if(null!==a)throw ac=a,l;return pc=1,So(e,Er(n,e.current)),void(ac=null)}32768&t.flags?(la||1===r?e=!0:cc||0!==(536870912&lc)?e=!1:(sc=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ro.current)&&13===r.tag&&(r.flags|=16384))),tu(t,e)):eu(t)}function eu(e){var t=e;do{if(0!==(32768&t.flags))return void tu(t,sc);e=t.return;var n=rs(t.alternate,t,dc);if(null!==n)return void(ac=n);if(null!==(t=t.sibling))return void(ac=t);ac=t=e}while(null!==t);0===pc&&(pc=5)}function tu(e,t){do{var n=as(e.alternate,e);if(null!==n)return n.flags&=32767,void(ac=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ac=e);ac=e=n}while(null!==e);pc=6,ac=null}function nu(e,t,n,r,a,l,o,s,c){e.cancelPendingCommit=null;do{ou()}while(0!==Cc);if(0!==(6&nc))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var o=e.entanglements,s=e.expirationTimes,c=e.hiddenUpdates;for(n=i&~n;0<n;){var u=31-fe(n),d=1<<u;o[u]=0,s[u]=-1;var p=c[u];if(null!==p)for(c[u]=null,u=0;u<p.length;u++){var f=p[u];null!==f&&(f.lane&=-536870913)}n&=~d}0!==r&&Te(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(i&~t))}(e,n,l|=Pr,o,s,c),e===rc&&(ac=rc=null,lc=0),Pc=t,Tc=e,Lc=n,Nc=l,jc=a,_c=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,K(le,function(){return su(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=z.T,z.T=null,a=A.p,A.p=2,o=nc,nc|=4;try{!function(e,t){if(e=e.containerInfo,td=np,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(g){n=null;break e}var o=0,s=-1,c=-1,u=0,d=0,p=e,f=null;t:for(;;){for(var m;p!==n||0!==a&&3!==p.nodeType||(s=o+a),p!==l||0!==r&&3!==p.nodeType||(c=o+r),3===p.nodeType&&(o+=p.nodeValue.length),null!==(m=p.firstChild);)f=p,p=m;for(;;){if(p===e)break t;if(f===n&&++u===a&&(s=o),f===l&&++d===r&&(c=o),null!==(m=p.nextSibling))break;f=(p=f).parentNode}p=m}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},np=!1,ks=t;null!==ks;)if(e=(t=ks).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,ks=e;else for(;null!==ks;){switch(l=(t=ks).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var h=go(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){uu(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))hd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":hd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,ks=e;break}ks=t.return}}(e,t)}finally{nc=o,A.p=a,z.T=r}}Cc=1,ru(),au(),lu()}}function ru(){if(1===Cc){Cc=0;var e=Tc,t=Pc,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=z.T,z.T=null;var r=A.p;A.p=2;var a=nc;nc|=4;try{zs(t,e);var l=nd,i=er(e.containerInfo),o=l.focusedElem,s=l.selectionRange;if(i!==o&&o&&o.ownerDocument&&Zn(o.ownerDocument.documentElement,o)){if(null!==s&&tr(o)){var c=s.start,u=s.end;if(void 0===u&&(u=c),"selectionStart"in o)o.selectionStart=c,o.selectionEnd=Math.min(u,o.value.length);else{var d=o.ownerDocument||document,p=d&&d.defaultView||window;if(p.getSelection){var f=p.getSelection(),m=o.textContent.length,h=Math.min(s.start,m),g=void 0===s.end?h:Math.min(s.end,m);!f.extend&&h>g&&(i=g,g=h,h=i);var v=Jn(o,h),y=Jn(o,g);if(v&&y&&(1!==f.rangeCount||f.anchorNode!==v.node||f.anchorOffset!==v.offset||f.focusNode!==y.node||f.focusOffset!==y.offset)){var b=d.createRange();b.setStart(v.node,v.offset),f.removeAllRanges(),h>g?(f.addRange(b),f.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),f.addRange(b))}}}}for(d=[],f=o;f=f.parentNode;)1===f.nodeType&&d.push({element:f,left:f.scrollLeft,top:f.scrollTop});for("function"===typeof o.focus&&o.focus(),o=0;o<d.length;o++){var w=d[o];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}np=!!td,nd=td=null}finally{nc=a,A.p=r,z.T=n}}e.current=t,Cc=2}}function au(){if(2===Cc){Cc=0;var e=Tc,t=Pc,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=z.T,z.T=null;var r=A.p;A.p=2;var a=nc;nc|=4;try{Es(e,t.alternate,t)}finally{nc=a,A.p=r,z.T=n}}Cc=3}}function lu(){if(4===Cc||3===Cc){Cc=0,ee();var e=Tc,t=Pc,n=Lc,r=_c;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cc=5:(Cc=0,Pc=Tc=null,iu(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Ec=null),Ne(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=z.T,a=A.p,A.p=2,z.T=null;try{for(var l=e.onRecoverableError,i=0;i<r.length;i++){var o=r[i];l(o.value,{componentStack:o.stack})}}finally{z.T=t,A.p=a}}0!==(3&Lc)&&ou(),xu(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Mc?Oc++:(Oc=0,Mc=e):Oc=0,ku(0,!1)}}function iu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,za(t)))}function ou(e){return ru(),au(),lu(),su()}function su(){if(5!==Cc)return!1;var e=Tc,t=Nc;Nc=0;var n=Ne(Lc),r=z.T,a=A.p;try{A.p=32>n?32:n,z.T=null,n=jc,jc=null;var l=Tc,o=Lc;if(Cc=0,Pc=Tc=null,Lc=0,0!==(6&nc))throw Error(i(331));var s=nc;if(nc|=4,Ks(l.current),Vs(l,l.current,o,n),nc=s,ku(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,l)}catch(c){}return!0}finally{A.p=a,z.T=r,iu(e,t)}}function cu(e,t,n){t=Er(n,t),null!==(e=ll(e,t=ko(e.stateNode,t,2),2))&&(Ce(e,2),xu(e))}function uu(e,t,n){if(3===e.tag)cu(e,e,n);else for(;null!==t;){if(3===t.tag){cu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Ec||!Ec.has(r))){e=Er(n,e),null!==(r=ll(t,n=Eo(2),2))&&(Co(n,r,t,e),Ce(r,2),xu(r));break}}t=t.return}}function du(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(uc=!0,a.add(n),e=pu.bind(null,e,t,n),t.then(e,e))}function pu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(lc&n)===n&&(4===pc||3===pc&&(62914560&lc)===lc&&300>te()-Sc?0===(2&nc)&&Vc(e,0):hc|=n,vc===lc&&(vc=0)),xu(e)}function fu(e,t){0===t&&(t=ke()),null!==(e=_r(e,t))&&(Ce(e,t),xu(e))}function mu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),fu(e,n)}function hu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),fu(e,n)}var gu=null,vu=null,yu=!1,bu=!1,wu=!1,Su=0;function xu(e){e!==vu&&null===e.next&&(null===vu?gu=vu=e:vu=vu.next=e),bu=!0,yu||(yu=!0,dd(function(){0!==(6&nc)?K(re,Eu):Cu()}))}function ku(e,t){if(!wu&&bu){wu=!0;do{for(var n=!1,r=gu;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var i=r.suspendedLanes,o=r.pingedLanes;l=(1<<31-fe(42|e)+1)-1,l=201326741&(l&=a&~(i&~o))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Lu(r,l))}else l=lc,0===(3&(l=be(r,r===rc?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,l)||(n=!0,Lu(r,l));r=r.next}}while(n);wu=!1}}function Eu(){Cu()}function Cu(){bu=yu=!1;var e=0;0!==Su&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==od&&(od=e,!0);return od=null,!1}()&&(e=Su),Su=0);for(var t=te(),n=null,r=gu;null!==r;){var a=r.next,l=Tu(r,t);0===l?(r.next=null,null===n?gu=a:n.next=a,null===a&&(vu=n)):(n=r,(0!==e||0!==(3&l))&&(bu=!0)),r=a}ku(e,!1)}function Tu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var i=31-fe(l),o=1<<i,s=a[i];-1===s?0!==(o&n)&&0===(o&r)||(a[i]=Se(o,t)):s<=t&&(e.expiredLanes|=o),l&=~o}if(n=lc,n=be(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===ic||9===ic)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&J(r),Ne(n)){case 2:case 8:n=ae;break;case 32:default:n=le;break;case 268435456:n=oe}return r=Pu.bind(null,e),n=K(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function Pu(e,t){if(0!==Cc&&5!==Cc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ou()&&e.callbackNode!==n)return null;var r=lc;return 0===(r=be(e,e===rc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Rc(e,r,t),Tu(e,te()),null!=e.callbackNode&&e.callbackNode===n?Pu.bind(null,e):null)}function Lu(e,t){if(ou())return null;Rc(e,t,!0)}function Nu(){return 0===Su&&(Su=xe()),Su}function ju(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Nt(""+e)}function _u(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ou=0;Ou<Sr.length;Ou++){var Mu=Sr[Ou];xr(Mu.toLowerCase(),"on"+(Mu[0].toUpperCase()+Mu.slice(1)))}xr(fr,"onAnimationEnd"),xr(mr,"onAnimationIteration"),xr(hr,"onAnimationStart"),xr("dblclick","onDoubleClick"),xr("focusin","onFocus"),xr("focusout","onBlur"),xr(gr,"onTransitionRun"),xr(vr,"onTransitionStart"),xr(yr,"onTransitionCancel"),xr(br,"onTransitionEnd"),Xe("onMouseEnter",["mouseout","mouseover"]),Xe("onMouseLeave",["mouseout","mouseover"]),Xe("onPointerEnter",["pointerout","pointerover"]),Xe("onPointerLeave",["pointerout","pointerover"]),Ye("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ye("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ye("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ye("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ye("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ye("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Au=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zu));function Du(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],s=o.instance,c=o.currentTarget;if(o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=c;try{l(a)}catch(u){vo(u)}a.currentTarget=null,l=s}else for(i=0;i<r.length;i++){if(s=(o=r[i]).instance,c=o.currentTarget,o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=c;try{l(a)}catch(u){vo(u)}a.currentTarget=null,l=s}}}}function Ru(e,t){var n=t[Ae];void 0===n&&(n=t[Ae]=new Set);var r=e+"__bubble";n.has(r)||(Hu(t,e,2,!1),n.add(r))}function Iu(e,t,n){var r=0;t&&(r|=4),Hu(n,e,r,t)}var Fu="_reactListening"+Math.random().toString(36).slice(2);function Bu(e){if(!e[Fu]){e[Fu]=!0,qe.forEach(function(t){"selectionchange"!==t&&(Au.has(t)||Iu(t,!1,e),Iu(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Fu]||(t[Fu]=!0,Iu("selectionchange",!1,t))}}function Hu(e,t,n,r){switch(cp(t)){case 2:var a=rp;break;case 8:a=ap;break;default:a=lp}n=a.bind(null,t,n,e),a=void 0,!Ft||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Uu(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=He(o)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=l=i;continue e}o=o.parentNode}}r=r.return}Dt(function(){var r=l,a=_t(n),i=[];e:{var o=wr.get(e);if(void 0!==o){var c=Zt,u=e;switch(e){case"keypress":if(0===Wt(n))break e;case"keydown":case"keyup":c=hn;break;case"focusin":u="focus",c=ln;break;case"focusout":u="blur",c=ln;break;case"beforeblur":case"afterblur":c=ln;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=vn;break;case fr:case mr:case hr:c=on;break;case br:c=yn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=bn;break;case"copy":case"cut":case"paste":c=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=wn}var d=0!==(4&t),p=!d&&("scroll"===e||"scrollend"===e),f=d?null!==o?o+"Capture":null:o;d=[];for(var m,h=r;null!==h;){var g=h;if(m=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===m||null===f||null!=(g=Rt(h,f))&&d.push(Vu(h,g,m)),p)break;h=h.return}0<d.length&&(o=new c(o,u,null,n,a),i.push({event:o,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===jt||!(u=n.relatedTarget||n.fromElement)||!He(u)&&!u[ze])&&(c||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?He(u):null)&&(p=s(u),d=u.tag,u!==p||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=rn,g="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",f="onPointerEnter",h="pointer"),p=null==c?o:Ve(c),m=null==u?o:Ve(u),(o=new d(g,h+"leave",c,n,a)).target=p,o.relatedTarget=m,g=null,He(a)===r&&((d=new d(f,h+"enter",u,n,a)).target=m,d.relatedTarget=p,g=d),p=g,c&&u)e:{for(f=u,h=0,m=d=c;m;m=Wu(m))h++;for(m=0,g=f;g;g=Wu(g))m++;for(;0<h-m;)d=Wu(d),h--;for(;0<m-h;)f=Wu(f),m--;for(;h--;){if(d===f||null!==f&&d===f.alternate)break e;d=Wu(d),f=Wu(f)}d=null}else d=null;null!==c&&qu(i,o,c,d,!1),null!==u&&null!==p&&qu(i,p,u,d,!0)}if("select"===(c=(o=r?Ve(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===c&&"file"===o.type)var v=In;else if(On(o))if(Fn)v=Yn;else{v=qn;var y=Wn}else!(c=o.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&Tt(r.elementType)&&(v=In):v=$n;switch(v&&(v=v(e,r))?Mn(i,v,n,a):(y&&y(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&yt(o,"number",o.value)),y=r?Ve(r):window,e){case"focusin":(On(y)||"true"===y.contentEditable)&&(rr=y,ar=r,lr=null);break;case"focusout":lr=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,or(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":or(i,n,a)}var b;if(xn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else jn?Ln(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Cn&&"ko"!==n.locale&&(jn||"onCompositionStart"!==w?"onCompositionEnd"===w&&jn&&(b=Gt()):(Ut="value"in(Ht=a)?Ht.value:Ht.textContent,jn=!0)),0<(y=Gu(r,w)).length&&(w=new cn(w,e,null,n,a),i.push({event:w,listeners:y}),b?w.data=b:null!==(b=Nn(n))&&(w.data=b))),(b=En?function(e,t){switch(e){case"compositionend":return Nn(t);case"keypress":return 32!==t.which?null:(Pn=!0,Tn);case"textInput":return(e=t.data)===Tn&&Pn?null:e;default:return null}}(e,n):function(e,t){if(jn)return"compositionend"===e||!xn&&Ln(e,t)?(e=Gt(),Vt=Ut=Ht=null,jn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(w=Gu(r,"onBeforeInput")).length&&(y=new cn("onBeforeInput","beforeinput",null,n,a),i.push({event:y,listeners:w}),y.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=ju((a[Me]||null).action),i=r.submitter;i&&null!==(t=(t=i[Me]||null)?ju(t.formAction):i.getAttribute("formAction"))&&(l=t,i=null);var o=new Zt("action","action",null,r,a);e.push({event:o,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Su){var e=i?_u(a,i):new FormData(a);ji(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"===typeof l&&(o.preventDefault(),e=i?_u(a,i):new FormData(a),ji(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(i,e,r,n,a)}Du(i,t)})}function Vu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gu(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Rt(e,n))&&r.unshift(Vu(e,a,l)),null!=(a=Rt(e,t))&&r.push(Vu(e,a,l))),3===e.tag)return r;e=e.return}return[]}function Wu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qu(e,t,n,r,a){for(var l=t._reactName,i=[];null!==n&&n!==r;){var o=n,s=o.alternate,c=o.stateNode;if(o=o.tag,null!==s&&s===r)break;5!==o&&26!==o&&27!==o||null===c||(s=c,a?null!=(c=Rt(n,l))&&i.unshift(Vu(n,c,s)):a||null!=(c=Rt(n,l))&&i.push(Vu(n,c,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var $u=/\r\n?/g,Yu=/\u0000|\uFFFD/g;function Xu(e){return("string"===typeof e?e:""+e).replace($u,"\n").replace(Yu,"")}function Qu(e,t){return t=Xu(t),Xu(e)===t}function Ku(){}function Ju(e,t,n,r,a,l){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||xt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&xt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,l);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Nt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof l&&("formAction"===n?("input"!==t&&Ju(e,t,"name",a.name,a,null),Ju(e,t,"formEncType",a.formEncType,a,null),Ju(e,t,"formMethod",a.formMethod,a,null),Ju(e,t,"formTarget",a.formTarget,a,null)):(Ju(e,t,"encType",a.encType,a,null),Ju(e,t,"method",a.method,a,null),Ju(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Nt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Ku);break;case"onScroll":null!=r&&Ru("scroll",e);break;case"onScrollEnd":null!=r&&Ru("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Nt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Ru("beforetoggle",e),Ru("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Pt.get(n)||n,r)}}function Zu(e,t,n,r,a,l){switch(n){case"style":Ct(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?xt(e,r):("number"===typeof r||"bigint"===typeof r)&&xt(e,""+r);break;case"onScroll":null!=r&&Ru("scroll",e);break;case"onScrollEnd":null!=r&&Ru("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Ku);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:$e.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(l=null!=(l=e[Me]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ru("error",e),Ru("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var o=n[r];if(null!=o)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ju(e,t,r,o,n,null)}}return l&&Ju(e,t,"srcSet",n.srcSet,n,null),void(a&&Ju(e,t,"src",n.src,n,null));case"input":Ru("invalid",e);var s=r=o=l=null,c=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":l=d;break;case"type":o=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:Ju(e,t,a,d,n,null)}}return vt(e,r,s,c,u,o,l,!1),void dt(e);case"select":for(l in Ru("invalid",e),a=o=r=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":r=s;break;case"defaultValue":o=s;break;case"multiple":a=s;default:Ju(e,t,l,s,n,null)}return t=r,n=o,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(o in Ru("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":a=s;break;case"defaultValue":l=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(i(91));break;default:Ju(e,t,o,s,n,null)}return St(e,a,l,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))if("selected"===c)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else Ju(e,t,c,a,n,null);return;case"dialog":Ru("beforetoggle",e),Ru("toggle",e),Ru("cancel",e),Ru("close",e);break;case"iframe":case"object":Ru("load",e);break;case"video":case"audio":for(a=0;a<zu.length;a++)Ru(zu[a],e);break;case"image":Ru("error",e),Ru("load",e);break;case"details":Ru("toggle",e);break;case"embed":case"source":case"link":Ru("error",e),Ru("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:Ju(e,t,u,a,n,null)}return;default:if(Tt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&Zu(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&Ju(e,t,s,a,n,null))}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ld(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function id(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var od=null;var sd="function"===typeof setTimeout?setTimeout:void 0,cd="function"===typeof clearTimeout?clearTimeout:void 0,ud="function"===typeof Promise?Promise:void 0,dd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ud?function(e){return ud.resolve(null).then(e).catch(pd)}:sd;function pd(e){setTimeout(function(){throw e})}function fd(e){return"head"===e}function md(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&Sd(i.documentElement),2&n&&Sd(i.body),4&n)for(Sd(n=i.head),i=n.firstChild;i;){var o=i.nextSibling,s=i.nodeName;i[Fe]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=o}}if(0===a)return e.removeChild(l),void Pp(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Pp(t)}function hd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":hd(n),Be(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function gd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function vd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var yd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function Sd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Be(e)}var xd=new Map,kd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=A.d;A.d={f:function(){var e=Cd.f(),t=Hc();return e||t},r:function(e){var t=Ue(e);null!==t&&5===t.tag&&"form"===t.type?Oi(t):Cd.r(e)},D:function(e){Cd.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Td;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var l=a;switch(t){case"style":l=Nd(e);break;case"script":l=Od(e)}xd.has(l)||(e=p({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),xd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(jd(l))||"script"===t&&r.querySelector(Md(l))||(ed(t=r.createElement("link"),"link",e),We(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Td;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Od(e)}if(!xd.has(l)&&(e=p({rel:"modulepreload",href:e},t),xd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Md(l)))return}ed(r=n.createElement("link"),"link",e),We(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Td;if(n&&e){var r=Ge(n).hoistableScripts,a=Od(e),l=r.get(a);l||((l=n.querySelector(Md(a)))||(e=p({src:e,async:!0},t),(t=xd.get(a))&&Rd(e,t),We(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Cd.S(e,t,n);var r=Td;if(r&&e){var a=Ge(r).hoistableStyles,l=Nd(e);t=t||"default";var i=a.get(l);if(!i){var o={loading:0,preload:null};if(i=r.querySelector(jd(l)))o.loading=5;else{e=p({rel:"stylesheet",href:e,"data-precedence":t},n),(n=xd.get(l))&&Dd(e,n);var s=i=r.createElement("link");We(s),ed(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){o.loading|=1}),s.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Ad(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:o},a.set(l,i)}}},M:function(e,t){Cd.M(e,t);var n=Td;if(n&&e){var r=Ge(n).hoistableScripts,a=Od(e),l=r.get(a);l||((l=n.querySelector(Md(a)))||(e=p({src:e,async:!0,type:"module"},t),(t=xd.get(a))&&Rd(e,t),We(l=n.createElement("script")),ed(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Td="undefined"===typeof document?null:document;function Pd(e,t,n){var r=Td;if(r&&"string"===typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),kd.has(a)||(kd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),We(t),r.head.appendChild(t)))}}function Ld(e,t,n,r){var a,l,o,s,c=(c=G.current)?Ed(c):null;if(!c)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Nd(n.href),(r=(n=Ge(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Nd(n.href);var u=Ge(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(jd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),xd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},xd.set(e,n),u||(a=c,l=e,o=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?s.loading=1:(l=a.createElement("link"),s.preload=l,l.addEventListener("load",function(){return s.loading|=1}),l.addEventListener("error",function(){return s.loading|=2}),ed(l,"link",o),We(l),a.head.appendChild(l))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Od(n),(r=(n=Ge(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Nd(e){return'href="'+ht(e)+'"'}function jd(e){return'link[rel="stylesheet"]['+e+"]"}function _d(e){return p({},e,{"data-precedence":e.precedence,precedence:null})}function Od(e){return'[src="'+ht(e)+'"]'}function Md(e){return"script[async]"+e}function zd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,We(r),r;var a=p({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return We(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Ad(r,n.precedence,e),t.instance=r;case"stylesheet":a=Nd(n.href);var l=e.querySelector(jd(a));if(l)return t.state.loading|=4,t.instance=l,We(l),l;r=_d(n),(a=xd.get(a))&&Dd(r,a),We(l=(e.ownerDocument||e).createElement("link"));var o=l;return o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),ed(l,"link",r),t.state.loading|=4,Ad(l,n.precedence,e),t.instance=l;case"script":return l=Od(n.src),(a=e.querySelector(Md(l)))?(t.instance=a,We(a),a):(r=n,(a=xd.get(l))&&Rd(r=p({},n),a),We(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Ad(r,n.precedence,e));return t.instance}function Ad(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,i=0;i<r.length;i++){var o=r[i];if(o.dataset.precedence===t)l=o;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Rd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function Fd(e,t,n){if(null===Id){var r=new Map,a=Id=new Map;a.set(n,r)}else(r=(a=Id).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Fe]||l[Oe]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var i=l.getAttribute(t)||"";i=e+i;var o=r.get(i);o?o.push(l):r.set(i,[l])}}return r}function Bd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Hd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Ud=null;function Vd(){}function Gd(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Wd=null;function qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Wd=new Map,t.forEach($d,e),Wd=null,Gd.call(e))}function $d(e,t){if(!(4&t.state.loading)){var n=Wd.get(e);if(n)var r=n.get(null);else{n=new Map,Wd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var i=a[l];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(l=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Gd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Yd={$$typeof:S,Provider:null,Consumer:null,_currentValue:D,_currentValue2:D,_threadCount:0};function Xd(e,t,n,r,a,l,i,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ee(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ee(0),this.hiddenUpdates=Ee(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Qd(e,t,n,r,a,l,i,o,s,c,u,d){return e=new Xd(e,t,n,i,o,s,c,d),t=1,!0===l&&(t|=24),l=Dr(3,null,null,t),e.current=l,l.stateNode=e,(t=Ma()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},nl(l),e}function Kd(e){return e?e=zr:zr}function Jd(e,t,n,r,a,l){a=Kd(a),null===r.context?r.context=a:r.pendingContext=a,(r=al(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=ll(e,r,t))&&(Dc(n,0,t),il(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ep(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tp(e){if(13===e.tag){var t=_r(e,67108864);null!==t&&Dc(t,0,67108864),ep(e,67108864)}}var np=!0;function rp(e,t,n,r){var a=z.T;z.T=null;var l=A.p;try{A.p=2,lp(e,t,n,r)}finally{A.p=l,z.T=a}}function ap(e,t,n,r){var a=z.T;z.T=null;var l=A.p;try{A.p=8,lp(e,t,n,r)}finally{A.p=l,z.T=a}}function lp(e,t,n,r){if(np){var a=ip(r);if(null===a)Uu(e,t,r,op,n),yp(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return dp=bp(dp,e,t,n,r,a),!0;case"dragenter":return pp=bp(pp,e,t,n,r,a),!0;case"mouseover":return fp=bp(fp,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return mp.set(l,bp(mp.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,hp.set(l,bp(hp.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(yp(e,r),4&t&&-1<vp.indexOf(e)){for(;null!==a;){var l=Ue(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var i=ye(l.pendingLanes);if(0!==i){var o=l;for(o.pendingLanes|=2,o.entangledLanes|=2;i;){var s=1<<31-fe(i);o.entanglements[1]|=s,i&=~s}xu(l),0===(6&nc)&&(xc=te()+500,ku(0,!1))}}break;case 13:null!==(o=_r(l,2))&&Dc(o,0,2),Hc(),ep(l,2)}if(null===(l=ip(r))&&Uu(e,t,r,op,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Uu(e,t,r,null,n)}}function ip(e){return sp(e=_t(e))}var op=null;function sp(e){if(op=null,null!==(e=He(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return op=e,null}function cp(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case le:case ie:return 32;case oe:return 268435456;default:return 32}default:return 32}}var up=!1,dp=null,pp=null,fp=null,mp=new Map,hp=new Map,gp=[],vp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function yp(e,t){switch(e){case"focusin":case"focusout":dp=null;break;case"dragenter":case"dragleave":pp=null;break;case"mouseover":case"mouseout":fp=null;break;case"pointerover":case"pointerout":mp.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":hp.delete(t.pointerId)}}function bp(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=Ue(t))&&tp(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function wp(e){var t=He(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=A.p;try{return A.p=e,t()}finally{A.p=n}}(e.priority,function(){if(13===n.tag){var e=zc();e=Le(e);var t=_r(n,e);null!==t&&Dc(t,0,e),ep(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Sp(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=ip(e.nativeEvent);if(null!==n)return null!==(t=Ue(n))&&tp(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);jt=r,n.target.dispatchEvent(r),jt=null,t.shift()}return!0}function xp(e,t,n){Sp(e)&&n.delete(t)}function kp(){up=!1,null!==dp&&Sp(dp)&&(dp=null),null!==pp&&Sp(pp)&&(pp=null),null!==fp&&Sp(fp)&&(fp=null),mp.forEach(xp),hp.forEach(xp)}function Ep(e,t){e.blockedOn===t&&(e.blockedOn=null,up||(up=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,kp)))}var Cp=null;function Tp(e){Cp!==e&&(Cp=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Cp===e&&(Cp=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===sp(r||n))continue;break}var l=Ue(n);null!==l&&(e.splice(t,3),t-=3,ji(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Pp(e){function t(t){return Ep(t,e)}null!==dp&&Ep(dp,e),null!==pp&&Ep(pp,e),null!==fp&&Ep(fp,e),mp.forEach(t),hp.forEach(t);for(var n=0;n<gp.length;n++){var r=gp[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<gp.length&&null===(n=gp[0]).blockedOn;)wp(n),null===n.blockedOn&&gp.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],i=a[Me]||null;if("function"===typeof l)i||Tp(n);else if(i){var o=null;if(l&&l.hasAttribute("formAction")){if(a=l,i=l[Me]||null)o=i.formAction;else if(null!==sp(a))continue}else o=i.action;"function"===typeof o?n[r+1]=o:(n.splice(r,3),r-=3),Tp(n)}}}function Lp(e){this._internalRoot=e}function Np(e){this._internalRoot=e}Np.prototype.render=Lp.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Jd(t.current,zc(),e,t,null,null)},Np.prototype.unmount=Lp.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Jd(e.current,2,null,e,null,null),Hc(),t[ze]=null}},Np.prototype.unstable_scheduleHydration=function(e){if(e){var t=je();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gp.length&&0!==t&&t<gp[n].priority;n++);gp.splice(n,0,e),0===n&&wp(e)}};var jp=a.version;if("19.1.0"!==jp)throw Error(i(527,jp,"19.1.0"));A.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return u(a),e;if(l===r)return u(a),t;l=l.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=l;else{for(var o=!1,c=a.child;c;){if(c===n){o=!0,n=a,r=l;break}if(c===r){o=!0,r=a,n=l;break}c=c.sibling}if(!o){for(c=l.child;c;){if(c===n){o=!0,n=l,r=a;break}if(c===r){o=!0,r=l,n=a;break}c=c.sibling}if(!o)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var _p={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Op=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Op.isDisabled&&Op.supportsFiber)try{ue=Op.inject(_p),de=Op}catch(zp){}}t.createRoot=function(e,t){if(!o(e))throw Error(i(299));var n=!1,r="",a=yo,l=bo,s=wo;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Qd(e,1,!1,null,0,n,r,a,l,s,0,null),e[ze]=t.current,Bu(e),new Lp(t)},t.hydrateRoot=function(e,t,n){if(!o(e))throw Error(i(299));var r=!1,a="",l=yo,s=bo,c=wo,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Qd(e,1,!0,t,0,r,a,l,s,c,0,u)).context=Kd(null),n=t.current,(a=al(r=Le(r=zc()))).callback=null,ll(n,a,r),n=r,t.current.lanes=n,Ce(t,n),xu(t),e[ze]=t.current,Bu(e),new Np(t)},t.version="19.1.0"},43:(e,t,n)=>{e.exports=n(288)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,h(w,v.prototype),w.isPureReactComponent=!0;var S=Array.isArray,x={H:null,A:null,T:null,S:null,V:null},k=Object.prototype.hasOwnProperty;function E(e,t,r,a,l,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function L(){}function N(e,t,a,l,i){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s,c,u=!1;if(null===e)u=!0;else switch(o){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case p:return N((u=e._init)(e._payload),t,a,l,i)}}if(u)return i=i(e),u=""===l?"."+P(e,0):l,S(i)?(a="",null!=u&&(a=u.replace(T,"$&/")+"/"),N(i,t,a,"",function(e){return e})):null!=i&&(C(i)&&(s=i,c=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(T,"$&/")+"/")+u,i=E(s.type,c,void 0,0,0,s.props)),t.push(i)),1;u=0;var d,m=""===l?".":l+":";if(S(e))for(var h=0;h<e.length;h++)u+=N(l=e[h],t,a,o=m+P(l,h),i);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=f&&d[f]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(l=e.next()).done;)u+=N(l=l.value,t,a,o=m+P(l,h++),i);else if("object"===o){if("function"===typeof e.then)return N(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(L,L):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function j(e,t,n){if(null==e)return e;var r=[],a=0;return N(e,r,"","",function(e){return t.call(n,e,a++)}),r}function _(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function M(){}t.Children={map:j,forEach:function(e,t,n){j(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return j(e,function(){t++}),t},toArray:function(e){return j(e,function(e){return e})||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=l,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return x.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),a=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!k.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var i=Array(l),o=0;o<l;o++)i[o]=arguments[o+2];r.children=i}return E(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:o,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)k.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var o=Array(i),s=0;s<i;s++)o[s]=arguments[s+2];a.children=o}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return E(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:_}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=x.T,n={};x.T=n;try{var r=e(),a=x.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(M,O)}catch(l){O(l)}finally{x.T=t}},t.unstable_useCacheRefresh=function(){return x.H.useCacheRefresh()},t.use=function(e){return x.H.use(e)},t.useActionState=function(e,t,n){return x.H.useActionState(e,t,n)},t.useCallback=function(e,t){return x.H.useCallback(e,t)},t.useContext=function(e){return x.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return x.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=x.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return x.H.useId()},t.useImperativeHandle=function(e,t,n){return x.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return x.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return x.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return x.H.useMemo(e,t)},t.useOptimistic=function(e,t){return x.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return x.H.useReducer(e,t,n)},t.useRef=function(e){return x.H.useRef(e)},t.useState=function(e){return x.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return x.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return x.H.useTransition()},t.version="19.1.0"},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,l=/^[\u0020-\u003A\u003D-\u007E]*$/,i=Object.prototype.toString,o=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function s(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function c(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function u(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var i={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},o=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=i.p;try{if(s.T=null,i.p=2,e)return e()}finally{s.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,l="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var o=2*(r+1)-1,s=e[o],c=o+1,u=e[c];if(0>l(s,n))c<a&&0>l(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[o]=n,r=o);else{if(!(c<a&&0>l(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var c=[],u=[],d=1,p=null,f=3,m=!1,h=!1,g=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,w="undefined"!==typeof setImmediate?setImmediate:null;function S(e){for(var t=r(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(g=!1,S(e),!h)if(null!==r(c))h=!0,E||(E=!0,k());else{var t=r(u);null!==t&&O(x,t.startTime-e)}}var k,E=!1,C=-1,T=5,P=-1;function L(){return!!v||!(t.unstable_now()-P<T)}function N(){if(v=!1,E){var e=t.unstable_now();P=e;var n=!0;try{e:{h=!1,g&&(g=!1,b(C),C=-1),m=!0;var l=f;try{t:{for(S(e),p=r(c);null!==p&&!(p.expirationTime>e&&L());){var i=p.callback;if("function"===typeof i){p.callback=null,f=p.priorityLevel;var o=i(p.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof o){p.callback=o,S(e),n=!0;break t}p===r(c)&&a(c),S(e)}else a(c);p=r(c)}if(null!==p)n=!0;else{var s=r(u);null!==s&&O(x,s.startTime-e),n=!1}}break e}finally{p=null,f=l,m=!1}n=void 0}}finally{n?k():E=!1}}}if("function"===typeof w)k=function(){w(N)};else if("undefined"!==typeof MessageChannel){var j=new MessageChannel,_=j.port2;j.port1.onmessage=N,k=function(){_.postMessage(null)}}else k=function(){y(N,0)};function O(e,n){C=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_requestPaint=function(){v=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_scheduleCallback=function(e,a,l){var i=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?i+l:i:l=i,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>i?(e.sortIndex=l,n(u,e),null===r(c)&&e===r(u)&&(g?(b(C),C=-1):g=!0,O(x,l-i))):(e.sortIndex=o,n(c,e),h||m||(h=!0,E||(E=!0,k()))),e},t.unstable_shouldYield=L,t.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+".5988f6f8.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="med-amine-chouchane-portfolio:";n.l=(r,a,l,i)=>{if(e[r])e[r].push(a);else{var o,s;if(void 0!==l)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+l){o=d;break}}o||(s=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",t+l),o.src=r),e[r]=[a];var p=(t,n)=>{o.onerror=o.onload=null,clearTimeout(f);var a=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),a&&a.forEach(e=>e(n)),t)return t(n)},f=setTimeout(p.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=p.bind(null,o.onerror),o.onload=p.bind(null,o.onload),s&&document.head.appendChild(o)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/Portfilio/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var l=new Promise((n,r)=>a=e[t]=[n,r]);r.push(a[2]=l);var i=n.p+n.u(t),o=new Error;n.l(i,r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var l=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+l+": "+i+")",o.name="ChunkLoadError",o.type=l,o.request=i,a[1](o)}},"chunk-"+t,t)}};var t=(t,r)=>{var a,l,i=r[0],o=r[1],s=r[2],c=0;if(i.some(t=>0!==e[t])){for(a in o)n.o(o,a)&&(n.m[a]=o[a]);if(s)s(n)}for(t&&t(r);c<i.length;c++)l=i[c],n.o(e,l)&&e[l]&&e[l][0](),e[l]=0},r=self.webpackChunkmed_amine_chouchane_portfolio=self.webpackChunkmed_amine_chouchane_portfolio||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r=n(43),a=n(391);function l(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function o(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}function s(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach(function(t){s(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}n(358);const d=["sri"],p=["page"],f=["page","matches"],m=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],h=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],g=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var v="popstate";function y(){return C(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return x("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:k(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function b(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function w(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function S(e,t){return{usr:e.state,key:e.key,idx:t}}function x(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return u(u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?E(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function k(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function E(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function C(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o="POP",s=null,c=d();function d(){return(i.state||{idx:null}).idx}function p(){o="POP";let e=d(),t=null==e?null:e-c;c=e,s&&s({action:o,location:m.location,delta:t})}function f(e){return T(e)}null==c&&(c=0,i.replaceState(u(u({},i.state),{},{idx:c}),""));let m={get action(){return o},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(v,p),s=e,()=>{a.removeEventListener(v,p),s=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o="PUSH";let r=x(m.location,e,t);n&&n(r,e),c=d()+1;let u=S(r,c),p=m.createHref(r);try{i.pushState(u,"",p)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;a.location.assign(p)}l&&s&&s({action:o,location:m.location,delta:1})},replace:function(e,t){o="REPLACE";let r=x(m.location,e,t);n&&n(r,e),c=d();let a=S(r,c),u=m.createHref(r);i.replaceState(a,"",u),l&&s&&s({action:o,location:m.location,delta:0})},go:e=>i.go(e)};return m}function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),b(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:k(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function P(e,t){return L(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function L(e,t,n,r){let a=V(("string"===typeof t?E(t):t).pathname||"/",n);if(null==a)return null;let l=N(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let i=null;for(let o=0;null==i&&o<l.length;++o){let e=U(a);i=F(l[o],e,r)}return i}function N(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,l)=>{let i={relativePath:void 0===l?e.path||"":l,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(b(i.relativePath.startsWith(r),'Absolute route path "'.concat(i.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),i.relativePath=i.relativePath.slice(r.length));let o=Y([r,i.relativePath]),s=n.concat(i);e.children&&e.children.length>0&&(b(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(o,'".')),N(e.children,t,s,o)),(null!=e.path||e.index)&&t.push({path:o,score:I(o,e.index),routesMeta:s})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of j(e.path))a(e,t,r);else a(e,t)}),t}function j(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let i=j(r.join("/")),o=[];return o.push(...i.map(e=>""===e?l:[l,e].join("/"))),a&&o.push(...i),o.map(t=>e.startsWith("/")&&""===t?"/":t)}var _=/^:[\w-]+$/,O=3,M=2,z=1,A=10,D=-2,R=e=>"*"===e;function I(e,t){let n=e.split("/"),r=n.length;return n.some(R)&&(r+=D),t&&(r+=M),n.filter(e=>!R(e)).reduce((e,t)=>e+(_.test(t)?O:""===t?z:A),r)}function F(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let e=r[o],s=o===r.length-1,c="/"===l?t:t.slice(l.length)||"/",u=B({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},c),d=e.route;if(!u&&s&&n&&!r[r.length-1].route.index&&(u=B({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(a,u.params),i.push({params:a,pathname:Y([l,u.pathname]),pathnameBase:X(Y([l,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(l=Y([l,u.pathnameBase]))}return i}function B(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=H(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=o[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:i,pattern:e}}function H(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];w("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function U(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return w(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function V(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function G(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function W(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function q(e){let t=W(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function $(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=E(e):(r=u({},e),b(!r.pathname||!r.pathname.includes("?"),G("?","pathname","search",r)),b(!r.pathname||!r.pathname.includes("#"),G("#","pathname","hash",r)),b(!r.search||!r.search.includes("#"),G("#","search","hash",r)));let l,i=""===e||""===r.pathname,o=i?"/":r.pathname;if(null==o)l=n;else{let e=t.length-1;if(!a&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?E(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:Q(r),hash:K(a)}}(r,l),c=o&&"/"!==o&&o.endsWith("/"),d=(i||"."===o)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!d||(s.pathname+="/"),s}var Y=e=>e.join("/").replace(/\/\/+/g,"/"),X=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Q=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",K=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function J(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var Z=["POST","PUT","PATCH","DELETE"],ee=(new Set(Z),["GET",...Z]);new Set(ee),Symbol("ResetLoaderData");var te=r.createContext(null);te.displayName="DataRouter";var ne=r.createContext(null);ne.displayName="DataRouterState";var re=r.createContext({isTransitioning:!1});re.displayName="ViewTransition";var ae=r.createContext(new Map);ae.displayName="Fetchers";var le=r.createContext(null);le.displayName="Await";var ie=r.createContext(null);ie.displayName="Navigation";var oe=r.createContext(null);oe.displayName="Location";var se=r.createContext({outlet:null,matches:[],isDataRoute:!1});se.displayName="Route";var ce=r.createContext(null);ce.displayName="RouteError";function ue(){return null!=r.useContext(oe)}function de(){return b(ue(),"useLocation() may be used only in the context of a <Router> component."),r.useContext(oe).location}var pe="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function fe(e){r.useContext(ie).static||r.useLayoutEffect(e)}function me(){let{isDataRoute:e}=r.useContext(se);return e?function(){let{router:e}=Ee("useNavigate"),t=Te("useNavigate"),n=r.useRef(!1);fe(()=>{n.current=!0});let a=r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w(n.current,pe),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,u({fromRouteId:t},a)))},[e,t]);return a}():function(){b(ue(),"useNavigate() may be used only in the context of a <Router> component.");let e=r.useContext(te),{basename:t,navigator:n}=r.useContext(ie),{matches:a}=r.useContext(se),{pathname:l}=de(),i=JSON.stringify(q(a)),o=r.useRef(!1);fe(()=>{o.current=!0});let s=r.useCallback(function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(w(o.current,pe),!o.current)return;if("number"===typeof r)return void n.go(r);let s=$(r,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:Y([t,s.pathname])),(a.replace?n.replace:n.push)(s,a.state,a)},[t,n,i,l,e]);return s}()}r.createContext(null);function he(){let{matches:e}=r.useContext(se),t=e[e.length-1];return t?t.params:{}}function ge(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=r.useContext(se),{pathname:a}=de(),l=JSON.stringify(q(n));return r.useMemo(()=>$(e,JSON.parse(l),a,"path"===t),[e,l,a,t])}function ve(e,t,n,a){b(ue(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=r.useContext(ie),{matches:i}=r.useContext(se),o=i[i.length-1],s=o?o.params:{},c=o?o.pathname:"/",d=o?o.pathnameBase:"/",p=o&&o.route;{let e=p&&p.path||"";Ne(c,!p||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(c,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let f,m=de();if(t){var h;let e="string"===typeof t?E(t):t;b("/"===d||(null===(h=e.pathname)||void 0===h?void 0:h.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),f=e}else f=m;let g=f.pathname||"/",v=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");v="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=P(e,{pathname:v});w(p||null!=y,'No routes matched location "'.concat(f.pathname).concat(f.search).concat(f.hash,'" ')),w(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,'Matched leaf route at location "'.concat(f.pathname).concat(f.search).concat(f.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let S=xe(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},s,e.params),pathname:Y([d,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:Y([d,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,n,a);return t&&S?r.createElement(oe.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:"POP"}},S):S}function ye(){let e=Pe(),t=J(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:a},i={padding:"2px 4px",backgroundColor:a},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=r.createElement(r.Fragment,null,r.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),r.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",r.createElement("code",{style:i},"ErrorBoundary")," or"," ",r.createElement("code",{style:i},"errorElement")," prop on your route.")),r.createElement(r.Fragment,null,r.createElement("h2",null,"Unexpected Application Error!"),r.createElement("h3",{style:{fontStyle:"italic"}},t),n?r.createElement("pre",{style:l},n):null,o)}var be=r.createElement(ye,null),we=class extends r.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?r.createElement(se.Provider,{value:this.props.routeContext},r.createElement(ce.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Se(e){let{routeContext:t,match:n,children:a}=e,l=r.useContext(te);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),r.createElement(se.Provider,{value:t},a)}function xe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let a=e,l=null===n||void 0===n?void 0:n.errors;if(null!=l){let e=a.findIndex(e=>e.route.id&&void 0!==(null===l||void 0===l?void 0:l[e.route.id]));b(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(l).join(","))),a=a.slice(0,Math.min(a.length,e+1))}let i=!1,o=-1;if(n)for(let r=0;r<a.length;r++){let e=a[r];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(o=r),e.route.id){let{loaderData:t,errors:r}=n,l=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||l){i=!0,a=o>=0?a.slice(0,o+1):[a[0]];break}}}return a.reduceRight((e,s,c)=>{let u,d=!1,p=null,f=null;n&&(u=l&&s.route.id?l[s.route.id]:void 0,p=s.route.errorElement||be,i&&(o<0&&0===c?(Ne("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,f=null):o===c&&(d=!0,f=s.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,c+1)),h=()=>{let t;return t=u?p:d?f:s.route.Component?r.createElement(s.route.Component,null):s.route.element?s.route.element:e,r.createElement(Se,{match:s,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(s.route.ErrorBoundary||s.route.errorElement||0===c)?r.createElement(we,{location:n.location,revalidation:n.revalidation,component:p,error:u,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}function ke(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Ee(e){let t=r.useContext(te);return b(t,ke(e)),t}function Ce(e){let t=r.useContext(ne);return b(t,ke(e)),t}function Te(e){let t=function(e){let t=r.useContext(se);return b(t,ke(e)),t}(e),n=t.matches[t.matches.length-1];return b(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Pe(){var e;let t=r.useContext(ce),n=Ce("useRouteError"),a=Te("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[a]}var Le={};function Ne(e,t,n){t||Le[e]||(Le[e]=!0,w(!1,n))}r.memo(function(e){let{routes:t,future:n,state:r}=e;return ve(t,void 0,r,n)});function je(e){b(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function _e(e){let{basename:t="/",children:n=null,location:a,navigationType:l="POP",navigator:i,static:o=!1}=e;b(!ue(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=t.replace(/^\/*/,"/"),c=r.useMemo(()=>({basename:s,navigator:i,static:o,future:{}}),[s,i,o]);"string"===typeof a&&(a=E(a));let{pathname:u="/",search:d="",hash:p="",state:f=null,key:m="default"}=a,h=r.useMemo(()=>{let e=V(u,s);return null==e?null:{location:{pathname:e,search:d,hash:p,state:f,key:m},navigationType:l}},[s,u,d,p,f,m,l]);return w(null!=h,'<Router basename="'.concat(s,'"> is not able to match the URL "').concat(u).concat(d).concat(p,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==h?null:r.createElement(ie.Provider,{value:c},r.createElement(oe.Provider,{children:n,value:h}))}function Oe(e){let{children:t,location:n}=e;return ve(Me(t),n)}r.Component;function Me(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return r.Children.forEach(e,(e,a)=>{if(!r.isValidElement(e))return;let l=[...t,a];if(e.type===r.Fragment)return void n.push.apply(n,Me(e.props.children,l));b(e.type===je,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),b(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Me(e.props.children,l)),n.push(i)}),n}var ze="get",Ae="application/x-www-form-urlencoded";function De(e){return null!=e&&"string"===typeof e.tagName}var Re=null;var Ie=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Fe(e){return null==e||Ie.has(e)?e:(w(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(Ae,'"')),null)}function Be(e,t){let n,r,a,l,i;if(De(o=e)&&"form"===o.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?V(i,t):null,n=e.getAttribute("method")||ze,a=Fe(e.getAttribute("enctype"))||Ae,l=new FormData(e)}else if(function(e){return De(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return De(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let o=e.getAttribute("formaction")||i.getAttribute("action");if(r=o?V(o,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ze,a=Fe(e.getAttribute("formenctype"))||Fe(i.getAttribute("enctype"))||Ae,l=new FormData(i,e),!function(){if(null===Re)try{new FormData(document.createElement("form"),0),Re=!1}catch(e){Re=!0}return Re}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";l.append("".concat(e,"x"),"0"),l.append("".concat(e,"y"),"0")}else t&&l.append(t,r)}}else{if(De(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ze,r=null,a=Ae,i=e}var o;return l&&"text/plain"===a&&(i=l,l=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:l,body:i}}function He(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Ue(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ve(e){return null!=e&&"string"===typeof e.page}function Ge(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function We(e,t,n,r,a,l){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,o=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===l?t.filter((e,t)=>i(e,t)||o(e,t)):"data"===l?t.filter((t,l)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,l)||o(t,l))return!0;if(t.route.shouldRevalidate){var c;let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function qe(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function $e(e,t){let n=new Set,r=new Set(t);return e.reduce((e,a)=>{if(t&&!Ve(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let l=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(l)||(n.add(l),e.push({key:l,link:a})),e},[])}function Ye(e){return{__html:e}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;Symbol("SingleFetchRedirect");var Xe=new Set([100,101,204,205]);function Qe(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===V(n.pathname,t)?n.pathname="".concat(t.replace(/\/$/,""),"/_root.data"):n.pathname="".concat(n.pathname.replace(/\/$/,""),".data"),n}r.Component;function Ke(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let a,l=r.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(J(t))return r.createElement(Je,{title:"Unhandled Thrown Response!"},r.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),l);if(t instanceof Error)a=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);a=new Error(e)}return r.createElement(Je,{title:"Application Error!",isOutsideRemixApp:n},r.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),r.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},a.stack),l)}function Je(e){var t;let{title:n,renderScripts:a,isOutsideRemixApp:l,children:i}=e,{routeModules:o}=rt();return null!==(t=o.root)&&void 0!==t&&t.Layout&&!l?i:r.createElement("html",{lang:"en"},r.createElement("head",null,r.createElement("meta",{charSet:"utf-8"}),r.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),r.createElement("title",null,n)),r.createElement("body",null,r.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,a?r.createElement(ut,null):null)))}function Ze(e,t){return"lazy"===e.mode&&!0===t}function et(){let e=r.useContext(te);return He(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function tt(){let e=r.useContext(ne);return He(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var nt=r.createContext(void 0);function rt(){let e=r.useContext(nt);return He(e,"You must render this element inside a <HydratedRouter> element"),e}function at(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function lt(e,t,n){if(n&&!ct)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}function it(e){let{page:t}=e,n=l(e,p),{router:a}=et(),i=r.useMemo(()=>P(a.routes,t,a.basename),[a.routes,t,a.basename]);return i?r.createElement(st,u({page:t,matches:i},n)):null}function ot(e){let{manifest:t,routeModules:n}=rt(),[a,l]=r.useState([]);return r.useEffect(()=>{let r=!1;return async function(e,t,n){return $e((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await Ue(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(Ge).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?u(u({},e),{},{rel:"prefetch",as:"style"}):u(u({},e),{},{rel:"prefetch"})))}(e,t,n).then(e=>{r||l(e)}),()=>{r=!0}},[e,t,n]),a}function st(e){let{page:t,matches:n}=e,a=l(e,f),i=de(),{manifest:o,routeModules:s}=rt(),{basename:c}=et(),{loaderData:d,matches:p}=tt(),m=r.useMemo(()=>We(t,n,p,o,i,"data"),[t,n,p,o,i]),h=r.useMemo(()=>We(t,n,p,o,i,"assets"),[t,n,p,o,i]),g=r.useMemo(()=>{if(t===i.pathname+i.search+i.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{var n;let a=o.routes[t.route.id];a&&a.hasLoader&&(!m.some(e=>e.route.id===t.route.id)&&t.route.id in d&&null!==(n=s[t.route.id])&&void 0!==n&&n.shouldRevalidate||a.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let a=Qe(t,c);return r&&e.size>0&&a.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[c,d,i,o,m,n,t,s]),v=r.useMemo(()=>qe(h,o),[h,o]),y=ot(h);return r.createElement(r.Fragment,null,g.map(e=>r.createElement("link",u({key:e,rel:"prefetch",as:"fetch",href:e},a))),v.map(e=>r.createElement("link",u({key:e,rel:"modulepreload",href:e},a))),y.map(e=>{let{key:t,link:n}=e;return r.createElement("link",u({key:t},n))}))}nt.displayName="FrameworkContext";var ct=!1;function ut(e){let{manifest:t,serverHandoffString:n,isSpaMode:a,renderMeta:i,routeDiscovery:o,ssr:s}=rt(),{router:c,static:p,staticContext:f}=et(),{matches:m}=tt(),h=Ze(o,s);i&&(i.didRenderScripts=!0);let g=lt(m,null,a);r.useEffect(()=>{ct=!0},[]);let v=r.useMemo(()=>{var a;let i=f?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",o=p?"".concat(null!==(a=t.hmr)&&void 0!==a&&a.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(h?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(g.map((e,n)=>{let r="route".concat(n),a=t.routes[e.route.id];He(a,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:l,clientLoaderModule:i,clientMiddlewareModule:o,hydrateFallbackModule:s,module:c}=a,u=[...l?[{module:l,varName:"".concat(r,"_clientAction")}]:[],...i?[{module:i,varName:"".concat(r,"_clientLoader")}]:[],...o?[{module:o,varName:"".concat(r,"_clientMiddleware")}]:[],...s?[{module:s,varName:"".concat(r,"_HydrateFallback")}]:[],{module:c,varName:"".concat(r,"_main")}];return 1===u.length?"import * as ".concat(r," from ").concat(JSON.stringify(c),";"):[u.map(e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";')).join("\n"),"const ".concat(r," = {").concat(u.map(e=>"...".concat(e.varName)).join(","),"};")].join("\n")}).join("\n"),"\n  ").concat(h?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=l(e,d),a=new Set(t.state.matches.map(e=>e.route.id)),i=t.state.location.pathname.split("/").filter(Boolean),o=["/"];for(i.pop();i.length>0;)o.push("/".concat(i.join("/"))),i.pop();o.forEach(e=>{let n=P(t.routes,e,t.basename);n&&n.forEach(e=>a.add(e.route.id))});let s=[...a].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return u(u({},r),{},{routes:s,sri:!!n||void 0})}(t,c),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t)).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return r.createElement(r.Fragment,null,r.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ye(i),type:void 0})),r.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ye(o),type:"module",async:!0})))},[]),y=ct?[]:(b=t.entry.imports.concat(qe(g,t,{includeHydrateFallback:!0})),[...new Set(b)]);var b;let w="object"===typeof t.sri?t.sri:{};return ct?null:r.createElement(r.Fragment,null,"object"===typeof t.sri?r.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:w})}}):null,h?null:r.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:w[t.url],suppressHydrationWarning:!0}),r.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:w[t.entry.module],suppressHydrationWarning:!0}),y.map(t=>r.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:w[t],suppressHydrationWarning:!0})),v)}function dt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}var pt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{pt&&(window.__reactRouterVersion="7.6.2")}catch(Er){}function ft(e){let{basename:t,children:n,window:a}=e,l=r.useRef();null==l.current&&(l.current=y({window:a,v5Compat:!0}));let i=l.current,[o,s]=r.useState({action:i.action,location:i.location}),c=r.useCallback(e=>{r.startTransition(()=>s(e))},[s]);return r.useLayoutEffect(()=>i.listen(c),[i,c]),r.createElement(_e,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i})}var mt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ht=r.forwardRef(function(e,t){let n,{onClick:a,discover:i="render",prefetch:o="none",relative:s,reloadDocument:c,replace:d,state:p,target:f,to:h,preventScrollReset:g,viewTransition:v}=e,y=l(e,m),{basename:S}=r.useContext(ie),x="string"===typeof h&&mt.test(h),E=!1;if("string"===typeof h&&x&&(n=h,pt))try{let e=new URL(window.location.href),t=h.startsWith("//")?new URL(e.protocol+h):new URL(h),n=V(t.pathname,S);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:E=!0}catch(Er){w(!1,'<Link to="'.concat(h,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let C=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};b(ue(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:a}=r.useContext(ie),{hash:l,pathname:i,search:o}=ge(e,{relative:t}),s=i;return"/"!==n&&(s="/"===i?n:Y([n,i])),a.createHref({pathname:s,search:o,hash:l})}(h,{relative:s}),[T,P,L]=function(e,t){let n=r.useContext(nt),[a,l]=r.useState(!1),[i,o]=r.useState(!1),{onFocus:s,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:p}=t,f=r.useRef(null);r.useEffect(()=>{if("render"===e&&o(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{o(e.isIntersecting)})},{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}},[e]),r.useEffect(()=>{if(a){let e=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(e)}}},[a]);let m=()=>{l(!0)},h=()=>{l(!1),o(!1)};return n?"intent"!==e?[i,f,{}]:[i,f,{onFocus:at(s,m),onBlur:at(c,h),onMouseEnter:at(u,m),onMouseLeave:at(d,h),onTouchStart:at(p,m)}]:[!1,f,{}]}(o,y),N=function(e){let{target:t,replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=me(),c=de(),u=ge(e,{relative:i});return r.useCallback(r=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(r,t)){r.preventDefault();let t=void 0!==n?n:k(c)===k(u);s(e,{replace:t,state:a,preventScrollReset:l,relative:i,viewTransition:o})}},[c,s,u,n,a,t,e,l,i,o])}(h,{replace:d,state:p,target:f,preventScrollReset:g,relative:s,viewTransition:v});let j=r.createElement("a",u(u(u({},y),L),{},{href:n||C,onClick:E||c?a:function(e){a&&a(e),e.defaultPrevented||N(e)},ref:dt(t,P),target:f,"data-discover":x||"render"!==i?void 0:"true"}));return T&&!x?r.createElement(r.Fragment,null,j,r.createElement(it,{page:C})):j});ht.displayName="Link",r.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:o=!1,style:s,to:c,viewTransition:d,children:p}=e,f=l(e,h),m=ge(c,{relative:f.relative}),g=de(),v=r.useContext(ne),{navigator:y,basename:w}=r.useContext(ie),S=null!=v&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.useContext(re);b(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=yt("useViewTransitionState"),l=ge(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=V(n.currentLocation.pathname,a)||n.currentLocation.pathname,o=V(n.nextLocation.pathname,a)||n.nextLocation.pathname;return null!=B(l.pathname,o)||null!=B(l.pathname,i)}(m)&&!0===d,x=y.encodeLocation?y.encodeLocation(m).pathname:m.pathname,k=g.pathname,E=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;a||(k=k.toLowerCase(),E=E?E.toLowerCase():null,x=x.toLowerCase()),E&&w&&(E=V(E,w)||E);const C="/"!==x&&x.endsWith("/")?x.length-1:x.length;let T,P=k===x||!o&&k.startsWith(x)&&"/"===k.charAt(C),L=null!=E&&(E===x||!o&&E.startsWith(x)&&"/"===E.charAt(x.length)),N={isActive:P,isPending:L,isTransitioning:S},j=P?n:void 0;T="function"===typeof i?i(N):[i,P?"active":null,L?"pending":null,S?"transitioning":null].filter(Boolean).join(" ");let _="function"===typeof s?s(N):s;return r.createElement(ht,u(u({},f),{},{"aria-current":j,className:T,ref:t,style:_,to:c,viewTransition:d}),"function"===typeof p?p(N):p)}).displayName="NavLink";var gt=r.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:a,navigate:i,reloadDocument:o,replace:s,state:c,method:d=ze,action:p,onSubmit:f,relative:m,preventScrollReset:h,viewTransition:v}=e,y=l(e,g),w=St(),S=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=r.useContext(ie),a=r.useContext(se);b(a,"useFormAction must be used inside a RouteContext");let[l]=a.matches.slice(-1),i=u({},ge(e||".",{relative:t})),o=de();if(null==e){i.search=o.search;let e=new URLSearchParams(i.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();i.search=n?"?".concat(n):""}}e&&"."!==e||!l.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:Y([n,i.pathname]));return k(i)}(p,{relative:m}),x="get"===d.toLowerCase()?"get":"post",E="string"===typeof p&&mt.test(p);return r.createElement("form",u(u({ref:t,method:x,action:S,onSubmit:o?f:e=>{if(f&&f(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||d;w(t||e.currentTarget,{fetcherKey:a,method:n,navigate:i,replace:s,state:c,relative:m,preventScrollReset:h,viewTransition:v})}},y),{},{"data-discover":E||"render"!==n?void 0:"true"}))});function vt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function yt(e){let t=r.useContext(te);return b(t,vt(e)),t}gt.displayName="Form";var bt=0,wt=()=>"__".concat(String(++bt),"__");function St(){let{router:e}=yt("useSubmit"),{basename:t}=r.useContext(ie),n=Te("useRouteId");return r.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:l,method:i,encType:o,formData:s,body:c}=Be(r,t);if(!1===a.navigate){let t=a.fetcherKey||wt();await e.fetch(t,n,a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:c,formMethod:a.method||i,formEncType:a.encType||o,flushSync:a.flushSync})}else await e.navigate(a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:c,formMethod:a.method||i,formEncType:a.encType||o,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}var xt=n(579);const kt=()=>(0,xt.jsxs)("header",{children:[(0,xt.jsx)("div",{className:"logo",children:(0,xt.jsx)(ht,{to:"/",children:(0,xt.jsx)("img",{src:"/logo.PNG",alt:"Logo",className:"logo-img"})})}),(0,xt.jsx)("a",{href:"https://www.canva.com/design/DAGNjKc0Cr0/nJ-kiMZTpFhVUD6B6nyPYg/view?utm_content=DAGNjKc0Cr0&utm_campaign=designshare&utm_medium=link2&utm_source=uniquelinks&utlId=h6802188a93",className:"cv-button",target:"_blank",rel:"noopener noreferrer",children:"Get CV"})]}),Et=()=>(0,xt.jsxs)("section",{className:"intro",children:[(0,xt.jsxs)("h1",{children:["Welcome, I'M",(0,xt.jsx)("br",{}),(0,xt.jsx)("span",{className:"highlight",children:"Chouchane Med Amine"})]}),(0,xt.jsx)("p",{children:"Software Developer"})]}),Ct=()=>(0,xt.jsxs)("section",{className:"intro-crafting",children:[(0,xt.jsxs)("div",{className:"crafting-content",children:[(0,xt.jsxs)("h2",{children:["CRAFTING UNIQUE",(0,xt.jsx)("br",{}),"DIGITAL EXPERIENCES"]}),(0,xt.jsx)("p",{children:"I specialize in delivering creative and functional solutions, combining design expertise with innovative web development to bring ideas to life."}),(0,xt.jsxs)("div",{className:"services",children:[(0,xt.jsxs)("div",{className:"service-item",children:[(0,xt.jsx)("img",{src:"/web-development-icon.jpg",alt:"Icon",style:{width:"50px",height:"50px",borderRadius:"50%",objectFit:"cover",display:"block",margin:"0 auto 20px",border:"2px solid #FF2D55"}}),(0,xt.jsx)("h3",{children:"WEB DEV"}),(0,xt.jsx)("p",{children:"Creating responsive, modern websites tailored to meet client needs and goals."})]}),(0,xt.jsxs)("div",{className:"service-item",children:[(0,xt.jsx)("img",{src:"/branding.jpeg",alt:"Icon",style:{width:"50px",height:"50px",borderRadius:"50%",objectFit:"cover",display:"block",margin:"0 auto 20px",border:"2px solid #FF2D55"}}),(0,xt.jsx)("h3",{children:"BRANDING"}),(0,xt.jsx)("p",{children:"Building strong brand identities that resonate and leave a lasting impression."})]}),(0,xt.jsxs)("div",{className:"service-item",children:[(0,xt.jsx)("img",{src:"/UI UX.png",alt:"Icon",style:{width:"50px",height:"50px",borderRadius:"50%",objectFit:"cover",display:"block",margin:"0 auto 20px",border:"2px solid #FF2D55"}}),(0,xt.jsx)("h3",{children:"UI/UX DESIGN"}),(0,xt.jsx)("p",{children:"Enhancing usability and aesthetics through well-thought-out interface designs."})]})]})]}),(0,xt.jsxs)("div",{className:"intro-image-wrapper",children:[(0,xt.jsx)("div",{className:"intro-image",children:(0,xt.jsx)("img",{src:"/Picsart_Pro.png",alt:"Chouchane Med Amine - Software Developer"})}),(0,xt.jsxs)("div",{className:"social-links",children:[(0,xt.jsx)("a",{href:"https://www.linkedin.com/in/chouchane-amine-932324320/",children:(0,xt.jsx)("img",{src:"/icons8-linkedin-circled-64.png",alt:"Linkedin"})}),(0,xt.jsx)("a",{href:"https://www.instagram.com/_ami_nos_",children:(0,xt.jsx)("img",{src:"/icons8-instagram-64.png",alt:"Instagram"})}),(0,xt.jsx)("a",{href:"https://github.com/aminos555",children:(0,xt.jsx)("img",{src:"/icons8-github-64.png",alt:"GitHub"})})]}),(0,xt.jsxs)("div",{className:"skills-bar",children:[(0,xt.jsx)("span",{children:"UI/UX DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"DIGITAL CREATIVITY"})," \u2022 ",(0,xt.jsx)("span",{children:"E-COMMERCE"})]})]})]}),Tt=()=>(0,xt.jsx)("div",{className:"skills-ticker",children:(0,xt.jsxs)("div",{className:"ticker-track",children:[(0,xt.jsx)("span",{children:"UI/UX DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"GRAPHIC DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"WEB DEVELOPMENT"})," \u2022 ",(0,xt.jsx)("span",{children:"UI/UX DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"BRANDING"})," \u2022 ",(0,xt.jsx)("span",{children:"WEBSITE DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"DIGITAL CREATIVITY"})," \u2022 ",(0,xt.jsx)("span",{children:"E-COMMERCE SOLUTIONS"})," \u2022 ",(0,xt.jsx)("span",{children:"CUSTOM DEVELOPMENT"}),(0,xt.jsx)("span",{children:"UI/UX DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"GRAPHIC DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"WEB DEVELOPMENT"})," \u2022 ",(0,xt.jsx)("span",{children:"UI/UX DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"BRANDING"})," \u2022 ",(0,xt.jsx)("span",{children:"WEBSITE DESIGN"})," \u2022 ",(0,xt.jsx)("span",{children:"DIGITAL CREATIVITY"})," \u2022 ",(0,xt.jsx)("span",{children:"E-COMMERCE SOLUTIONS"})," \u2022 ",(0,xt.jsx)("span",{children:"CUSTOM DEVELOPMENT"})]})}),Pt=()=>(0,xt.jsxs)("section",{className:"statistics",children:[(0,xt.jsx)("h2",{children:"STATISTICS"}),(0,xt.jsxs)("div",{className:"stats-grid",children:[(0,xt.jsxs)("div",{className:"stat",children:[(0,xt.jsx)("h3",{children:"9+"}),(0,xt.jsx)("p",{children:"WEBSITES DEVELOPED"})]}),(0,xt.jsxs)("div",{className:"stat",children:[(0,xt.jsx)("h3",{children:"14"}),(0,xt.jsx)("p",{children:"Web DESIGN PROJECTS"})]}),(0,xt.jsxs)("div",{className:"stat",children:[(0,xt.jsx)("h3",{children:"2+"}),(0,xt.jsx)("p",{children:"YEARS OF EXPERIENCE"})]})]}),(0,xt.jsx)("div",{className:"stats-image",children:(0,xt.jsx)("img",{src:"/business-8398066.jpg",alt:"Business statistics visualization"})}),(0,xt.jsx)(ht,{to:"/contact",className:"action-button",children:"LET'S TALK"})]}),Lt=[{id:1,slug:"frontend-receeto",title:"Frontend Developer Angular",company:"Company : Receeto",companyLink:"https://receeto.com",duration:"2/2025 - 6/2025",logo:"/Receeto_logo.jpg",logoAlt:"Receeto Logo",summary:"A smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.",roleOverview:"Note: Due to an active (NDA) contract, I'm unable to share detailed specifics about the project. However, I can briefly describe the technical scope and my personal contributions without disclosing confidential information.\n\nIt's a smart and responsive web application built with Angular, designed to enhance shopping and budgeting efficiency through data-driven insights.\n\nKey Highlights:\nExpense Tracking & Budgeting: Tools for monitoring transactions and setting personalized financial goals.\n\nSpending Analytics: Interactive category-based analysis to help users make informed decisions.\n\nPerformance Optimization:\n\u2022 Lazy loading for improved speed\n\u2022 Critical CSS and production build optimization\n\nTech Stack & Architecture:\n\u2022 Angular SPA with reactive state (signals)\n\u2022 Fully responsive design for mobile and desktop\n\u2022 Custom financial data visualizations using charts\n\nThis project showcases my expertise in Angular development, performance tuning, and crafting scalable, user-centric interfaces\u2014while respecting the confidentiality of the client's program.",responsibilities:["Develop responsive web applications using Angular and modern frontend technologies","Implement financial data visualizations and interactive charts","Optimize application performance through lazy loading and build optimization","Create expense tracking and budgeting tools with real-time data processing","Build responsive interfaces for both mobile and desktop platforms","Implement Angular reactive state management using signals"],skills:{Frontend:["Angular","TypeScript","RxJS","Angular Signals","Angular CLI"],Styling:["CSS3","SASS/SCSS","Angular Material","Responsive Design","Bootstrap"],"Tools & Testing":["Git","Angular CLI","Webpack","Lighthouse (for performance auditing)","Figma"]},accomplishments:[{metric:"40%",description:"Improved application performance through lazy loading and build optimization"},{metric:"100%",description:"Responsive design compatibility across mobile and desktop platforms"},{metric:"NDA",description:"Confidential project delivered successfully while maintaining client privacy"}],projects:[{title:"Img 1",description:"NDA - details confidential",images:["../NDA.jpg","../NDA.jpg","../NDA.jpg"],technologies:["Angular","TypeScript","Charts.js"],liveUrl:"https://receeto.com"},{title:"Img 2",description:"NDA - details confidential",images:["../NDA.jpg","../NDA.jpg"],technologies:["Angular","RxJS","Angular Material"],liveUrl:"https://receeto.com"}]},{id:2,slug:"3d-ecommerce-platform",title:"3D-ecommerce platform UI/UX Designer",company:"DigitalStudio Creative",companyLink:"https://threed-e-commerce.onrender.com",duration:"2022 - 2023",logo:"/3D E Logo.png",logoAlt:"DigitalStudio Creative Logo",summary:"Developed an innovative 3D E-Commerce platform that revolutionizes online shopping through immersive 3D product visualization. Created interactive shopping experiences with photorealistic product models, increasing user engagement by 40%.",roleOverview:"As UI/UX Designer & Frontend Developer at DigitalStudio Creative, I spearheaded the development of a groundbreaking 3D E-Commerce platform that transforms how customers interact with products online. This project bridges the gap between online and in-store shopping experiences through advanced 3D visualization technologies.\n\nThe platform specializes in photorealistic product visualization for complex technology products including smartphones, computers, gaming consoles, and wearable technology. I designed and implemented a comprehensive solution that converts technical specifications into interactive 3D journeys, allowing users to explore every detail before purchasing.\n\nKey achievements include creating a mobile-first responsive design with touch-optimized 3D controls, implementing fallback mechanisms for graceful degradation, and establishing a scalable architecture that serves as the foundation for next-generation e-commerce experiences.",responsibilities:["Design user interfaces and experiences for the 3D e-commerce platform","Develop responsive frontend using React.js and modern web technologies","Implement 3D model visualization with Three.js/WebGL and optimized mobile interactions","Create comprehensive design systems for consistent user experience across devices","Conduct usability testing to refine the shopping experience and 3D interactions","Optimize application performance across various devices and network conditions","Collaborate with clients to understand business requirements and technical constraints","Develop fallback mechanisms and error boundaries for graceful failure handling","Implement progressive enhancement and mobile-first responsive design principles"],skills:{Frontend:["React.js","Three.js/WebGL","JavaScript","CSS3","React Router","Axios"],"3D Technologies":["Model Viewer","GLTF/GLB","OrbitControls","Real-time Rendering"],Backend:["Node.js","Express.js","MongoDB","RESTful APIs"],"Design & UX":["Figma","Responsive Design","Mobile-First Design","User Testing","Performance Optimization"],"Tools & Deployment":["Git","Render","Webpack","Font Awesome","Chrome DevTools"]},accomplishments:[{metric:"40%",description:"Increased user engagement through improved UX design and 3D interactions"},{metric:"95%",description:"Client satisfaction rate based on project feedback"}],projects:[{title:"3D Product Visualization Engine",description:"Interactive 3D models with zoom, rotate, and pan capabilities for smartphones, computers, and gaming consoles. Features touch-optimized controls and fallback mechanisms.",images:["../3D E Commerce Home.PNG","../home mobil mode.PNG"],technologies:["Three.js","WebGL","GLTF/GLB","OrbitControls"],liveUrl:"https://threed-e-commerce.onrender.com"},{title:"Mobile-Responsive Shopping Interface",description:"Comprehensive product grid with responsive layout, cart functionality, and mobile-optimized 3D controls. Minimum 44px touch targets for enhanced mobile experience.",images:["../All product pc.PNG","../all products mobil mode.PNG"],technologies:["React.js","CSS3","Responsive Design","Mobile UX"],liveUrl:"https://threed-e-commerce.onrender.com"},{title:"Performance-Optimized Architecture",description:"Scalable client-server architecture with MongoDB backend, RESTful APIs, and automated deployment. Features fallback data and optimized build process.",images:["../air pods pc mode.PNG","../air pods mobil mode.PNG"],technologies:["Node.js","Express.js","MongoDB","Render","API Design"],liveUrl:"https://threed-e-commerce-backend.onrender.com"}]}],Nt=()=>{const e=e=>{e.stopPropagation()};return(0,xt.jsxs)("section",{className:"experience",id:"experience",children:[(0,xt.jsx)("h2",{children:"Professional Experience"}),(0,xt.jsxs)("div",{className:"timeline",children:[Lt.map((t,n)=>(0,xt.jsxs)("div",{className:"timeline-item",children:[(0,xt.jsx)("div",{className:"timeline-dot"}),(0,xt.jsx)(ht,{to:"/job/".concat(t.slug),className:"timeline-content-link",children:(0,xt.jsxs)("div",{className:"timeline-content",children:[(0,xt.jsx)("img",{src:t.logo,alt:t.logoAlt,className:"company-logo"}),(0,xt.jsx)("h3",{className:"job-title",children:t.title}),(0,xt.jsx)("h4",{className:"company-name",children:t.company}),t.companyLink&&(0,xt.jsx)("p",{className:"company-link",children:(0,xt.jsx)("a",{href:t.companyLink,target:"_blank",rel:"noopener noreferrer",onClick:e,children:t.companyLink})}),(0,xt.jsx)("p",{className:"job-duration",children:t.duration}),(0,xt.jsx)("p",{className:"job-description",children:t.summary}),(0,xt.jsx)("div",{className:"view-details",children:(0,xt.jsx)("span",{children:"View Details \u2192"})})]})})]},t.id)),0===Lt.length&&(0,xt.jsx)("div",{className:"timeline-item",children:(0,xt.jsx)("div",{className:"timeline-content",children:(0,xt.jsx)("p",{children:"No jobs found in jobsData.js"})})})]}),(0,xt.jsxs)("div",{style:{textAlign:"center",marginTop:"40px",color:"rgba(255,255,255,0.6)",fontSize:"14px"},children:["Currently showing ",Lt.length," job",1!==Lt.length?"s":""," from jobsData.js"]})]})},jt=()=>{const e=(0,r.useRef)(null);(0,r.useEffect)(()=>{const t=e.current;if(!t)return;let n,r,a,l=!1,i=!1;const o=()=>{i=!0},s=()=>{i=!1},c=e=>{l=!0,i=!0,t.classList.add("dragging"),n=e.pageX,r=t.scrollLeft},u=e=>{if(!l)return;const a=1.8*(e.pageX-n);t.scrollLeft=r-a;const i=t.scrollWidth/2;t.scrollLeft>=i?t.scrollLeft=0:t.scrollLeft<0&&(t.scrollLeft=i-1)},d=()=>{l&&(l=!1,t.classList.remove("dragging"),setTimeout(()=>{},100))},p=e=>{e.preventDefault();const n=e.deltaY>0?50:-50;t.scrollLeft+=n;const r=t.scrollWidth/2;t.scrollLeft>=r?t.scrollLeft=0:t.scrollLeft<0&&(t.scrollLeft=r-1)};return t.addEventListener("mouseenter",o),t.addEventListener("mouseleave",s),t.addEventListener("mousedown",c),t.addEventListener("mousemove",u),document.addEventListener("mouseup",d),t.addEventListener("wheel",p,{passive:!1}),a&&clearInterval(a),a=setInterval(()=>{if(!l&&!i){t.scrollLeft+=1;const e=t.scrollWidth/2;t.scrollLeft>=e&&(t.scrollLeft=0)}},16),()=>{a&&(clearInterval(a),a=null),t.removeEventListener("mouseenter",o),t.removeEventListener("mouseleave",s),t.removeEventListener("mousedown",c),t.removeEventListener("mousemove",u),document.removeEventListener("mouseup",d),t.removeEventListener("wheel",p)}},[]);const t=[{href:"https://threed-e-commerce.onrender.com",image:"/3D E-Comm.PNG",alt:"3D Ecommerce",title:"3D Ecommerce (Finish Soon)"},{href:"#",image:"/ex1.webp",alt:"Yalla Go Posters",title:"Will be deployed soon."},{href:"#",image:"/ex2.png",alt:"Nexit Brand Identity",title:"Will be deployed soon."},{href:"#",image:"/ex3.webp",alt:"Yalla Go Posters",title:"Will be deployed soon."},{href:"#",image:"/ex4.1.png",alt:"Yalla Go Posters",title:"Will be deployed soon."},{href:"#",image:"/ex5.png",alt:"Yalla Go Posters",title:"Will be deployed soon."},{href:"#",image:"/bussniss web UI.PNG",alt:"Business Web UI",title:"Available in git Will be deployed soon."}];return(0,xt.jsxs)("section",{className:"portfolio",children:[(0,xt.jsxs)("h2",{children:["Top Projects",(0,xt.jsx)("br",{})]}),(0,xt.jsx)("button",{className:"discover-button",onClick:()=>console.log("Discover more clicked"),children:"DISCOVER MORE"}),(0,xt.jsx)("div",{className:"portfolio-carousel",children:(0,xt.jsx)("div",{className:"carousel-track",ref:e,children:[...t,...t].map((e,t)=>(0,xt.jsx)("div",{className:"portfolio-item",children:(0,xt.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",children:[(0,xt.jsx)("img",{src:e.image,alt:e.alt}),(0,xt.jsx)("p",{children:e.title})]})},t))})})]})},_t=()=>(0,xt.jsxs)("section",{className:"client-thoughts",children:[(0,xt.jsx)("div",{className:"quote-icon"}),(0,xt.jsx)("h2",{children:"CLIENT THOUGHTS"}),(0,xt.jsx)("h3",{children:'"E-COMMERCE EXPERTISE"'}),(0,xt.jsx)("p",{children:"Will be Available Soon . . . "}),(0,xt.jsx)("div",{className:"thoughts-image",children:(0,xt.jsx)("img",{src:"/client touch.jpg",alt:"E-commerce Expertise Visual",style:{width:"100%",maxWidth:"600px",height:"auto",margin:"20px 0",border:"2px solid #FF2D55",borderRadius:"10px"}})})]}),Ot=()=>(0,xt.jsx)("section",{className:"contact",children:(0,xt.jsxs)("div",{className:"contact-overlay",children:[(0,xt.jsx)("h2",{children:"LET'S CREATE TOGETHER"}),(0,xt.jsxs)("form",{action:"https://formspree.io/f/mblgroaz",method:"POST",children:[(0,xt.jsx)("label",{htmlFor:"name",children:"YOUR NAME"}),(0,xt.jsx)("input",{type:"text",id:"name",name:"name",placeholder:"NAME",required:!0}),(0,xt.jsx)("label",{htmlFor:"email",children:"YOUR EMAIL"}),(0,xt.jsx)("input",{type:"email",id:"email",name:"email",placeholder:"EMAIL",required:!0}),(0,xt.jsx)("label",{htmlFor:"services",children:"WHAT SERVICES ARE YOU LOOKING FOR?"}),(0,xt.jsx)("input",{type:"text",id:"services",name:"services",placeholder:"Web Design, Graphic design...",required:!0}),(0,xt.jsx)("label",{htmlFor:"message",children:"YOUR MESSAGE"}),(0,xt.jsx)("textarea",{id:"message",name:"message",placeholder:"YOUR MESSAGE",required:!0}),(0,xt.jsxs)("label",{htmlFor:"linkedin",children:["Your LinkedIn ",(0,xt.jsx)("span",{style:{color:"green"},children:"(Optional)"})]}),(0,xt.jsx)("input",{type:"url",id:"linkedin",name:"linkedin",placeholder:"https://www.linkedin.com/in/your-profile"}),(0,xt.jsx)("button",{type:"submit",className:"submit-button",children:"SEND"})]})]})}),Mt=()=>(0,xt.jsxs)(xt.Fragment,{children:[(0,xt.jsx)("footer",{children:(0,xt.jsx)("p",{children:"2025 REECRAFT. ALL RIGHTS RESERVED."})}),(0,xt.jsx)("button",{className:"back-to-top",onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),"aria-label":"Back to top",children:"\u2191"})]}),zt=()=>(0,xt.jsxs)("div",{children:[(0,xt.jsx)(kt,{}),(0,xt.jsx)(Et,{}),(0,xt.jsx)(Ct,{}),(0,xt.jsx)(Tt,{}),(0,xt.jsx)(Pt,{}),(0,xt.jsx)(Nt,{}),(0,xt.jsx)(jt,{}),(0,xt.jsx)(Tt,{}),(0,xt.jsx)(_t,{}),(0,xt.jsx)(Ot,{}),(0,xt.jsx)(Mt,{})]});var At=n(950);function Dt(e){return null!==e&&"object"===typeof e&&"constructor"in e&&e.constructor===Object}function Rt(e,t){void 0===e&&(e={}),void 0===t&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>n.indexOf(e)<0).forEach(n=>{"undefined"===typeof e[n]?e[n]=t[n]:Dt(t[n])&&Dt(e[n])&&Object.keys(t[n]).length>0&&Rt(e[n],t[n])})}const It={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function Ft(){const e="undefined"!==typeof document?document:{};return Rt(e,It),e}const Bt={document:It,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"===typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!==typeof setTimeout&&clearTimeout(e)}};function Ht(){const e="undefined"!==typeof window?window:{};return Rt(e,Bt),e}function Ut(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function Vt(){return Date.now()}function Gt(e,t){void 0===t&&(t="x");const n=Ht();let r,a,l;const i=function(e){const t=Ht();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}(e);return n.WebKitCSSMatrix?(a=i.transform||i.webkitTransform,a.split(",").length>6&&(a=a.split(", ").map(e=>e.replace(",",".")).join(", ")),l=new n.WebKitCSSMatrix("none"===a?"":a)):(l=i.MozTransform||i.OTransform||i.MsTransform||i.msTransform||i.transform||i.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=l.toString().split(",")),"x"===t&&(a=n.WebKitCSSMatrix?l.m41:16===r.length?parseFloat(r[12]):parseFloat(r[4])),"y"===t&&(a=n.WebKitCSSMatrix?l.m42:16===r.length?parseFloat(r[13]):parseFloat(r[5])),a||0}function Wt(e){return"object"===typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function qt(e){return"undefined"!==typeof window&&"undefined"!==typeof window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function $t(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(void 0!==r&&null!==r&&!qt(r)){const n=Object.keys(Object(r)).filter(e=>t.indexOf(e)<0);for(let t=0,a=n.length;t<a;t+=1){const a=n[t],l=Object.getOwnPropertyDescriptor(r,a);void 0!==l&&l.enumerable&&(Wt(e[a])&&Wt(r[a])?r[a].__swiper__?e[a]=r[a]:$t(e[a],r[a]):!Wt(e[a])&&Wt(r[a])?(e[a]={},r[a].__swiper__?e[a]=r[a]:$t(e[a],r[a])):e[a]=r[a])}}}return e}function Yt(e,t,n){e.style.setProperty(t,n)}function Xt(e){let{swiper:t,targetPosition:n,side:r}=e;const a=Ht(),l=-t.translate;let i,o=null;const s=t.params.speed;t.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(t.cssModeFrameID);const c=n>l?"next":"prev",u=(e,t)=>"next"===c&&e>=t||"prev"===c&&e<=t,d=()=>{i=(new Date).getTime(),null===o&&(o=i);const e=Math.max(Math.min((i-o)/s,1),0),c=.5-Math.cos(e*Math.PI)/2;let p=l+c*(n-l);if(u(p,n)&&(p=n),t.wrapperEl.scrollTo({[r]:p}),u(p,n))return t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:p})}),void a.cancelAnimationFrame(t.cssModeFrameID);t.cssModeFrameID=a.requestAnimationFrame(d)};d()}function Qt(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function Kt(e,t){void 0===t&&(t="");const n=Ht(),r=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t?r.filter(e=>e.matches(t)):r}function Jt(e){try{return void console.warn(e)}catch(t){}}function Zt(e,t){void 0===t&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:function(e){return void 0===e&&(e=""),e.trim().split(" ").filter(e=>!!e.trim())}(t)),n}function en(e,t){return Ht().getComputedStyle(e,null).getPropertyValue(t)}function tn(e){let t,n=e;if(n){for(t=0;null!==(n=n.previousSibling);)1===n.nodeType&&(t+=1);return t}}function nn(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function rn(e,t,n){const r=Ht();return n?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function an(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function ln(e,t){void 0===t&&(t=""),"undefined"!==typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}let on,sn,cn;function un(){return on||(on=function(){const e=Ht(),t=Ft();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),on}function dn(e){return void 0===e&&(e={}),sn||(sn=function(e){let{userAgent:t}=void 0===e?{}:e;const n=un(),r=Ht(),a=r.navigator.platform,l=t||r.navigator.userAgent,i={ios:!1,android:!1},o=r.screen.width,s=r.screen.height,c=l.match(/(Android);?[\s\/]+([\d.]+)?/);let u=l.match(/(iPad).*OS\s([\d_]+)/);const d=l.match(/(iPod)(.*OS\s([\d_]+))?/),p=!u&&l.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f="Win32"===a;let m="MacIntel"===a;return!u&&m&&n.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf("".concat(o,"x").concat(s))>=0&&(u=l.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),m=!1),c&&!f&&(i.os="android",i.android=!0),(u||p||d)&&(i.os="ios",i.ios=!0),i}(e)),sn}function pn(){return cn||(cn=function(){const e=Ht(),t=dn();let n=!1;function r(){const t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&t.indexOf("chrome")<0&&t.indexOf("android")<0}if(r()){const t=String(e.navigator.userAgent);if(t.includes("Version/")){const[e,r]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));n=e<16||16===e&&r<2}}const a=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),l=r();return{isSafari:n||l,needPerspectiveFix:n,need3dFix:l||a&&t.ios,isWebView:a}}()),cn}var fn={on(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed)return r;if("function"!==typeof t)return r;const a=n?"unshift":"push";return e.split(" ").forEach(e=>{r.eventsListeners[e]||(r.eventsListeners[e]=[]),r.eventsListeners[e][a](t)}),r},once(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed)return r;if("function"!==typeof t)return r;function a(){r.off(e,a),a.__emitterProxy&&delete a.__emitterProxy;for(var n=arguments.length,l=new Array(n),i=0;i<n;i++)l[i]=arguments[i];t.apply(r,l)}return a.__emitterProxy=t,r.on(e,a,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed)return n;if("function"!==typeof e)return n;const r=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[r](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed?n:n.eventsListeners?(e.split(" ").forEach(e=>{"undefined"===typeof t?n.eventsListeners[e]=[]:n.eventsListeners[e]&&n.eventsListeners[e].forEach((r,a)=>{(r===t||r.__emitterProxy&&r.__emitterProxy===t)&&n.eventsListeners[e].splice(a,1)})}),n):n},emit(){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsListeners)return e;let t,n,r;for(var a=arguments.length,l=new Array(a),i=0;i<a;i++)l[i]=arguments[i];"string"===typeof l[0]||Array.isArray(l[0])?(t=l[0],n=l.slice(1,l.length),r=e):(t=l[0].events,n=l[0].data,r=l[0].context||e),n.unshift(r);return(Array.isArray(t)?t:t.split(" ")).forEach(t=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(e=>{e.apply(r,[t,...n])}),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach(e=>{e.apply(r,n)})}),e}};const mn=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};const hn=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};const gn=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=t.closest(e.isElement?"swiper-slide":".".concat(e.params.slideClass));if(n){let t=n.querySelector(".".concat(e.params.lazyPreloaderClass));!t&&e.isElement&&(n.shadowRoot?t=n.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)):requestAnimationFrame(()=>{n.shadowRoot&&(t=n.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)),t&&t.remove())})),t&&t.remove()}},vn=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},yn=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const r="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),a=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const n=a,l=[n-t];return l.push(...Array.from({length:t}).map((e,t)=>n+r+t)),void e.slides.forEach((t,n)=>{l.includes(t.column)&&vn(e,n)})}const l=a+r-1;if(e.params.rewind||e.params.loop)for(let i=a-t;i<=l+t;i+=1){const t=(i%n+n)%n;(t<a||t>l)&&vn(e,t)}else for(let i=Math.max(a-t,0);i<=Math.min(l+t,n-1);i+=1)i!==a&&(i>l||i<a)&&vn(e,i)};var bn={updateSize:function(){const e=this;let t,n;const r=e.el;t="undefined"!==typeof e.params.width&&null!==e.params.width?e.params.width:r.clientWidth,n="undefined"!==typeof e.params.height&&null!==e.params.height?e.params.height:r.clientHeight,0===t&&e.isHorizontal()||0===n&&e.isVertical()||(t=t-parseInt(en(r,"padding-left")||0,10)-parseInt(en(r,"padding-right")||0,10),n=n-parseInt(en(r,"padding-top")||0,10)-parseInt(en(r,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))},updateSlides:function(){const e=this;function t(t,n){return parseFloat(t.getPropertyValue(e.getDirectionLabel(n))||0)}const n=e.params,{wrapperEl:r,slidesEl:a,size:l,rtlTranslate:i,wrongRTL:o}=e,s=e.virtual&&n.virtual.enabled,c=s?e.virtual.slides.length:e.slides.length,u=Kt(a,".".concat(e.params.slideClass,", swiper-slide")),d=s?e.virtual.slides.length:u.length;let p=[];const f=[],m=[];let h=n.slidesOffsetBefore;"function"===typeof h&&(h=n.slidesOffsetBefore.call(e));let g=n.slidesOffsetAfter;"function"===typeof g&&(g=n.slidesOffsetAfter.call(e));const v=e.snapGrid.length,y=e.slidesGrid.length;let b=n.spaceBetween,w=-h,S=0,x=0;if("undefined"===typeof l)return;"string"===typeof b&&b.indexOf("%")>=0?b=parseFloat(b.replace("%",""))/100*l:"string"===typeof b&&(b=parseFloat(b)),e.virtualSize=-b,u.forEach(e=>{i?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(Yt(r,"--swiper-centered-offset-before",""),Yt(r,"--swiper-centered-offset-after",""));const k=n.grid&&n.grid.rows>1&&e.grid;let E;k?e.grid.initSlides(u):e.grid&&e.grid.unsetSlides();const C="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter(e=>"undefined"!==typeof n.breakpoints[e].slidesPerView).length>0;for(let T=0;T<d;T+=1){let r;if(E=0,u[T]&&(r=u[T]),k&&e.grid.updateSlide(T,r,u),!u[T]||"none"!==en(r,"display")){if("auto"===n.slidesPerView){C&&(u[T].style[e.getDirectionLabel("width")]="");const a=getComputedStyle(r),l=r.style.transform,i=r.style.webkitTransform;if(l&&(r.style.transform="none"),i&&(r.style.webkitTransform="none"),n.roundLengths)E=e.isHorizontal()?rn(r,"width",!0):rn(r,"height",!0);else{const e=t(a,"width"),n=t(a,"padding-left"),l=t(a,"padding-right"),i=t(a,"margin-left"),o=t(a,"margin-right"),s=a.getPropertyValue("box-sizing");if(s&&"border-box"===s)E=e+i+o;else{const{clientWidth:t,offsetWidth:a}=r;E=e+n+l+i+o+(a-t)}}l&&(r.style.transform=l),i&&(r.style.webkitTransform=i),n.roundLengths&&(E=Math.floor(E))}else E=(l-(n.slidesPerView-1)*b)/n.slidesPerView,n.roundLengths&&(E=Math.floor(E)),u[T]&&(u[T].style[e.getDirectionLabel("width")]="".concat(E,"px"));u[T]&&(u[T].swiperSlideSize=E),m.push(E),n.centeredSlides?(w=w+E/2+S/2+b,0===S&&0!==T&&(w=w-l/2-b),0===T&&(w=w-l/2-b),Math.abs(w)<.001&&(w=0),n.roundLengths&&(w=Math.floor(w)),x%n.slidesPerGroup===0&&p.push(w),f.push(w)):(n.roundLengths&&(w=Math.floor(w)),(x-Math.min(e.params.slidesPerGroupSkip,x))%e.params.slidesPerGroup===0&&p.push(w),f.push(w),w=w+E+b),e.virtualSize+=E+b,S=E,x+=1}}if(e.virtualSize=Math.max(e.virtualSize,l)+g,i&&o&&("slide"===n.effect||"coverflow"===n.effect)&&(r.style.width="".concat(e.virtualSize+b,"px")),n.setWrapperSize&&(r.style[e.getDirectionLabel("width")]="".concat(e.virtualSize+b,"px")),k&&e.grid.updateWrapperSize(E,p),!n.centeredSlides){const t=[];for(let r=0;r<p.length;r+=1){let a=p[r];n.roundLengths&&(a=Math.floor(a)),p[r]<=e.virtualSize-l&&t.push(a)}p=t,Math.floor(e.virtualSize-l)-Math.floor(p[p.length-1])>1&&p.push(e.virtualSize-l)}if(s&&n.loop){const t=m[0]+b;if(n.slidesPerGroup>1){const r=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),a=t*n.slidesPerGroup;for(let e=0;e<r;e+=1)p.push(p[p.length-1]+a)}for(let r=0;r<e.virtual.slidesBefore+e.virtual.slidesAfter;r+=1)1===n.slidesPerGroup&&p.push(p[p.length-1]+t),f.push(f[f.length-1]+t),e.virtualSize+=t}if(0===p.length&&(p=[0]),0!==b){const t=e.isHorizontal()&&i?"marginLeft":e.getDirectionLabel("marginRight");u.filter((e,t)=>!(n.cssMode&&!n.loop)||t!==u.length-1).forEach(e=>{e.style[t]="".concat(b,"px")})}if(n.centeredSlides&&n.centeredSlidesBounds){let e=0;m.forEach(t=>{e+=t+(b||0)}),e-=b;const t=e>l?e-l:0;p=p.map(e=>e<=0?-h:e>t?t+g:e)}if(n.centerInsufficientSlides){let e=0;m.forEach(t=>{e+=t+(b||0)}),e-=b;const t=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(e+t<l){const n=(l-e-t)/2;p.forEach((e,t)=>{p[t]=e-n}),f.forEach((e,t)=>{f[t]=e+n})}}if(Object.assign(e,{slides:u,snapGrid:p,slidesGrid:f,slidesSizesGrid:m}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){Yt(r,"--swiper-centered-offset-before","".concat(-p[0],"px")),Yt(r,"--swiper-centered-offset-after","".concat(e.size/2-m[m.length-1]/2,"px"));const t=-e.snapGrid[0],n=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(e=>e+t),e.slidesGrid=e.slidesGrid.map(e=>e+n)}if(d!==c&&e.emit("slidesLengthChange"),p.length!==v&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),f.length!==y&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!s&&!n.cssMode&&("slide"===n.effect||"fade"===n.effect)){const t="".concat(n.containerModifierClass,"backface-hidden"),r=e.el.classList.contains(t);d<=n.maxBackfaceHiddenSlides?r||e.el.classList.add(t):r&&e.el.classList.remove(t)}},updateAutoHeight:function(e){const t=this,n=[],r=t.virtual&&t.params.virtual.enabled;let a,l=0;"number"===typeof e?t.setTransition(e):!0===e&&t.setTransition(t.params.speed);const i=e=>r?t.slides[t.getSlideIndexByData(e)]:t.slides[e];if("auto"!==t.params.slidesPerView&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(e=>{n.push(e)});else for(a=0;a<Math.ceil(t.params.slidesPerView);a+=1){const e=t.activeIndex+a;if(e>t.slides.length&&!r)break;n.push(i(e))}else n.push(i(t.activeIndex));for(a=0;a<n.length;a+=1)if("undefined"!==typeof n[a]){const e=n[a].offsetHeight;l=e>l?e:l}(l||0===l)&&(t.wrapperEl.style.height="".concat(l,"px"))},updateSlidesOffset:function(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-n-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:r,rtlTranslate:a,snapGrid:l}=t;if(0===r.length)return;"undefined"===typeof r[0].swiperSlideOffset&&t.updateSlidesOffset();let i=-e;a&&(i=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let o=n.spaceBetween;"string"===typeof o&&o.indexOf("%")>=0?o=parseFloat(o.replace("%",""))/100*t.size:"string"===typeof o&&(o=parseFloat(o));for(let s=0;s<r.length;s+=1){const e=r[s];let c=e.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(c-=r[0].swiperSlideOffset);const u=(i+(n.centeredSlides?t.minTranslate():0)-c)/(e.swiperSlideSize+o),d=(i-l[0]+(n.centeredSlides?t.minTranslate():0)-c)/(e.swiperSlideSize+o),p=-(i-c),f=p+t.slidesSizesGrid[s],m=p>=0&&p<=t.size-t.slidesSizesGrid[s],h=p>=0&&p<t.size-1||f>1&&f<=t.size||p<=0&&f>=t.size;h&&(t.visibleSlides.push(e),t.visibleSlidesIndexes.push(s)),mn(e,h,n.slideVisibleClass),mn(e,m,n.slideFullyVisibleClass),e.progress=a?-u:u,e.originalProgress=a?-d:d}},updateProgress:function(e){const t=this;if("undefined"===typeof e){const n=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*n||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:a,isBeginning:l,isEnd:i,progressLoop:o}=t;const s=l,c=i;if(0===r)a=0,l=!0,i=!0;else{a=(e-t.minTranslate())/r;const n=Math.abs(e-t.minTranslate())<1,o=Math.abs(e-t.maxTranslate())<1;l=n||a<=0,i=o||a>=1,n&&(a=0),o&&(a=1)}if(n.loop){const n=t.getSlideIndexByData(0),r=t.getSlideIndexByData(t.slides.length-1),a=t.slidesGrid[n],l=t.slidesGrid[r],i=t.slidesGrid[t.slidesGrid.length-1],s=Math.abs(e);o=s>=a?(s-a)/i:(s+i-l)/i,o>1&&(o-=1)}Object.assign(t,{progress:a,progressLoop:o,isBeginning:l,isEnd:i}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),l&&!s&&t.emit("reachBeginning toEdge"),i&&!c&&t.emit("reachEnd toEdge"),(s&&!l||c&&!i)&&t.emit("fromEdge"),t.emit("progress",a)},updateSlidesClasses:function(){const e=this,{slides:t,params:n,slidesEl:r,activeIndex:a}=e,l=e.virtual&&n.virtual.enabled,i=e.grid&&n.grid&&n.grid.rows>1,o=e=>Kt(r,".".concat(n.slideClass).concat(e,", swiper-slide").concat(e))[0];let s,c,u;if(l)if(n.loop){let t=a-e.virtual.slidesBefore;t<0&&(t=e.virtual.slides.length+t),t>=e.virtual.slides.length&&(t-=e.virtual.slides.length),s=o('[data-swiper-slide-index="'.concat(t,'"]'))}else s=o('[data-swiper-slide-index="'.concat(a,'"]'));else i?(s=t.find(e=>e.column===a),u=t.find(e=>e.column===a+1),c=t.find(e=>e.column===a-1)):s=t[a];s&&(i||(u=function(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}(s,".".concat(n.slideClass,", swiper-slide"))[0],n.loop&&!u&&(u=t[0]),c=function(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}(s,".".concat(n.slideClass,", swiper-slide"))[0],n.loop&&0===!c&&(c=t[t.length-1]))),t.forEach(e=>{hn(e,e===s,n.slideActiveClass),hn(e,e===u,n.slideNextClass),hn(e,e===c,n.slidePrevClass)}),e.emitSlidesClasses()},updateActiveIndex:function(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:r,params:a,activeIndex:l,realIndex:i,snapIndex:o}=t;let s,c=e;const u=e=>{let n=e-t.virtual.slidesBefore;return n<0&&(n=t.virtual.slides.length+n),n>=t.virtual.slides.length&&(n-=t.virtual.slides.length),n};if("undefined"===typeof c&&(c=function(e){const{slidesGrid:t,params:n}=e,r=e.rtlTranslate?e.translate:-e.translate;let a;for(let l=0;l<t.length;l+=1)"undefined"!==typeof t[l+1]?r>=t[l]&&r<t[l+1]-(t[l+1]-t[l])/2?a=l:r>=t[l]&&r<t[l+1]&&(a=l+1):r>=t[l]&&(a=l);return n.normalizeSlideIndex&&(a<0||"undefined"===typeof a)&&(a=0),a}(t)),r.indexOf(n)>=0)s=r.indexOf(n);else{const e=Math.min(a.slidesPerGroupSkip,c);s=e+Math.floor((c-e)/a.slidesPerGroup)}if(s>=r.length&&(s=r.length-1),c===l&&!t.params.loop)return void(s!==o&&(t.snapIndex=s,t.emit("snapIndexChange")));if(c===l&&t.params.loop&&t.virtual&&t.params.virtual.enabled)return void(t.realIndex=u(c));const d=t.grid&&a.grid&&a.grid.rows>1;let p;if(t.virtual&&a.virtual.enabled&&a.loop)p=u(c);else if(d){const e=t.slides.find(e=>e.column===c);let n=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(n)&&(n=Math.max(t.slides.indexOf(e),0)),p=Math.floor(n/a.grid.rows)}else if(t.slides[c]){const e=t.slides[c].getAttribute("data-swiper-slide-index");p=e?parseInt(e,10):c}else p=c;Object.assign(t,{previousSnapIndex:o,snapIndex:s,previousRealIndex:i,realIndex:p,previousIndex:l,activeIndex:c}),t.initialized&&yn(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(i!==p&&t.emit("realIndexChange"),t.emit("slideChange"))},updateClickedSlide:function(e,t){const n=this,r=n.params;let a=e.closest(".".concat(r.slideClass,", swiper-slide"));!a&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!a&&e.matches&&e.matches(".".concat(r.slideClass,", swiper-slide"))&&(a=e)});let l,i=!1;if(a)for(let o=0;o<n.slides.length;o+=1)if(n.slides[o]===a){i=!0,l=o;break}if(!a||!i)return n.clickedSlide=void 0,void(n.clickedIndex=void 0);n.clickedSlide=a,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(a.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=l,r.slideToClickedSlide&&void 0!==n.clickedIndex&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}};var wn={getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");const{params:t,rtlTranslate:n,translate:r,wrapperEl:a}=this;if(t.virtualTranslate)return n?-r:r;if(t.cssMode)return r;let l=Gt(a,e);return l+=this.cssOverflowAdjustment(),n&&(l=-l),l||0},setTranslate:function(e,t){const n=this,{rtlTranslate:r,params:a,wrapperEl:l,progress:i}=n;let o,s=0,c=0;n.isHorizontal()?s=r?-e:e:c=e,a.roundLengths&&(s=Math.floor(s),c=Math.floor(c)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?s:c,a.cssMode?l[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-s:-c:a.virtualTranslate||(n.isHorizontal()?s-=n.cssOverflowAdjustment():c-=n.cssOverflowAdjustment(),l.style.transform="translate3d(".concat(s,"px, ").concat(c,"px, ").concat(0,"px)"));const u=n.maxTranslate()-n.minTranslate();o=0===u?0:(e-n.minTranslate())/u,o!==i&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,n,r,a){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0),void 0===r&&(r=!0);const l=this,{params:i,wrapperEl:o}=l;if(l.animating&&i.preventInteractionOnTransition)return!1;const s=l.minTranslate(),c=l.maxTranslate();let u;if(u=r&&e>s?s:r&&e<c?c:e,l.updateProgress(u),i.cssMode){const e=l.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-u;else{if(!l.support.smoothScroll)return Xt({swiper:l,targetPosition:-u,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-u,behavior:"smooth"})}return!0}return 0===t?(l.setTransition(0),l.setTranslate(u),n&&(l.emit("beforeTransitionStart",t,a),l.emit("transitionEnd"))):(l.setTransition(t),l.setTranslate(u),n&&(l.emit("beforeTransitionStart",t,a),l.emit("transitionStart")),l.animating||(l.animating=!0,l.onTranslateToWrapperTransitionEnd||(l.onTranslateToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.onTranslateToWrapperTransitionEnd=null,delete l.onTranslateToWrapperTransitionEnd,l.animating=!1,n&&l.emit("transitionEnd"))}),l.wrapperEl.addEventListener("transitionend",l.onTranslateToWrapperTransitionEnd))),!0}};function Sn(e){let{swiper:t,runCallbacks:n,direction:r,step:a}=e;const{activeIndex:l,previousIndex:i}=t;let o=r;o||(o=l>i?"next":l<i?"prev":"reset"),t.emit("transition".concat(a)),n&&"reset"===o?t.emit("slideResetTransition".concat(a)):n&&l!==i&&(t.emit("slideChangeTransition".concat(a)),"next"===o?t.emit("slideNextTransition".concat(a)):t.emit("slidePrevTransition".concat(a)))}var xn={setTransition:function(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration="".concat(e,"ms"),n.wrapperEl.style.transitionDelay=0===e?"0ms":""),n.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);const n=this,{params:r}=n;r.cssMode||(r.autoHeight&&n.updateAutoHeight(),Sn({swiper:n,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);const n=this,{params:r}=n;n.animating=!1,r.cssMode||(n.setTransition(0),Sn({swiper:n,runCallbacks:e,direction:t,step:"End"}))}};var kn={slideTo:function(e,t,n,r,a){void 0===e&&(e=0),void 0===n&&(n=!0),"string"===typeof e&&(e=parseInt(e,10));const l=this;let i=e;i<0&&(i=0);const{params:o,snapGrid:s,slidesGrid:c,previousIndex:u,activeIndex:d,rtlTranslate:p,wrapperEl:f,enabled:m}=l;if(!m&&!r&&!a||l.destroyed||l.animating&&o.preventInteractionOnTransition)return!1;"undefined"===typeof t&&(t=l.params.speed);const h=Math.min(l.params.slidesPerGroupSkip,i);let g=h+Math.floor((i-h)/l.params.slidesPerGroup);g>=s.length&&(g=s.length-1);const v=-s[g];if(o.normalizeSlideIndex)for(let S=0;S<c.length;S+=1){const e=-Math.floor(100*v),t=Math.floor(100*c[S]),n=Math.floor(100*c[S+1]);"undefined"!==typeof c[S+1]?e>=t&&e<n-(n-t)/2?i=S:e>=t&&e<n&&(i=S+1):e>=t&&(i=S)}if(l.initialized&&i!==d){if(!l.allowSlideNext&&(p?v>l.translate&&v>l.minTranslate():v<l.translate&&v<l.minTranslate()))return!1;if(!l.allowSlidePrev&&v>l.translate&&v>l.maxTranslate()&&(d||0)!==i)return!1}let y;i!==(u||0)&&n&&l.emit("beforeSlideChangeStart"),l.updateProgress(v),y=i>d?"next":i<d?"prev":"reset";const b=l.virtual&&l.params.virtual.enabled;if(!(b&&a)&&(p&&-v===l.translate||!p&&v===l.translate))return l.updateActiveIndex(i),o.autoHeight&&l.updateAutoHeight(),l.updateSlidesClasses(),"slide"!==o.effect&&l.setTranslate(v),"reset"!==y&&(l.transitionStart(n,y),l.transitionEnd(n,y)),!1;if(o.cssMode){const e=l.isHorizontal(),n=p?v:-v;if(0===t)b&&(l.wrapperEl.style.scrollSnapType="none",l._immediateVirtual=!0),b&&!l._cssModeVirtualInitialSet&&l.params.initialSlide>0?(l._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{f[e?"scrollLeft":"scrollTop"]=n})):f[e?"scrollLeft":"scrollTop"]=n,b&&requestAnimationFrame(()=>{l.wrapperEl.style.scrollSnapType="",l._immediateVirtual=!1});else{if(!l.support.smoothScroll)return Xt({swiper:l,targetPosition:n,side:e?"left":"top"}),!0;f.scrollTo({[e?"left":"top"]:n,behavior:"smooth"})}return!0}const w=pn().isSafari;return b&&!a&&w&&l.isElement&&l.virtual.update(!1,!1,i),l.setTransition(t),l.setTranslate(v),l.updateActiveIndex(i),l.updateSlidesClasses(),l.emit("beforeTransitionStart",t,r),l.transitionStart(n,y),0===t?l.transitionEnd(n,y):l.animating||(l.animating=!0,l.onSlideToWrapperTransitionEnd||(l.onSlideToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.onSlideToWrapperTransitionEnd=null,delete l.onSlideToWrapperTransitionEnd,l.transitionEnd(n,y))}),l.wrapperEl.addEventListener("transitionend",l.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,n,r){if(void 0===e&&(e=0),void 0===n&&(n=!0),"string"===typeof e){e=parseInt(e,10)}const a=this;if(a.destroyed)return;"undefined"===typeof t&&(t=a.params.speed);const l=a.grid&&a.params.grid&&a.params.grid.rows>1;let i=e;if(a.params.loop)if(a.virtual&&a.params.virtual.enabled)i+=a.virtual.slidesBefore;else{let e;if(l){const t=i*a.params.grid.rows;e=a.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=a.getSlideIndexByData(i);const t=l?Math.ceil(a.slides.length/a.params.grid.rows):a.slides.length,{centeredSlides:n}=a.params;let o=a.params.slidesPerView;"auto"===o?o=a.slidesPerViewDynamic():(o=Math.ceil(parseFloat(a.params.slidesPerView,10)),n&&o%2===0&&(o+=1));let s=t-e<o;if(n&&(s=s||e<Math.ceil(o/2)),r&&n&&"auto"!==a.params.slidesPerView&&!l&&(s=!1),s){const r=n?e<a.activeIndex?"prev":"next":e-a.activeIndex-1<a.params.slidesPerView?"next":"prev";a.loopFix({direction:r,slideTo:!0,activeSlideIndex:"next"===r?e+1:e-t+1,slideRealIndex:"next"===r?a.realIndex:void 0})}if(l){const e=i*a.params.grid.rows;i=a.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else i=a.getSlideIndexByData(i)}return requestAnimationFrame(()=>{a.slideTo(i,t,n,r)}),a},slideNext:function(e,t,n){void 0===t&&(t=!0);const r=this,{enabled:a,params:l,animating:i}=r;if(!a||r.destroyed)return r;"undefined"===typeof e&&(e=r.params.speed);let o=l.slidesPerGroup;"auto"===l.slidesPerView&&1===l.slidesPerGroup&&l.slidesPerGroupAuto&&(o=Math.max(r.slidesPerViewDynamic("current",!0),1));const s=r.activeIndex<l.slidesPerGroupSkip?1:o,c=r.virtual&&l.virtual.enabled;if(l.loop){if(i&&!c&&l.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&l.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+s,e,t,n)}),!0}return l.rewind&&r.isEnd?r.slideTo(0,e,t,n):r.slideTo(r.activeIndex+s,e,t,n)},slidePrev:function(e,t,n){void 0===t&&(t=!0);const r=this,{params:a,snapGrid:l,slidesGrid:i,rtlTranslate:o,enabled:s,animating:c}=r;if(!s||r.destroyed)return r;"undefined"===typeof e&&(e=r.params.speed);const u=r.virtual&&a.virtual.enabled;if(a.loop){if(c&&!u&&a.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}function d(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const p=d(o?r.translate:-r.translate),f=l.map(e=>d(e)),m=a.freeMode&&a.freeMode.enabled;let h=l[f.indexOf(p)-1];if("undefined"===typeof h&&(a.cssMode||m)){let e;l.forEach((t,n)=>{p>=t&&(e=n)}),"undefined"!==typeof e&&(h=m?l[e]:l[e>0?e-1:e])}let g=0;if("undefined"!==typeof h&&(g=i.indexOf(h),g<0&&(g=r.activeIndex-1),"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(g=g-r.slidesPerViewDynamic("previous",!0)+1,g=Math.max(g,0))),a.rewind&&r.isBeginning){const a=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(a,e,t,n)}return a.loop&&0===r.activeIndex&&a.cssMode?(requestAnimationFrame(()=>{r.slideTo(g,e,t,n)}),!0):r.slideTo(g,e,t,n)},slideReset:function(e,t,n){void 0===t&&(t=!0);const r=this;if(!r.destroyed)return"undefined"===typeof e&&(e=r.params.speed),r.slideTo(r.activeIndex,e,t,n)},slideToClosest:function(e,t,n,r){void 0===t&&(t=!0),void 0===r&&(r=.5);const a=this;if(a.destroyed)return;"undefined"===typeof e&&(e=a.params.speed);let l=a.activeIndex;const i=Math.min(a.params.slidesPerGroupSkip,l),o=i+Math.floor((l-i)/a.params.slidesPerGroup),s=a.rtlTranslate?a.translate:-a.translate;if(s>=a.snapGrid[o]){const e=a.snapGrid[o];s-e>(a.snapGrid[o+1]-e)*r&&(l+=a.params.slidesPerGroup)}else{const e=a.snapGrid[o-1];s-e<=(a.snapGrid[o]-e)*r&&(l-=a.params.slidesPerGroup)}return l=Math.max(l,0),l=Math.min(l,a.slidesGrid.length-1),a.slideTo(l,e,t,n)},slideToClickedSlide:function(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,r="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let a,l=e.clickedIndex;const i=e.isElement?"swiper-slide":".".concat(t.slideClass);if(t.loop){if(e.animating)return;a=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?l<e.loopedSlides-r/2||l>e.slides.length-e.loopedSlides+r/2?(e.loopFix(),l=e.getSlideIndex(Kt(n,"".concat(i,'[data-swiper-slide-index="').concat(a,'"]'))[0]),Ut(()=>{e.slideTo(l)})):e.slideTo(l):l>e.slides.length-r?(e.loopFix(),l=e.getSlideIndex(Kt(n,"".concat(i,'[data-swiper-slide-index="').concat(a,'"]'))[0]),Ut(()=>{e.slideTo(l)})):e.slideTo(l)}else e.slideTo(l)}};var En={loopCreate:function(e,t){const n=this,{params:r,slidesEl:a}=n;if(!r.loop||n.virtual&&n.params.virtual.enabled)return;const l=()=>{Kt(a,".".concat(r.slideClass,", swiper-slide")).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)})},i=n.grid&&r.grid&&r.grid.rows>1,o=r.slidesPerGroup*(i?r.grid.rows:1),s=n.slides.length%o!==0,c=i&&n.slides.length%r.grid.rows!==0,u=e=>{for(let t=0;t<e;t+=1){const e=n.isElement?Zt("swiper-slide",[r.slideBlankClass]):Zt("div",[r.slideClass,r.slideBlankClass]);n.slidesEl.append(e)}};if(s){if(r.loopAddBlankSlides){u(o-n.slides.length%o),n.recalcSlides(),n.updateSlides()}else Jt("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");l()}else if(c){if(r.loopAddBlankSlides){u(r.grid.rows-n.slides.length%r.grid.rows),n.recalcSlides(),n.updateSlides()}else Jt("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");l()}else l();n.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:n=!0,direction:r,setTranslate:a,activeSlideIndex:l,initial:i,byController:o,byMousewheel:s}=void 0===e?{}:e;const c=this;if(!c.params.loop)return;c.emit("beforeLoopFix");const{slides:d,allowSlidePrev:p,allowSlideNext:f,slidesEl:m,params:h}=c,{centeredSlides:g,initialSlide:v}=h;if(c.allowSlidePrev=!0,c.allowSlideNext=!0,c.virtual&&h.virtual.enabled)return n&&(h.centeredSlides||0!==c.snapIndex?h.centeredSlides&&c.snapIndex<h.slidesPerView?c.slideTo(c.virtual.slides.length+c.snapIndex,0,!1,!0):c.snapIndex===c.snapGrid.length-1&&c.slideTo(c.virtual.slidesBefore,0,!1,!0):c.slideTo(c.virtual.slides.length,0,!1,!0)),c.allowSlidePrev=p,c.allowSlideNext=f,void c.emit("loopFix");let y=h.slidesPerView;"auto"===y?y=c.slidesPerViewDynamic():(y=Math.ceil(parseFloat(h.slidesPerView,10)),g&&y%2===0&&(y+=1));const b=h.slidesPerGroupAuto?y:h.slidesPerGroup;let w=b;w%b!==0&&(w+=b-w%b),w+=h.loopAdditionalSlides,c.loopedSlides=w;const S=c.grid&&h.grid&&h.grid.rows>1;d.length<y+w||"cards"===c.params.effect&&d.length<y+2*w?Jt("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):S&&"row"===h.grid.fill&&Jt("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const x=[],k=[],E=S?Math.ceil(d.length/h.grid.rows):d.length,C=i&&E-v<y&&!g;let T=C?v:c.activeIndex;"undefined"===typeof l?l=c.getSlideIndex(d.find(e=>e.classList.contains(h.slideActiveClass))):T=l;const P="next"===r||!r,L="prev"===r||!r;let N=0,j=0;const _=(S?d[l].column:l)+(g&&"undefined"===typeof a?-y/2+.5:0);if(_<w){N=Math.max(w-_,b);for(let e=0;e<w-_;e+=1){const t=e-Math.floor(e/E)*E;if(S){const e=E-t-1;for(let t=d.length-1;t>=0;t-=1)d[t].column===e&&x.push(t)}else x.push(E-t-1)}}else if(_+y>E-w){j=Math.max(_-(E-2*w),b),C&&(j=Math.max(j,y-E+v+1));for(let e=0;e<j;e+=1){const t=e-Math.floor(e/E)*E;S?d.forEach((e,n)=>{e.column===t&&k.push(n)}):k.push(t)}}if(c.__preventObserver__=!0,requestAnimationFrame(()=>{c.__preventObserver__=!1}),"cards"===c.params.effect&&d.length<y+2*w&&(k.includes(l)&&k.splice(k.indexOf(l),1),x.includes(l)&&x.splice(x.indexOf(l),1)),L&&x.forEach(e=>{d[e].swiperLoopMoveDOM=!0,m.prepend(d[e]),d[e].swiperLoopMoveDOM=!1}),P&&k.forEach(e=>{d[e].swiperLoopMoveDOM=!0,m.append(d[e]),d[e].swiperLoopMoveDOM=!1}),c.recalcSlides(),"auto"===h.slidesPerView?c.updateSlides():S&&(x.length>0&&L||k.length>0&&P)&&c.slides.forEach((e,t)=>{c.grid.updateSlide(t,e,c.slides)}),h.watchSlidesProgress&&c.updateSlidesOffset(),n)if(x.length>0&&L){if("undefined"===typeof t){const e=c.slidesGrid[T],t=c.slidesGrid[T+N]-e;s?c.setTranslate(c.translate-t):(c.slideTo(T+Math.ceil(N),0,!1,!0),a&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else if(a){const e=S?x.length/h.grid.rows:x.length;c.slideTo(c.activeIndex+e,0,!1,!0),c.touchEventsData.currentTranslate=c.translate}}else if(k.length>0&&P)if("undefined"===typeof t){const e=c.slidesGrid[T],t=c.slidesGrid[T-j]-e;s?c.setTranslate(c.translate-t):(c.slideTo(T-j,0,!1,!0),a&&(c.touchEventsData.startTranslate=c.touchEventsData.startTranslate-t,c.touchEventsData.currentTranslate=c.touchEventsData.currentTranslate-t))}else{const e=S?k.length/h.grid.rows:k.length;c.slideTo(c.activeIndex-e,0,!1,!0)}if(c.allowSlidePrev=p,c.allowSlideNext=f,c.controller&&c.controller.control&&!o){const e={slideRealIndex:t,direction:r,setTranslate:a,activeSlideIndex:l,byController:!0};Array.isArray(c.controller.control)?c.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix(u(u({},e),{},{slideTo:t.params.slidesPerView===h.slidesPerView&&n}))}):c.controller.control instanceof c.constructor&&c.controller.control.params.loop&&c.controller.control.loopFix(u(u({},e),{},{slideTo:c.controller.control.params.slidesPerView===h.slidesPerView&&n}))}c.emit("loopFix")},loopDestroy:function(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const r=[];e.slides.forEach(e=>{const t="undefined"===typeof e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;r[t]=e}),e.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),r.forEach(e=>{n.append(e)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}};var Cn={setGrabCursor:function(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}};function Tn(e,t,n){const r=Ht(),{params:a}=e,l=a.edgeSwipeDetection,i=a.edgeSwipeThreshold;return!l||!(n<=i||n>=r.innerWidth-i)||"prevent"===l&&(t.preventDefault(),!0)}function Pn(e){const t=this,n=Ft();let r=e;r.originalEvent&&(r=r.originalEvent);const a=t.touchEventsData;if("pointerdown"===r.type){if(null!==a.pointerId&&a.pointerId!==r.pointerId)return;a.pointerId=r.pointerId}else"touchstart"===r.type&&1===r.targetTouches.length&&(a.touchId=r.targetTouches[0].identifier);if("touchstart"===r.type)return void Tn(t,r,r.targetTouches[0].pageX);const{params:l,touches:i,enabled:o}=t;if(!o)return;if(!l.simulateTouch&&"mouse"===r.pointerType)return;if(t.animating&&l.preventInteractionOnTransition)return;!t.animating&&l.cssMode&&l.loop&&t.loopFix();let s=r.target;if("wrapper"===l.touchEventsTarget&&!function(e,t){const n=Ht();let r=t.contains(e);!r&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(r=[...t.assignedElements()].includes(e),r||(r=function(e,t){const n=[t];for(;n.length>0;){const t=n.shift();if(e===t)return!0;n.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)));return r}(s,t.wrapperEl))return;if("which"in r&&3===r.which)return;if("button"in r&&r.button>0)return;if(a.isTouched&&a.isMoved)return;const c=!!l.noSwipingClass&&""!==l.noSwipingClass,u=r.composedPath?r.composedPath():r.path;c&&r.target&&r.target.shadowRoot&&u&&(s=u[0]);const d=l.noSwipingSelector?l.noSwipingSelector:".".concat(l.noSwipingClass),p=!(!r.target||!r.target.shadowRoot);if(l.noSwiping&&(p?function(e,t){return void 0===t&&(t=this),function t(n){if(!n||n===Ft()||n===Ht())return null;n.assignedSlot&&(n=n.assignedSlot);const r=n.closest(e);return r||n.getRootNode?r||t(n.getRootNode().host):null}(t)}(d,s):s.closest(d)))return void(t.allowClick=!0);if(l.swipeHandler&&!s.closest(l.swipeHandler))return;i.currentX=r.pageX,i.currentY=r.pageY;const f=i.currentX,m=i.currentY;if(!Tn(t,r,f))return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),i.startX=f,i.startY=m,a.touchStartTime=Vt(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,l.threshold>0&&(a.allowThresholdMove=!1);let h=!0;s.matches(a.focusableElements)&&(h=!1,"SELECT"===s.nodeName&&(a.isTouched=!1)),n.activeElement&&n.activeElement.matches(a.focusableElements)&&n.activeElement!==s&&("mouse"===r.pointerType||"mouse"!==r.pointerType&&!s.matches(a.focusableElements))&&n.activeElement.blur();const g=h&&t.allowTouchMove&&l.touchStartPreventDefault;!l.touchStartForcePreventDefault&&!g||s.isContentEditable||r.preventDefault(),l.freeMode&&l.freeMode.enabled&&t.freeMode&&t.animating&&!l.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",r)}function Ln(e){const t=Ft(),n=this,r=n.touchEventsData,{params:a,touches:l,rtlTranslate:i,enabled:o}=n;if(!o)return;if(!a.simulateTouch&&"mouse"===e.pointerType)return;let s,c=e;if(c.originalEvent&&(c=c.originalEvent),"pointermove"===c.type){if(null!==r.touchId)return;if(c.pointerId!==r.pointerId)return}if("touchmove"===c.type){if(s=[...c.changedTouches].find(e=>e.identifier===r.touchId),!s||s.identifier!==r.touchId)return}else s=c;if(!r.isTouched)return void(r.startMoving&&r.isScrolling&&n.emit("touchMoveOpposite",c));const u=s.pageX,d=s.pageY;if(c.preventedByNestedSwiper)return l.startX=u,void(l.startY=d);if(!n.allowTouchMove)return c.target.matches(r.focusableElements)||(n.allowClick=!1),void(r.isTouched&&(Object.assign(l,{startX:u,startY:d,currentX:u,currentY:d}),r.touchStartTime=Vt()));if(a.touchReleaseOnEdges&&!a.loop)if(n.isVertical()){if(d<l.startY&&n.translate<=n.maxTranslate()||d>l.startY&&n.translate>=n.minTranslate())return r.isTouched=!1,void(r.isMoved=!1)}else{if(i&&(u>l.startX&&-n.translate<=n.maxTranslate()||u<l.startX&&-n.translate>=n.minTranslate()))return;if(!i&&(u<l.startX&&n.translate<=n.maxTranslate()||u>l.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==c.target&&"mouse"!==c.pointerType&&t.activeElement.blur(),t.activeElement&&c.target===t.activeElement&&c.target.matches(r.focusableElements))return r.isMoved=!0,void(n.allowClick=!1);r.allowTouchCallbacks&&n.emit("touchMove",c),l.previousX=l.currentX,l.previousY=l.currentY,l.currentX=u,l.currentY=d;const p=l.currentX-l.startX,f=l.currentY-l.startY;if(n.params.threshold&&Math.sqrt(p**2+f**2)<n.params.threshold)return;if("undefined"===typeof r.isScrolling){let e;n.isHorizontal()&&l.currentY===l.startY||n.isVertical()&&l.currentX===l.startX?r.isScrolling=!1:p*p+f*f>=25&&(e=180*Math.atan2(Math.abs(f),Math.abs(p))/Math.PI,r.isScrolling=n.isHorizontal()?e>a.touchAngle:90-e>a.touchAngle)}if(r.isScrolling&&n.emit("touchMoveOpposite",c),"undefined"===typeof r.startMoving&&(l.currentX===l.startX&&l.currentY===l.startY||(r.startMoving=!0)),r.isScrolling||"touchmove"===c.type&&r.preventTouchMoveFromPointerMove)return void(r.isTouched=!1);if(!r.startMoving)return;n.allowClick=!1,!a.cssMode&&c.cancelable&&c.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&c.stopPropagation();let m=n.isHorizontal()?p:f,h=n.isHorizontal()?l.currentX-l.previousX:l.currentY-l.previousY;a.oneWayMovement&&(m=Math.abs(m)*(i?1:-1),h=Math.abs(h)*(i?1:-1)),l.diff=m,m*=a.touchRatio,i&&(m=-m,h=-h);const g=n.touchesDirection;n.swipeDirection=m>0?"prev":"next",n.touchesDirection=h>0?"prev":"next";const v=n.params.loop&&!a.cssMode,y="next"===n.touchesDirection&&n.allowSlideNext||"prev"===n.touchesDirection&&n.allowSlidePrev;if(!r.isMoved){if(v&&y&&n.loopFix({direction:n.swipeDirection}),r.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,!a.grabCursor||!0!==n.allowSlideNext&&!0!==n.allowSlidePrev||n.setGrabCursor(!0),n.emit("sliderFirstMove",c)}if((new Date).getTime(),!1!==a._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&g!==n.touchesDirection&&v&&y&&Math.abs(m)>=1)return Object.assign(l,{startX:u,startY:d,currentX:u,currentY:d,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,void(r.startTranslate=r.currentTranslate);n.emit("sliderMove",c),r.isMoved=!0,r.currentTranslate=m+r.startTranslate;let b=!0,w=a.resistanceRatio;if(a.touchReleaseOnEdges&&(w=0),m>0?(v&&y&&r.allowThresholdMove&&r.currentTranslate>(a.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-("auto"!==a.slidesPerView&&n.slides.length-a.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>n.minTranslate()&&(b=!1,a.resistance&&(r.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+r.startTranslate+m)**w))):m<0&&(v&&y&&r.allowThresholdMove&&r.currentTranslate<(a.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+("auto"!==a.slidesPerView&&n.slides.length-a.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-("auto"===a.slidesPerView?n.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),r.currentTranslate<n.maxTranslate()&&(b=!1,a.resistance&&(r.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-r.startTranslate-m)**w))),b&&(c.preventedByNestedSwiper=!0),!n.allowSlideNext&&"next"===n.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&"prev"===n.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),n.allowSlidePrev||n.allowSlideNext||(r.currentTranslate=r.startTranslate),a.threshold>0){if(!(Math.abs(m)>a.threshold||r.allowThresholdMove))return void(r.currentTranslate=r.startTranslate);if(!r.allowThresholdMove)return r.allowThresholdMove=!0,l.startX=l.currentX,l.startY=l.currentY,r.currentTranslate=r.startTranslate,void(l.diff=n.isHorizontal()?l.currentX-l.startX:l.currentY-l.startY)}a.followFinger&&!a.cssMode&&((a.freeMode&&a.freeMode.enabled&&n.freeMode||a.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(r.currentTranslate),n.setTranslate(r.currentTranslate))}function Nn(e){const t=this,n=t.touchEventsData;let r,a=e;a.originalEvent&&(a=a.originalEvent);if("touchend"===a.type||"touchcancel"===a.type){if(r=[...a.changedTouches].find(e=>e.identifier===n.touchId),!r||r.identifier!==n.touchId)return}else{if(null!==n.touchId)return;if(a.pointerId!==n.pointerId)return;r=a}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(a.type)){if(!(["pointercancel","contextmenu"].includes(a.type)&&(t.browser.isSafari||t.browser.isWebView)))return}n.pointerId=null,n.touchId=null;const{params:l,touches:i,rtlTranslate:o,slidesGrid:s,enabled:c}=t;if(!c)return;if(!l.simulateTouch&&"mouse"===a.pointerType)return;if(n.allowTouchCallbacks&&t.emit("touchEnd",a),n.allowTouchCallbacks=!1,!n.isTouched)return n.isMoved&&l.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,void(n.startMoving=!1);l.grabCursor&&n.isMoved&&n.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);const u=Vt(),d=u-n.touchStartTime;if(t.allowClick){const e=a.path||a.composedPath&&a.composedPath();t.updateClickedSlide(e&&e[0]||a.target,e),t.emit("tap click",a),d<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",a)}if(n.lastClickTime=Vt(),Ut(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||0===i.diff&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset)return n.isTouched=!1,n.isMoved=!1,void(n.startMoving=!1);let p;if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,p=l.followFinger?o?t.translate:-t.translate:-n.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled)return void t.freeMode.onTouchEnd({currentPos:p});const f=p>=-t.maxTranslate()&&!t.params.loop;let m=0,h=t.slidesSizesGrid[0];for(let w=0;w<s.length;w+=w<l.slidesPerGroupSkip?1:l.slidesPerGroup){const e=w<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;"undefined"!==typeof s[w+e]?(f||p>=s[w]&&p<s[w+e])&&(m=w,h=s[w+e]-s[w]):(f||p>=s[w])&&(m=w,h=s[s.length-1]-s[s.length-2])}let g=null,v=null;l.rewind&&(t.isBeginning?v=l.virtual&&l.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(g=0));const y=(p-s[m])/h,b=m<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(d>l.longSwipesMs){if(!l.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(y>=l.longSwipesRatio?t.slideTo(l.rewind&&t.isEnd?g:m+b):t.slideTo(m)),"prev"===t.swipeDirection&&(y>1-l.longSwipesRatio?t.slideTo(m+b):null!==v&&y<0&&Math.abs(y)>l.longSwipesRatio?t.slideTo(v):t.slideTo(m))}else{if(!l.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(a.target===t.navigation.nextEl||a.target===t.navigation.prevEl)?a.target===t.navigation.nextEl?t.slideTo(m+b):t.slideTo(m):("next"===t.swipeDirection&&t.slideTo(null!==g?g:m+b),"prev"===t.swipeDirection&&t.slideTo(null!==v?v:m))}}function jn(){const e=this,{params:t,el:n}=e;if(n&&0===n.offsetWidth)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:a,snapGrid:l}=e,i=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const o=i&&t.loop;!("auto"===t.slidesPerView||t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!i?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=a,e.allowSlideNext=r,e.params.watchOverflow&&l!==e.snapGrid&&e.checkOverflow()}function _n(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function On(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:r}=e;if(!r)return;let a;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();const l=e.maxTranslate()-e.minTranslate();a=0===l?0:(e.translate-e.minTranslate())/l,a!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function Mn(e){const t=this;gn(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}function zn(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const An=(e,t)=>{const n=Ft(),{params:r,el:a,wrapperEl:l,device:i}=e,o=!!r.nested,s="on"===t?"addEventListener":"removeEventListener",c=t;a&&"string"!==typeof a&&(n[s]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),a[s]("touchstart",e.onTouchStart,{passive:!1}),a[s]("pointerdown",e.onTouchStart,{passive:!1}),n[s]("touchmove",e.onTouchMove,{passive:!1,capture:o}),n[s]("pointermove",e.onTouchMove,{passive:!1,capture:o}),n[s]("touchend",e.onTouchEnd,{passive:!0}),n[s]("pointerup",e.onTouchEnd,{passive:!0}),n[s]("pointercancel",e.onTouchEnd,{passive:!0}),n[s]("touchcancel",e.onTouchEnd,{passive:!0}),n[s]("pointerout",e.onTouchEnd,{passive:!0}),n[s]("pointerleave",e.onTouchEnd,{passive:!0}),n[s]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&a[s]("click",e.onClick,!0),r.cssMode&&l[s]("scroll",e.onScroll),r.updateOnWindowResize?e[c](i.ios||i.android?"resize orientationchange observerUpdate":"resize observerUpdate",jn,!0):e[c]("observerUpdate",jn,!0),a[s]("load",e.onLoad,{capture:!0}))};var Dn={attachEvents:function(){const e=this,{params:t}=e;e.onTouchStart=Pn.bind(e),e.onTouchMove=Ln.bind(e),e.onTouchEnd=Nn.bind(e),e.onDocumentTouchStart=zn.bind(e),t.cssMode&&(e.onScroll=On.bind(e)),e.onClick=_n.bind(e),e.onLoad=Mn.bind(e),An(e,"on")},detachEvents:function(){An(this,"off")}};const Rn=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var In={setBreakpoint:function(){const e=this,{realIndex:t,initialized:n,params:r,el:a}=e,l=r.breakpoints;if(!l||l&&0===Object.keys(l).length)return;const i=Ft(),o="window"!==r.breakpointsBase&&r.breakpointsBase?"container":r.breakpointsBase,s=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:i.querySelector(r.breakpointsBase),c=e.getBreakpoint(l,o,s);if(!c||e.currentBreakpoint===c)return;const u=(c in l?l[c]:void 0)||e.originalParams,d=Rn(e,r),p=Rn(e,u),f=e.params.grabCursor,m=u.grabCursor,h=r.enabled;d&&!p?(a.classList.remove("".concat(r.containerModifierClass,"grid"),"".concat(r.containerModifierClass,"grid-column")),e.emitContainerClasses()):!d&&p&&(a.classList.add("".concat(r.containerModifierClass,"grid")),(u.grid.fill&&"column"===u.grid.fill||!u.grid.fill&&"column"===r.grid.fill)&&a.classList.add("".concat(r.containerModifierClass,"grid-column")),e.emitContainerClasses()),f&&!m?e.unsetGrabCursor():!f&&m&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if("undefined"===typeof u[t])return;const n=r[t]&&r[t].enabled,a=u[t]&&u[t].enabled;n&&!a&&e[t].disable(),!n&&a&&e[t].enable()});const g=u.direction&&u.direction!==r.direction,v=r.loop&&(u.slidesPerView!==r.slidesPerView||g),y=r.loop;g&&n&&e.changeDirection(),$t(e.params,u);const b=e.params.enabled,w=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),h&&!b?e.disable():!h&&b&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",u),n&&(v?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!y&&w?(e.loopCreate(t),e.updateSlides()):y&&!w&&e.loopDestroy()),e.emit("breakpoint",u)},getBreakpoint:function(e,t,n){if(void 0===t&&(t="window"),!e||"container"===t&&!n)return;let r=!1;const a=Ht(),l="window"===t?a.innerHeight:n.clientHeight,i=Object.keys(e).map(e=>{if("string"===typeof e&&0===e.indexOf("@")){const t=parseFloat(e.substr(1));return{value:l*t,point:e}}return{value:e,point:e}});i.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let o=0;o<i.length;o+=1){const{point:e,value:l}=i[o];"window"===t?a.matchMedia("(min-width: ".concat(l,"px)")).matches&&(r=e):l<=n.clientWidth&&(r=e)}return r||"max"}};var Fn={addClasses:function(){const e=this,{classNames:t,params:n,rtl:r,el:a,device:l}=e,i=function(e,t){const n=[];return e.forEach(e=>{"object"===typeof e?Object.keys(e).forEach(r=>{e[r]&&n.push(t+r)}):"string"===typeof e&&n.push(t+e)}),n}(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:r},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&"column"===n.grid.fill},{android:l.android},{ios:l.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...i),a.classList.add(...t),e.emitContainerClasses()},removeClasses:function(){const{el:e,classNames:t}=this;e&&"string"!==typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}};var Bn={checkOverflow:function(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:r}=n;if(r){const t=e.slides.length-1,n=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*r;e.isLocked=e.size>n}else e.isLocked=1===e.snapGrid.length;!0===n.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===n.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},Hn={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Un(e,t){return function(n){void 0===n&&(n={});const r=Object.keys(n)[0],a=n[r];"object"===typeof a&&null!==a?(!0===e[r]&&(e[r]={enabled:!0}),"navigation"===r&&e[r]&&e[r].enabled&&!e[r].prevEl&&!e[r].nextEl&&(e[r].auto=!0),["pagination","scrollbar"].indexOf(r)>=0&&e[r]&&e[r].enabled&&!e[r].el&&(e[r].auto=!0),r in e&&"enabled"in a?("object"!==typeof e[r]||"enabled"in e[r]||(e[r].enabled=!0),e[r]||(e[r]={enabled:!1}),$t(t,n)):$t(t,n)):$t(t,n)}}const Vn={eventsEmitter:fn,update:bn,translate:wn,transition:xn,slide:kn,loop:En,grabCursor:Cn,events:Dn,breakpoints:In,checkOverflow:Bn,classes:Fn},Gn={};class Wn{constructor(){let e,t;for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?t=r[0]:[e,t]=r,t||(t={}),t=$t({},t),e&&!t.el&&(t.el=e);const l=Ft();if(t.el&&"string"===typeof t.el&&l.querySelectorAll(t.el).length>1){const e=[];return l.querySelectorAll(t.el).forEach(n=>{const r=$t({},t,{el:n});e.push(new Wn(r))}),e}const i=this;i.__swiper__=!0,i.support=un(),i.device=dn({userAgent:t.userAgent}),i.browser=pn(),i.eventsListeners={},i.eventsAnyListeners=[],i.modules=[...i.__modules__],t.modules&&Array.isArray(t.modules)&&i.modules.push(...t.modules);const o={};i.modules.forEach(e=>{e({params:t,swiper:i,extendParams:Un(t,o),on:i.on.bind(i),once:i.once.bind(i),off:i.off.bind(i),emit:i.emit.bind(i)})});const s=$t({},Hn,o);return i.params=$t({},s,Gn,t),i.originalParams=$t({},i.params),i.passedParams=$t({},t),i.params&&i.params.on&&Object.keys(i.params.on).forEach(e=>{i.on(e,i.params.on[e])}),i.params&&i.params.onAny&&i.onAny(i.params.onAny),Object.assign(i,{enabled:i.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===i.params.direction,isVertical:()=>"vertical"===i.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:i.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:i.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),i.emit("_swiper"),i.params.init&&i.init(),i}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:n}=this,r=tn(Kt(t,".".concat(n.slideClass,", swiper-slide"))[0]);return tn(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}recalcSlides(){const{slidesEl:e,params:t}=this;this.slides=Kt(e,".".concat(t.slideClass,", swiper-slide"))}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const n=this;e=Math.min(Math.max(e,0),1);const r=n.minTranslate(),a=(n.maxTranslate()-r)*e+r;n.translateTo(a,"undefined"===typeof t?0:t),n.updateActiveIndex(),n.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(n=>{const r=e.getSlideClasses(n);t.push({slideEl:n,classNames:r}),e.emit("_slideClass",n,r)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);const{params:n,slides:r,slidesGrid:a,slidesSizesGrid:l,size:i,activeIndex:o}=this;let s=1;if("number"===typeof n.slidesPerView)return n.slidesPerView;if(n.centeredSlides){let e,t=r[o]?Math.ceil(r[o].swiperSlideSize):0;for(let n=o+1;n<r.length;n+=1)r[n]&&!e&&(t+=Math.ceil(r[n].swiperSlideSize),s+=1,t>i&&(e=!0));for(let n=o-1;n>=0;n-=1)r[n]&&!e&&(t+=r[n].swiperSlideSize,s+=1,t>i&&(e=!0))}else if("current"===e)for(let c=o+1;c<r.length;c+=1){(t?a[c]+l[c]-a[o]<i:a[c]-a[o]<i)&&(s+=1)}else for(let c=o-1;c>=0;c-=1){a[o]-a[c]<i&&(s+=1)}return s}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:n}=e;function r(){const t=e.rtlTranslate?-1*e.translate:e.translate,n=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(n),e.updateActiveIndex(),e.updateSlidesClasses()}let a;if(n.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(t=>{t.complete&&gn(e,t)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),n.freeMode&&n.freeMode.enabled&&!n.cssMode)r(),n.autoHeight&&e.updateAutoHeight();else{if(("auto"===n.slidesPerView||n.slidesPerView>1)&&e.isEnd&&!n.centeredSlides){const t=e.virtual&&n.virtual.enabled?e.virtual.slides:e.slides;a=e.slideTo(t.length-1,0,!1,!0)}else a=e.slideTo(e.activeIndex,0,!1,!0);a||r()}n.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);const n=this,r=n.params.direction;return e||(e="horizontal"===r?"vertical":"horizontal"),e===r||"horizontal"!==e&&"vertical"!==e||(n.el.classList.remove("".concat(n.params.containerModifierClass).concat(r)),n.el.classList.add("".concat(n.params.containerModifierClass).concat(e)),n.emitContainerClasses(),n.params.direction=e,n.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),n.emit("changeDirection"),t&&n.update()),n}changeLanguageDirection(e){const t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="rtl"):(t.el.classList.remove("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let n=e||t.params.el;if("string"===typeof n&&(n=document.querySelector(n)),!n)return!1;n.swiper=t,n.parentNode&&n.parentNode.host&&n.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const r=()=>".".concat((t.params.wrapperClass||"").trim().split(" ").join("."));let a=(()=>{if(n&&n.shadowRoot&&n.shadowRoot.querySelector){return n.shadowRoot.querySelector(r())}return Kt(n,r())[0]})();return!a&&t.params.createElements&&(a=Zt("div",t.params.wrapperClass),n.append(a),Kt(n,".".concat(t.params.slideClass)).forEach(e=>{a.append(e)})),Object.assign(t,{el:n,wrapperEl:a,slidesEl:t.isElement&&!n.parentNode.host.slideSlots?n.parentNode.host:a,hostEl:t.isElement?n.parentNode.host:n,mounted:!0,rtl:"rtl"===n.dir.toLowerCase()||"rtl"===en(n,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===n.dir.toLowerCase()||"rtl"===en(n,"direction")),wrongRTL:"-webkit-box"===en(a,"display")}),!0}init(e){const t=this;if(t.initialized)return t;if(!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach(e=>{e.complete?gn(t,e):e.addEventListener("load",e=>{gn(t,e.target)})}),yn(t),t.initialized=!0,yn(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const n=this,{params:r,el:a,wrapperEl:l,slides:i}=n;return"undefined"===typeof n.params||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),r.loop&&n.loopDestroy(),t&&(n.removeClasses(),a&&"string"!==typeof a&&a.removeAttribute("style"),l&&l.removeAttribute("style"),i&&i.length&&i.forEach(e=>{e.classList.remove(r.slideVisibleClass,r.slideFullyVisibleClass,r.slideActiveClass,r.slideNextClass,r.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(e=>{n.off(e)}),!1!==e&&(n.el&&"string"!==typeof n.el&&(n.el.swiper=null),function(e){const t=e;Object.keys(t).forEach(e=>{try{t[e]=null}catch(Er){}try{delete t[e]}catch(Er){}})}(n)),n.destroyed=!0),null}static extendDefaults(e){$t(Gn,e)}static get extendedDefaults(){return Gn}static get defaults(){return Hn}static installModule(e){Wn.prototype.__modules__||(Wn.prototype.__modules__=[]);const t=Wn.prototype.__modules__;"function"===typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>Wn.installModule(e)),Wn):(Wn.installModule(e),Wn)}}Object.keys(Vn).forEach(e=>{Object.keys(Vn[e]).forEach(t=>{Wn.prototype[t]=Vn[e][t]})}),Wn.use([function(e){let{swiper:t,on:n,emit:r}=e;const a=Ht();let l=null,i=null;const o=()=>{t&&!t.destroyed&&t.initialized&&(r("beforeResize"),r("resize"))},s=()=>{t&&!t.destroyed&&t.initialized&&r("orientationchange")};n("init",()=>{t.params.resizeObserver&&"undefined"!==typeof a.ResizeObserver?t&&!t.destroyed&&t.initialized&&(l=new ResizeObserver(e=>{i=a.requestAnimationFrame(()=>{const{width:n,height:r}=t;let a=n,l=r;e.forEach(e=>{let{contentBoxSize:n,contentRect:r,target:i}=e;i&&i!==t.el||(a=r?r.width:(n[0]||n).inlineSize,l=r?r.height:(n[0]||n).blockSize)}),a===n&&l===r||o()})}),l.observe(t.el)):(a.addEventListener("resize",o),a.addEventListener("orientationchange",s))}),n("destroy",()=>{i&&a.cancelAnimationFrame(i),l&&l.unobserve&&t.el&&(l.unobserve(t.el),l=null),a.removeEventListener("resize",o),a.removeEventListener("orientationchange",s)})},function(e){let{swiper:t,extendParams:n,on:r,emit:a}=e;const l=[],i=Ht(),o=function(e,n){void 0===n&&(n={});const r=new(i.MutationObserver||i.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void a("observerUpdate",e[0]);const n=function(){a("observerUpdate",e[0])};i.requestAnimationFrame?i.requestAnimationFrame(n):i.setTimeout(n,0)});r.observe(e,{attributes:"undefined"===typeof n.attributes||n.attributes,childList:t.isElement||("undefined"===typeof n.childList||n).childList,characterData:"undefined"===typeof n.characterData||n.characterData}),l.push(r)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",()=>{if(t.params.observer){if(t.params.observeParents){const e=nn(t.hostEl);for(let t=0;t<e.length;t+=1)o(e[t])}o(t.hostEl,{childList:t.params.observeSlideChildren}),o(t.wrapperEl,{attributes:!1})}}),r("destroy",()=>{l.forEach(e=>{e.disconnect()}),l.splice(0,l.length)})}]);const qn=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function $n(e){return"object"===typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function Yn(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>n.indexOf(e)<0).forEach(n=>{"undefined"===typeof e[n]?e[n]=t[n]:$n(t[n])&&$n(e[n])&&Object.keys(t[n]).length>0?t[n].__swiper__?e[n]=t[n]:Yn(e[n],t[n]):e[n]=t[n]})}function Xn(e){return void 0===e&&(e={}),e.navigation&&"undefined"===typeof e.navigation.nextEl&&"undefined"===typeof e.navigation.prevEl}function Qn(e){return void 0===e&&(e={}),e.pagination&&"undefined"===typeof e.pagination.el}function Kn(e){return void 0===e&&(e={}),e.scrollbar&&"undefined"===typeof e.scrollbar.el}function Jn(e){void 0===e&&(e="");const t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),n=[];return t.forEach(e=>{n.indexOf(e)<0&&n.push(e)}),n.join(" ")}function Zn(e){return void 0===e&&(e=""),e?e.includes("swiper-wrapper")?e:"swiper-wrapper ".concat(e):"swiper-wrapper"}const er=["className","tag","wrapperTag","children","onSwiper"],tr=["tag","children","className","swiper","zoom","lazy","virtualIndex","swiperSlideIndex"];function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nr.apply(this,arguments)}function rr(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function ar(e){const t=[];return r.Children.toArray(e).forEach(e=>{rr(e)?t.push(e):e.props&&e.props.children&&ar(e.props.children).forEach(e=>t.push(e))}),t}function lr(e){const t=[],n={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return r.Children.toArray(e).forEach(e=>{if(rr(e))t.push(e);else if(e.props&&e.props.slot&&n[e.props.slot])n[e.props.slot].push(e);else if(e.props&&e.props.children){const r=ar(e.props.children);r.length>0?r.forEach(e=>t.push(e)):n["container-end"].push(e)}else n["container-end"].push(e)}),{slides:t,slots:n}}function ir(e,t){return"undefined"===typeof window?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}const or=(0,r.createContext)(null),sr=(0,r.createContext)(null),cr=(0,r.forwardRef)(function(e,t){let n=void 0===e?{}:e,{className:a,tag:i="div",wrapperTag:o="div",children:s,onSwiper:c}=n,d=l(n,er),p=!1;const[f,m]=(0,r.useState)("swiper"),[h,g]=(0,r.useState)(null),[v,y]=(0,r.useState)(!1),b=(0,r.useRef)(!1),w=(0,r.useRef)(null),S=(0,r.useRef)(null),x=(0,r.useRef)(null),k=(0,r.useRef)(null),E=(0,r.useRef)(null),C=(0,r.useRef)(null),T=(0,r.useRef)(null),P=(0,r.useRef)(null),{params:L,passedParams:N,rest:j,events:_}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);const n={on:{}},r={},a={};Yn(n,Hn),n._emitClasses=!0,n.init=!1;const l={},i=qn.map(e=>e.replace(/_/,"")),o=Object.assign({},e);return Object.keys(o).forEach(o=>{"undefined"!==typeof e[o]&&(i.indexOf(o)>=0?$n(e[o])?(n[o]={},a[o]={},Yn(n[o],e[o]),Yn(a[o],e[o])):(n[o]=e[o],a[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"===typeof e[o]?t?r["".concat(o[2].toLowerCase()).concat(o.substr(3))]=e[o]:n.on["".concat(o[2].toLowerCase()).concat(o.substr(3))]=e[o]:l[o]=e[o])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===n[e]&&(n[e]={}),!1===n[e]&&delete n[e]}),{params:n,passedParams:a,rest:l,events:r}}(d),{slides:O,slots:M}=lr(s),z=()=>{y(!v)};Object.assign(L.on,{_containerClasses(e,t){m(t)}});const A=()=>{Object.assign(L.on,_),p=!0;const e=u({},L);if(delete e.wrapperClass,S.current=new Wn(e),S.current.virtual&&S.current.params.virtual.enabled){S.current.virtual.slides=O;const e={cache:!1,slides:O,renderExternal:g,renderExternalUpdate:!1};Yn(S.current.params.virtual,e),Yn(S.current.originalParams.virtual,e)}};w.current||A(),S.current&&S.current.on("_beforeBreakpoint",z);return(0,r.useEffect)(()=>()=>{S.current&&S.current.off("_beforeBreakpoint",z)}),(0,r.useEffect)(()=>{!b.current&&S.current&&(S.current.emitSlidesClasses(),b.current=!0)}),ir(()=>{if(t&&(t.current=w.current),w.current)return S.current.destroyed&&A(),function(e,t){let{el:n,nextEl:r,prevEl:a,paginationEl:l,scrollbarEl:i,swiper:o}=e;Xn(t)&&r&&a&&(o.params.navigation.nextEl=r,o.originalParams.navigation.nextEl=r,o.params.navigation.prevEl=a,o.originalParams.navigation.prevEl=a),Qn(t)&&l&&(o.params.pagination.el=l,o.originalParams.pagination.el=l),Kn(t)&&i&&(o.params.scrollbar.el=i,o.originalParams.scrollbar.el=i),o.init(n)}({el:w.current,nextEl:E.current,prevEl:C.current,paginationEl:T.current,scrollbarEl:P.current,swiper:S.current},L),c&&!S.current.destroyed&&c(S.current),()=>{S.current&&!S.current.destroyed&&S.current.destroy(!0,!1)}},[]),ir(()=>{!p&&_&&S.current&&Object.keys(_).forEach(e=>{S.current.on(e,_[e])});const e=function(e,t,n,r,a){const l=[];if(!t)return l;const i=e=>{l.indexOf(e)<0&&l.push(e)};if(n&&r){const e=r.map(a),t=n.map(a);e.join("")!==t.join("")&&i("children"),r.length!==n.length&&i("children")}return qn.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(n=>{if(n in e&&n in t)if($n(e[n])&&$n(t[n])){const r=Object.keys(e[n]),a=Object.keys(t[n]);r.length!==a.length?i(n):(r.forEach(r=>{e[n][r]!==t[n][r]&&i(n)}),a.forEach(r=>{e[n][r]!==t[n][r]&&i(n)}))}else e[n]!==t[n]&&i(n)}),l}(N,x.current,O,k.current,e=>e.key);return x.current=N,k.current=O,e.length&&S.current&&!S.current.destroyed&&function(e){let{swiper:t,slides:n,passedParams:r,changedParams:a,nextEl:l,prevEl:i,scrollbarEl:o,paginationEl:s}=e;const c=a.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:u,pagination:d,navigation:p,scrollbar:f,virtual:m,thumbs:h}=t;let g,v,y,b,w,S,x,k;a.includes("thumbs")&&r.thumbs&&r.thumbs.swiper&&!r.thumbs.swiper.destroyed&&u.thumbs&&(!u.thumbs.swiper||u.thumbs.swiper.destroyed)&&(g=!0),a.includes("controller")&&r.controller&&r.controller.control&&u.controller&&!u.controller.control&&(v=!0),a.includes("pagination")&&r.pagination&&(r.pagination.el||s)&&(u.pagination||!1===u.pagination)&&d&&!d.el&&(y=!0),a.includes("scrollbar")&&r.scrollbar&&(r.scrollbar.el||o)&&(u.scrollbar||!1===u.scrollbar)&&f&&!f.el&&(b=!0),a.includes("navigation")&&r.navigation&&(r.navigation.prevEl||i)&&(r.navigation.nextEl||l)&&(u.navigation||!1===u.navigation)&&p&&!p.prevEl&&!p.nextEl&&(w=!0);const E=e=>{t[e]&&(t[e].destroy(),"navigation"===e?(t.isElement&&(t[e].prevEl.remove(),t[e].nextEl.remove()),u[e].prevEl=void 0,u[e].nextEl=void 0,t[e].prevEl=void 0,t[e].nextEl=void 0):(t.isElement&&t[e].el.remove(),u[e].el=void 0,t[e].el=void 0))};a.includes("loop")&&t.isElement&&(u.loop&&!r.loop?S=!0:!u.loop&&r.loop?x=!0:k=!0),c.forEach(e=>{if($n(u[e])&&$n(r[e]))Object.assign(u[e],r[e]),"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e||!("enabled"in r[e])||r[e].enabled||E(e);else{const t=r[e];!0!==t&&!1!==t||"navigation"!==e&&"pagination"!==e&&"scrollbar"!==e?u[e]=r[e]:!1===t&&E(e)}}),c.includes("controller")&&!v&&t.controller&&t.controller.control&&u.controller&&u.controller.control&&(t.controller.control=u.controller.control),a.includes("children")&&n&&m&&u.virtual.enabled?(m.slides=n,m.update(!0)):a.includes("virtual")&&m&&u.virtual.enabled&&(n&&(m.slides=n),m.update(!0)),a.includes("children")&&n&&u.loop&&(k=!0),g&&h.init()&&h.update(!0);v&&(t.controller.control=u.controller.control),y&&(!t.isElement||s&&"string"!==typeof s||(s=document.createElement("div"),s.classList.add("swiper-pagination"),s.part.add("pagination"),t.el.appendChild(s)),s&&(u.pagination.el=s),d.init(),d.render(),d.update()),b&&(!t.isElement||o&&"string"!==typeof o||(o=document.createElement("div"),o.classList.add("swiper-scrollbar"),o.part.add("scrollbar"),t.el.appendChild(o)),o&&(u.scrollbar.el=o),f.init(),f.updateSize(),f.setTranslate()),w&&(t.isElement&&(l&&"string"!==typeof l||(l=document.createElement("div"),l.classList.add("swiper-button-next"),ln(l,t.hostEl.constructor.nextButtonSvg),l.part.add("button-next"),t.el.appendChild(l)),i&&"string"!==typeof i||(i=document.createElement("div"),i.classList.add("swiper-button-prev"),ln(i,t.hostEl.constructor.prevButtonSvg),i.part.add("button-prev"),t.el.appendChild(i))),l&&(u.navigation.nextEl=l),i&&(u.navigation.prevEl=i),p.init(),p.update()),a.includes("allowSlideNext")&&(t.allowSlideNext=r.allowSlideNext),a.includes("allowSlidePrev")&&(t.allowSlidePrev=r.allowSlidePrev),a.includes("direction")&&t.changeDirection(r.direction,!1),(S||k)&&t.loopDestroy(),(x||k)&&t.loopCreate(),t.update()}({swiper:S.current,slides:O,passedParams:N,changedParams:e,nextEl:E.current,prevEl:C.current,scrollbarEl:P.current,paginationEl:T.current}),()=>{_&&S.current&&Object.keys(_).forEach(e=>{S.current.off(e,_[e])})}}),ir(()=>{(e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())})(S.current)},[h]),r.createElement(i,nr({ref:w,className:Jn("".concat(f).concat(a?" ".concat(a):""))},j),r.createElement(sr.Provider,{value:S.current},M["container-start"],r.createElement(o,{className:Zn(L.wrapperClass)},M["wrapper-start"],L.virtual?function(e,t,n){if(!n)return null;const a=e=>{let n=e;return e<0?n=t.length+e:n>=t.length&&(n-=t.length),n},l=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:"".concat(n.offset,"px")}:{top:"".concat(n.offset,"px")},{from:i,to:o}=n,s=e.params.loop?-t.length:0,c=e.params.loop?2*t.length:t.length,u=[];for(let r=s;r<c;r+=1)r>=i&&r<=o&&u.push(t[a(r)]);return u.map((t,n)=>r.cloneElement(t,{swiper:e,style:l,key:t.props.virtualIndex||t.key||"slide-".concat(n)}))}(S.current,O,h):O.map((e,t)=>r.cloneElement(e,{swiper:S.current,swiperSlideIndex:t})),M["wrapper-end"]),Xn(L)&&r.createElement(r.Fragment,null,r.createElement("div",{ref:C,className:"swiper-button-prev"}),r.createElement("div",{ref:E,className:"swiper-button-next"})),Kn(L)&&r.createElement("div",{ref:P,className:"swiper-scrollbar"}),Qn(L)&&r.createElement("div",{ref:T,className:"swiper-pagination"}),M["container-end"]))});cr.displayName="Swiper";const ur=(0,r.forwardRef)(function(e,t){let n=void 0===e?{}:e,{tag:a="div",children:i,className:o="",swiper:s,zoom:c,lazy:u,virtualIndex:d,swiperSlideIndex:p}=n,f=l(n,tr);const m=(0,r.useRef)(null),[h,g]=(0,r.useState)("swiper-slide"),[v,y]=(0,r.useState)(!1);function b(e,t,n){t===m.current&&g(n)}ir(()=>{if("undefined"!==typeof p&&(m.current.swiperSlideIndex=p),t&&(t.current=m.current),m.current&&s){if(!s.destroyed)return s.on("_slideClass",b),()=>{s&&s.off("_slideClass",b)};"swiper-slide"!==h&&g("swiper-slide")}}),ir(()=>{s&&m.current&&!s.destroyed&&g(s.getSlideClasses(m.current))},[s]);const w={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},S=()=>"function"===typeof i?i(w):i;return r.createElement(a,nr({ref:m,className:Jn("".concat(h).concat(o?" ".concat(o):"")),"data-swiper-slide-index":d,onLoad:()=>{y(!0)}},f),c&&r.createElement(or.Provider,{value:w},r.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"===typeof c?c:void 0},S(),u&&!v&&r.createElement("div",{className:"swiper-lazy-preloader"}))),!c&&r.createElement(or.Provider,{value:w},S(),u&&!v&&r.createElement("div",{className:"swiper-lazy-preloader"})))});function dr(e,t,n,r){return e.params.createElements&&Object.keys(r).forEach(a=>{if(!n[a]&&!0===n.auto){let l=Kt(e.el,".".concat(r[a]))[0];l||(l=Zt("div",r[a]),l.className=r[a],e.el.append(l)),n[a]=l,t[a]=l}}),n}function pr(e){let{swiper:t,extendParams:n,on:r,emit:a}=e;function l(e){let n;return e&&"string"===typeof e&&t.isElement&&(n=t.el.querySelector(e)||t.hostEl.querySelector(e),n)?n:(e&&("string"===typeof e&&(n=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"===typeof e&&n&&n.length>1&&1===t.el.querySelectorAll(e).length?n=t.el.querySelector(e):n&&1===n.length&&(n=n[0])),e&&!n?e:n)}function i(e,n){const r=t.params.navigation;(e=an(e)).forEach(e=>{e&&(e.classList[n?"add":"remove"](...r.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=n),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](r.lockClass))})}function o(){const{nextEl:e,prevEl:n}=t.navigation;if(t.params.loop)return i(n,!1),void i(e,!1);i(n,t.isBeginning&&!t.params.rewind),i(e,t.isEnd&&!t.params.rewind)}function s(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),a("navigationPrev"))}function c(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),a("navigationNext"))}function u(){const e=t.params.navigation;if(t.params.navigation=dr(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!e.nextEl&&!e.prevEl)return;let n=l(e.nextEl),r=l(e.prevEl);Object.assign(t.navigation,{nextEl:n,prevEl:r}),n=an(n),r=an(r);const a=(n,r)=>{n&&n.addEventListener("click","next"===r?c:s),!t.enabled&&n&&n.classList.add(...e.lockClass.split(" "))};n.forEach(e=>a(e,"next")),r.forEach(e=>a(e,"prev"))}function d(){let{nextEl:e,prevEl:n}=t.navigation;e=an(e),n=an(n);const r=(e,n)=>{e.removeEventListener("click","next"===n?c:s),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>r(e,"next")),n.forEach(e=>r(e,"prev"))}n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},r("init",()=>{!1===t.params.navigation.enabled?p():(u(),o())}),r("toEdge fromEdge lock unlock",()=>{o()}),r("destroy",()=>{d()}),r("enable disable",()=>{let{nextEl:e,prevEl:n}=t.navigation;e=an(e),n=an(n),t.enabled?o():[...e,...n].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),r("click",(e,n)=>{let{nextEl:r,prevEl:l}=t.navigation;r=an(r),l=an(l);const i=n.target;let o=l.includes(i)||r.includes(i);if(t.isElement&&!o){const e=n.path||n.composedPath&&n.composedPath();e&&(o=e.find(e=>r.includes(e)||l.includes(e)))}if(t.params.navigation.hideOnClick&&!o){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===i||t.pagination.el.contains(i)))return;let e;r.length?e=r[0].classList.contains(t.params.navigation.hiddenClass):l.length&&(e=l[0].classList.contains(t.params.navigation.hiddenClass)),a(!0===e?"navigationShow":"navigationHide"),[...r,...l].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});const p=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),d()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),u(),o()},disable:p,update:o,init:u,destroy:d})}function fr(e){return void 0===e&&(e=""),".".concat(e.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,"."))}function mr(e){let{swiper:t,extendParams:n,on:r,emit:a}=e;const l="swiper-pagination";let i;n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:"".concat(l,"-bullet"),bulletActiveClass:"".concat(l,"-bullet-active"),modifierClass:"".concat(l,"-"),currentClass:"".concat(l,"-current"),totalClass:"".concat(l,"-total"),hiddenClass:"".concat(l,"-hidden"),progressbarFillClass:"".concat(l,"-progressbar-fill"),progressbarOppositeClass:"".concat(l,"-progressbar-opposite"),clickableClass:"".concat(l,"-clickable"),lockClass:"".concat(l,"-lock"),horizontalClass:"".concat(l,"-horizontal"),verticalClass:"".concat(l,"-vertical"),paginationDisabledClass:"".concat(l,"-disabled")}}),t.pagination={el:null,bullets:[]};let o=0;function s(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&0===t.pagination.el.length}function c(e,n){const{bulletActiveClass:r}=t.params.pagination;e&&(e=e["".concat("prev"===n?"previous":"next","ElementSibling")])&&(e.classList.add("".concat(r,"-").concat(n)),(e=e["".concat("prev"===n?"previous":"next","ElementSibling")])&&e.classList.add("".concat(r,"-").concat(n,"-").concat(n)))}function u(e){const n=e.target.closest(fr(t.params.pagination.bulletClass));if(!n)return;e.preventDefault();const r=tn(n)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===r)return;const e=(a=t.realIndex,l=r,i=t.slides.length,(l%=i)===1+(a%=i)?"next":l===a-1?"previous":void 0);"next"===e?t.slideNext():"previous"===e?t.slidePrev():t.slideToLoop(r)}else t.slideTo(r);var a,l,i}function d(){const e=t.rtl,n=t.params.pagination;if(s())return;let r,l,u=t.pagination.el;u=an(u);const d=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,p=t.params.loop?Math.ceil(d/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(l=t.previousRealIndex||0,r=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):"undefined"!==typeof t.snapIndex?(r=t.snapIndex,l=t.previousSnapIndex):(l=t.previousIndex||0,r=t.activeIndex||0),"bullets"===n.type&&t.pagination.bullets&&t.pagination.bullets.length>0){const a=t.pagination.bullets;let s,d,p;if(n.dynamicBullets&&(i=rn(a[0],t.isHorizontal()?"width":"height",!0),u.forEach(e=>{e.style[t.isHorizontal()?"width":"height"]="".concat(i*(n.dynamicMainBullets+4),"px")}),n.dynamicMainBullets>1&&void 0!==l&&(o+=r-(l||0),o>n.dynamicMainBullets-1?o=n.dynamicMainBullets-1:o<0&&(o=0)),s=Math.max(r-o,0),d=s+(Math.min(a.length,n.dynamicMainBullets)-1),p=(d+s)/2),a.forEach(e=>{const t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>"".concat(n.bulletActiveClass).concat(e))].map(e=>"string"===typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),u.length>1)a.forEach(e=>{const a=tn(e);a===r?e.classList.add(...n.bulletActiveClass.split(" ")):t.isElement&&e.setAttribute("part","bullet"),n.dynamicBullets&&(a>=s&&a<=d&&e.classList.add(..."".concat(n.bulletActiveClass,"-main").split(" ")),a===s&&c(e,"prev"),a===d&&c(e,"next"))});else{const e=a[r];if(e&&e.classList.add(...n.bulletActiveClass.split(" ")),t.isElement&&a.forEach((e,t)=>{e.setAttribute("part",t===r?"bullet-active":"bullet")}),n.dynamicBullets){const e=a[s],t=a[d];for(let r=s;r<=d;r+=1)a[r]&&a[r].classList.add(..."".concat(n.bulletActiveClass,"-main").split(" "));c(e,"prev"),c(t,"next")}}if(n.dynamicBullets){const r=Math.min(a.length,n.dynamicMainBullets+4),l=(i*r-i)/2-p*i,o=e?"right":"left";a.forEach(e=>{e.style[t.isHorizontal()?o:"top"]="".concat(l,"px")})}}u.forEach((e,l)=>{if("fraction"===n.type&&(e.querySelectorAll(fr(n.currentClass)).forEach(e=>{e.textContent=n.formatFractionCurrent(r+1)}),e.querySelectorAll(fr(n.totalClass)).forEach(e=>{e.textContent=n.formatFractionTotal(p)})),"progressbar"===n.type){let a;a=n.progressbarOpposite?t.isHorizontal()?"vertical":"horizontal":t.isHorizontal()?"horizontal":"vertical";const l=(r+1)/p;let i=1,o=1;"horizontal"===a?i=l:o=l,e.querySelectorAll(fr(n.progressbarFillClass)).forEach(e=>{e.style.transform="translate3d(0,0,0) scaleX(".concat(i,") scaleY(").concat(o,")"),e.style.transitionDuration="".concat(t.params.speed,"ms")})}"custom"===n.type&&n.renderCustom?(ln(e,n.renderCustom(t,r+1,p)),0===l&&a("paginationRender",e)):(0===l&&a("paginationRender",e),a("paginationUpdate",e)),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](n.lockClass)})}function p(){const e=t.params.pagination;if(s())return;const n=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let r=t.pagination.el;r=an(r);let l="";if("bullets"===e.type){let r=t.params.loop?Math.ceil(n/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&r>n&&(r=n);for(let n=0;n<r;n+=1)e.renderBullet?l+=e.renderBullet.call(t,n,e.bulletClass):l+="<".concat(e.bulletElement," ").concat(t.isElement?'part="bullet"':"",' class="').concat(e.bulletClass,'"></').concat(e.bulletElement,">")}"fraction"===e.type&&(l=e.renderFraction?e.renderFraction.call(t,e.currentClass,e.totalClass):'<span class="'.concat(e.currentClass,'"></span>')+" / "+'<span class="'.concat(e.totalClass,'"></span>')),"progressbar"===e.type&&(l=e.renderProgressbar?e.renderProgressbar.call(t,e.progressbarFillClass):'<span class="'.concat(e.progressbarFillClass,'"></span>')),t.pagination.bullets=[],r.forEach(n=>{"custom"!==e.type&&ln(n,l||""),"bullets"===e.type&&t.pagination.bullets.push(...n.querySelectorAll(fr(e.bulletClass)))}),"custom"!==e.type&&a("paginationRender",r[0])}function f(){t.params.pagination=dr(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const e=t.params.pagination;if(!e.el)return;let n;"string"===typeof e.el&&t.isElement&&(n=t.el.querySelector(e.el)),n||"string"!==typeof e.el||(n=[...document.querySelectorAll(e.el)]),n||(n=e.el),n&&0!==n.length&&(t.params.uniqueNavElements&&"string"===typeof e.el&&Array.isArray(n)&&n.length>1&&(n=[...t.el.querySelectorAll(e.el)],n.length>1&&(n=n.find(e=>nn(e,".swiper")[0]===t.el))),Array.isArray(n)&&1===n.length&&(n=n[0]),Object.assign(t.pagination,{el:n}),n=an(n),n.forEach(n=>{"bullets"===e.type&&e.clickable&&n.classList.add(...(e.clickableClass||"").split(" ")),n.classList.add(e.modifierClass+e.type),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(n.classList.add("".concat(e.modifierClass).concat(e.type,"-dynamic")),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&n.classList.add(e.progressbarOppositeClass),e.clickable&&n.addEventListener("click",u),t.enabled||n.classList.add(e.lockClass)}))}function m(){const e=t.params.pagination;if(s())return;let n=t.pagination.el;n&&(n=an(n),n.forEach(n=>{n.classList.remove(e.hiddenClass),n.classList.remove(e.modifierClass+e.type),n.classList.remove(t.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(n.classList.remove(...(e.clickableClass||"").split(" ")),n.removeEventListener("click",u))})),t.pagination.bullets&&t.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const e=t.params.pagination;let{el:n}=t.pagination;n=an(n),n.forEach(n=>{n.classList.remove(e.horizontalClass,e.verticalClass),n.classList.add(t.isHorizontal()?e.horizontalClass:e.verticalClass)})}),r("init",()=>{!1===t.params.pagination.enabled?h():(f(),p(),d())}),r("activeIndexChange",()=>{"undefined"===typeof t.snapIndex&&d()}),r("snapIndexChange",()=>{d()}),r("snapGridLengthChange",()=>{p(),d()}),r("destroy",()=>{m()}),r("enable disable",()=>{let{el:e}=t.pagination;e&&(e=an(e),e.forEach(e=>e.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),r("lock unlock",()=>{d()}),r("click",(e,n)=>{const r=n.target,l=an(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&l&&l.length>0&&!r.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&r===t.navigation.nextEl||t.navigation.prevEl&&r===t.navigation.prevEl))return;const e=l[0].classList.contains(t.params.pagination.hiddenClass);a(!0===e?"paginationShow":"paginationHide"),l.forEach(e=>e.classList.toggle(t.params.pagination.hiddenClass))}});const h=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=an(e),e.forEach(e=>e.classList.add(t.params.pagination.paginationDisabledClass))),m()};Object.assign(t.pagination,{enable:()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:e}=t.pagination;e&&(e=an(e),e.forEach(e=>e.classList.remove(t.params.pagination.paginationDisabledClass))),f(),p(),d()},disable:h,render:p,update:d,init:f,destroy:m})}function hr(e){let t,n,{swiper:r,extendParams:a,on:l,emit:i,params:o}=e;r.autoplay={running:!1,paused:!1,timeLeft:0},a({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let s,c,u,d,p,f,m,h,g=o&&o.autoplay?o.autoplay.delay:3e3,v=o&&o.autoplay?o.autoplay.delay:3e3,y=(new Date).getTime();function b(e){r&&!r.destroyed&&r.wrapperEl&&e.target===r.wrapperEl&&(r.wrapperEl.removeEventListener("transitionend",b),h||e.detail&&e.detail.bySwiperTouchMove||C())}const w=()=>{if(r.destroyed||!r.autoplay.running)return;r.autoplay.paused?c=!0:c&&(v=s,c=!1);const e=r.autoplay.paused?s:y+v-(new Date).getTime();r.autoplay.timeLeft=e,i("autoplayTimeLeft",e,e/g),n=requestAnimationFrame(()=>{w()})},S=e=>{if(r.destroyed||!r.autoplay.running)return;cancelAnimationFrame(n),w();let a="undefined"===typeof e?r.params.autoplay.delay:e;g=r.params.autoplay.delay,v=r.params.autoplay.delay;const l=(()=>{let e;if(e=r.virtual&&r.params.virtual.enabled?r.slides.find(e=>e.classList.contains("swiper-slide-active")):r.slides[r.activeIndex],!e)return;return parseInt(e.getAttribute("data-swiper-autoplay"),10)})();!Number.isNaN(l)&&l>0&&"undefined"===typeof e&&(a=l,g=l,v=l),s=a;const o=r.params.speed,c=()=>{r&&!r.destroyed&&(r.params.autoplay.reverseDirection?!r.isBeginning||r.params.loop||r.params.rewind?(r.slidePrev(o,!0,!0),i("autoplay")):r.params.autoplay.stopOnLastSlide||(r.slideTo(r.slides.length-1,o,!0,!0),i("autoplay")):!r.isEnd||r.params.loop||r.params.rewind?(r.slideNext(o,!0,!0),i("autoplay")):r.params.autoplay.stopOnLastSlide||(r.slideTo(0,o,!0,!0),i("autoplay")),r.params.cssMode&&(y=(new Date).getTime(),requestAnimationFrame(()=>{S()})))};return a>0?(clearTimeout(t),t=setTimeout(()=>{c()},a)):requestAnimationFrame(()=>{c()}),a},x=()=>{y=(new Date).getTime(),r.autoplay.running=!0,S(),i("autoplayStart")},k=()=>{r.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(n),i("autoplayStop")},E=(e,n)=>{if(r.destroyed||!r.autoplay.running)return;clearTimeout(t),e||(m=!0);const a=()=>{i("autoplayPause"),r.params.autoplay.waitForTransition?r.wrapperEl.addEventListener("transitionend",b):C()};if(r.autoplay.paused=!0,n)return f&&(s=r.params.autoplay.delay),f=!1,void a();const l=s||r.params.autoplay.delay;s=l-((new Date).getTime()-y),r.isEnd&&s<0&&!r.params.loop||(s<0&&(s=0),a())},C=()=>{r.isEnd&&s<0&&!r.params.loop||r.destroyed||!r.autoplay.running||(y=(new Date).getTime(),m?(m=!1,S(s)):S(),r.autoplay.paused=!1,i("autoplayResume"))},T=()=>{if(r.destroyed||!r.autoplay.running)return;const e=Ft();"hidden"===e.visibilityState&&(m=!0,E(!0)),"visible"===e.visibilityState&&C()},P=e=>{"mouse"===e.pointerType&&(m=!0,h=!0,r.animating||r.autoplay.paused||E(!0))},L=e=>{"mouse"===e.pointerType&&(h=!1,r.autoplay.paused&&C())};l("init",()=>{r.params.autoplay.enabled&&(r.params.autoplay.pauseOnMouseEnter&&(r.el.addEventListener("pointerenter",P),r.el.addEventListener("pointerleave",L)),Ft().addEventListener("visibilitychange",T),x())}),l("destroy",()=>{r.el&&"string"!==typeof r.el&&(r.el.removeEventListener("pointerenter",P),r.el.removeEventListener("pointerleave",L)),Ft().removeEventListener("visibilitychange",T),r.autoplay.running&&k()}),l("_freeModeStaticRelease",()=>{(d||m)&&C()}),l("_freeModeNoMomentumRelease",()=>{r.params.autoplay.disableOnInteraction?k():E(!0,!0)}),l("beforeTransitionStart",(e,t,n)=>{!r.destroyed&&r.autoplay.running&&(n||!r.params.autoplay.disableOnInteraction?E(!0,!0):k())}),l("sliderFirstMove",()=>{!r.destroyed&&r.autoplay.running&&(r.params.autoplay.disableOnInteraction?k():(u=!0,d=!1,m=!1,p=setTimeout(()=>{m=!0,d=!0,E(!0)},200)))}),l("touchEnd",()=>{if(!r.destroyed&&r.autoplay.running&&u){if(clearTimeout(p),clearTimeout(t),r.params.autoplay.disableOnInteraction)return d=!1,void(u=!1);d&&r.params.cssMode&&C(),d=!1,u=!1}}),l("slideChange",()=>{!r.destroyed&&r.autoplay.running&&(f=!0)}),Object.assign(r.autoplay,{start:x,stop:k,pause:E,resume:C})}function gr(e,t){const n=Qt(t);return n!==t&&(n.style.backfaceVisibility="hidden",n.style["-webkit-backface-visibility"]="hidden"),n}function vr(e){let{swiper:t,duration:n,transformElements:r,allSlides:a}=e;const{activeIndex:l}=t;if(t.params.virtualTranslate&&0!==n){let e,n=!1;e=a?r:r.filter(e=>{const n=e.classList.contains("swiper-slide-transform")?(e=>{if(!e.parentElement)return t.slides.find(t=>t.shadowRoot&&t.shadowRoot===e.parentNode);return e.parentElement})(e):e;return t.getSlideIndex(n)===l}),e.forEach(e=>{!function(e,t){t&&e.addEventListener("transitionend",function n(r){r.target===e&&(t.call(e,r),e.removeEventListener("transitionend",n))})}(e,()=>{if(n)return;if(!t||t.destroyed)return;n=!0,t.animating=!1;const e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(e)})})}}function yr(e){let{swiper:t,extendParams:n,on:r}=e;n({fadeEffect:{crossFade:!1}});!function(e){const{effect:t,swiper:n,on:r,setTranslate:a,setTransition:l,overwriteParams:i,perspective:o,recreateShadows:s,getEffectParams:c}=e;let u;r("beforeInit",()=>{if(n.params.effect!==t)return;n.classNames.push("".concat(n.params.containerModifierClass).concat(t)),o&&o()&&n.classNames.push("".concat(n.params.containerModifierClass,"3d"));const e=i?i():{};Object.assign(n.params,e),Object.assign(n.originalParams,e)}),r("setTranslate _virtualUpdated",()=>{n.params.effect===t&&a()}),r("setTransition",(e,r)=>{n.params.effect===t&&l(r)}),r("transitionEnd",()=>{if(n.params.effect===t&&s){if(!c||!c().slideShadows)return;n.slides.forEach(e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(e=>e.remove())}),s()}}),r("virtualUpdate",()=>{n.params.effect===t&&(n.slides.length||(u=!0),requestAnimationFrame(()=>{u&&n.slides&&n.slides.length&&(a(),u=!1)}))})}({effect:"fade",swiper:t,on:r,setTranslate:()=>{const{slides:e}=t;t.params.fadeEffect;for(let n=0;n<e.length;n+=1){const e=t.slides[n];let r=-e.swiperSlideOffset;t.params.virtualTranslate||(r-=t.translate);let a=0;t.isHorizontal()||(a=r,r=0);const l=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(e.progress),0):1+Math.min(Math.max(e.progress,-1),0),i=gr(0,e);i.style.opacity=l,i.style.transform="translate3d(".concat(r,"px, ").concat(a,"px, 0px)")}},setTransition:e=>{const n=t.slides.map(e=>Qt(e));n.forEach(t=>{t.style.transitionDuration="".concat(e,"ms")}),vr({swiper:t,duration:e,transformElements:n,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}ur.displayName="SwiperSlide";const br=e=>{let{images:t,title:n,isNDA:a=!1}=e;const[l,i]=(0,r.useState)(!1),[o,s]=(0,r.useState)(0);(0,r.useEffect)(()=>{const e=e=>{"Escape"===e.key&&l&&i(!1)};return l?(document.addEventListener("keydown",e),document.body.style.overflow="hidden"):document.body.style.overflow="unset",()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[l]);const c=()=>{i(!1)};return(0,xt.jsxs)(xt.Fragment,{children:[(0,xt.jsx)("div",{className:"project-image-swiper",children:(0,xt.jsxs)(cr,{modules:[pr,mr,hr,yr],spaceBetween:0,slidesPerView:1,navigation:{nextEl:".swiper-button-next-custom",prevEl:".swiper-button-prev-custom"},pagination:{clickable:!0,dynamicBullets:!0},autoplay:{delay:4e3,disableOnInteraction:!1,pauseOnMouseEnter:!0},effect:"fade",fadeEffect:{crossFade:!0},loop:t.length>1,className:"project-swiper",children:[t.map((e,t)=>(0,xt.jsx)(ur,{children:(0,xt.jsxs)("div",{className:"swiper-slide-content",children:[(0,xt.jsx)("img",{src:e,alt:"".concat(n," - View ").concat(t+1),className:"swiper-image"}),(0,xt.jsx)("div",{className:"fullscreen-icon",onClick:e=>{e.stopPropagation(),s(t),i(!0)},title:"View Fullscreen",children:(0,xt.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,xt.jsx)("path",{d:"M7 14H5V19H10V17H7V14Z",fill:"currentColor"}),(0,xt.jsx)("path",{d:"M5 10H7V7H10V5H5V10Z",fill:"currentColor"}),(0,xt.jsx)("path",{d:"M17 14H19V19H14V17H17V14Z",fill:"currentColor"}),(0,xt.jsx)("path",{d:"M14 5V7H17V10H19V5H14Z",fill:"currentColor"})]})})]})},t)),t.length>1&&(0,xt.jsxs)(xt.Fragment,{children:[(0,xt.jsx)("div",{className:"swiper-button-prev-custom",children:(0,xt.jsx)("span",{children:"\u2039"})}),(0,xt.jsx)("div",{className:"swiper-button-next-custom",children:(0,xt.jsx)("span",{children:"\u203a"})})]}),t.length>1&&(0,xt.jsxs)("div",{className:"swipe-indicator",children:[(0,xt.jsx)("span",{className:"swipe-text",children:"Swipe"}),(0,xt.jsxs)("div",{className:"swipe-animation",children:[(0,xt.jsx)("div",{className:"swipe-dot"}),(0,xt.jsx)("div",{className:"swipe-dot"}),(0,xt.jsx)("div",{className:"swipe-dot"})]})]})]})}),l&&(0,At.createPortal)((0,xt.jsx)("div",{className:"fullscreen-modal",onClick:e=>{e.target===e.currentTarget&&c()},children:(0,xt.jsxs)("div",{className:"fullscreen-content",children:[(0,xt.jsx)("button",{className:"fullscreen-close",onClick:c,title:"Close (Esc)",children:(0,xt.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,xt.jsx)("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,xt.jsx)("img",{src:t[o],alt:"".concat(n," - Fullscreen View"),className:"fullscreen-image"}),t.length>1&&(0,xt.jsxs)("div",{className:"fullscreen-navigation",children:[(0,xt.jsx)("button",{className:"fullscreen-nav-btn fullscreen-prev",onClick:e=>{e.stopPropagation(),s(e=>0===e?t.length-1:e-1)},title:"Previous Image",children:(0,xt.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,xt.jsx)("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),(0,xt.jsx)("button",{className:"fullscreen-nav-btn fullscreen-next",onClick:e=>{e.stopPropagation(),s(e=>e===t.length-1?0:e+1)},title:"Next Image",children:(0,xt.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,xt.jsx)("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),(0,xt.jsxs)("div",{className:"fullscreen-counter",children:[o+1," / ",t.length]})]})}),document.body)]})},wr=e=>{let{isOpen:t,onClose:n,projectTitle:a}=e;return(0,r.useEffect)(()=>{const e=e=>{"Escape"===e.key&&n()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,n]),t?(0,xt.jsx)("div",{className:"nda-overlay",onClick:n,children:(0,xt.jsxs)("div",{className:"nda-content",onClick:e=>e.stopPropagation(),children:[(0,xt.jsx)("div",{className:"nda-icon",children:(0,xt.jsx)("span",{children:"\ud83d\udd12"})}),(0,xt.jsx)("h2",{className:"nda-title",children:"Project Under NDA"}),(0,xt.jsx)("p",{className:"nda-message",children:"This project is protected under a Non-Disclosure Agreement (NDA). Due to confidentiality requirements, detailed information and live demos cannot be shared publicly."}),(0,xt.jsxs)("div",{className:"nda-details",children:[(0,xt.jsx)("h3",{children:"What I can share:"}),(0,xt.jsxs)("ul",{children:[(0,xt.jsx)("li",{children:"General technical scope and technologies used"}),(0,xt.jsx)("li",{children:"My role and contributions to the project"}),(0,xt.jsx)("li",{children:"Skills and methodologies applied"}),(0,xt.jsx)("li",{children:"Overall project outcomes and achievements"})]})]}),(0,xt.jsx)("div",{className:"nda-contact",children:(0,xt.jsx)("p",{children:"For more information about this project, please contact me directly."})}),(0,xt.jsx)("button",{className:"nda-close-btn",onClick:n,children:(0,xt.jsx)("span",{children:"Understood"})})]})}):null},Sr=()=>{const{slug:e}=he(),t=Lt.find(t=>t.slug===e),[n,a]=(0,r.useState)({isOpen:!1,projectTitle:""}),l=e=>e.description.toLowerCase().includes("nda")||e.title.toLowerCase().includes("nda")||e.images.some(e=>e.includes("NDA"));return t?(0,xt.jsxs)("div",{children:[(0,xt.jsx)(kt,{}),(0,xt.jsx)("div",{className:"back-navigation",children:(0,xt.jsxs)(ht,{to:"/#experience",className:"back-button",children:[(0,xt.jsx)("span",{className:"back-arrow",children:"\u2190"}),(0,xt.jsx)("span",{children:"Back to Timeline"})]})}),(0,xt.jsx)("section",{className:"job-hero",children:(0,xt.jsxs)("div",{className:"job-hero-content",children:[(0,xt.jsxs)("div",{className:"company-branding",children:[(0,xt.jsx)("img",{src:t.logo,alt:t.logoAlt,className:"hero-company-logo"}),(0,xt.jsxs)("div",{className:"company-info",children:[(0,xt.jsx)("h1",{className:"job-title-hero",children:t.title}),(0,xt.jsx)("h2",{className:"company-name-hero",children:t.company}),t.companyLink&&(0,xt.jsx)("p",{className:"company-link-hero",children:(0,xt.jsx)("a",{href:t.companyLink,target:"_blank",rel:"noopener noreferrer",children:t.companyLink})}),(0,xt.jsx)("p",{className:"job-duration-hero",children:t.duration})]})]}),(0,xt.jsx)("div",{className:"job-summary",children:(0,xt.jsx)("p",{children:t.summary})})]})}),(0,xt.jsx)("section",{className:"job-content",children:(0,xt.jsxs)("div",{className:"content-grid",children:[(0,xt.jsxs)("div",{className:"content-card",children:[(0,xt.jsx)("h3",{children:"Role Overview"}),(0,xt.jsx)("p",{children:t.roleOverview}),(0,xt.jsx)("h4",{children:"Key Responsibilities"}),(0,xt.jsx)("ul",{children:t.responsibilities.map((e,t)=>(0,xt.jsx)("li",{children:e},t))})]}),(0,xt.jsxs)("div",{className:"content-card",children:[(0,xt.jsx)("h3",{children:"Technologies & Skills"}),(0,xt.jsx)("div",{className:"skills-grid",children:Object.entries(t.skills).map(e=>{let[t,n]=e;const r=t.toLowerCase().replace(/[^a-z0-9]/g,"_")+"_skills";return(0,xt.jsxs)("div",{className:"skill-category ".concat(r),children:[(0,xt.jsx)("h4",{children:t}),(0,xt.jsx)("div",{className:"skill-tags",children:n.map((e,t)=>(0,xt.jsx)("span",{className:"skill-tag",children:e},t))})]},t)})})]}),(0,xt.jsxs)("div",{className:"content-card",children:[(0,xt.jsx)("h3",{children:"Key Accomplishments"}),(0,xt.jsx)("div",{className:"accomplishments-list",children:t.accomplishments.map((e,t)=>(0,xt.jsxs)("div",{className:"accomplishment-item",children:[(0,xt.jsx)("div",{className:"metric",children:e.metric}),(0,xt.jsx)("div",{className:"metric-description",children:e.description})]},t))})]})]})}),(0,xt.jsxs)("section",{className:"role-projects",children:[(0,xt.jsx)("h2",{children:"Projects from this Role"}),(0,xt.jsx)("div",{className:"projects-grid",children:t.projects.map((e,t)=>(0,xt.jsxs)("div",{className:"project-card",children:[(0,xt.jsx)("div",{className:"project-image",children:(0,xt.jsx)(br,{images:e.images,title:e.title,isNDA:l(e)})}),(0,xt.jsxs)("div",{className:"project-info",onClick:t=>((e,t)=>{e.stopPropagation(),l(t)?a({isOpen:!0,projectTitle:t.title}):window.open(t.liveUrl,"_blank")})(t,e),style:{cursor:"pointer"},children:[(0,xt.jsx)("h3",{children:e.title}),(0,xt.jsx)("p",{children:e.description}),(0,xt.jsx)("div",{className:"project-tech",children:e.technologies.map((e,t)=>(0,xt.jsx)("span",{children:e},t))}),e.liveUrl&&(0,xt.jsx)("div",{className:"project-link",children:(0,xt.jsx)("span",{children:l(e)?"Click to view NDA info \u2192":"Click to view project \u2192"})})]})]},t))})]}),(0,xt.jsx)(wr,{isOpen:n.isOpen,onClose:()=>{a({isOpen:!1,projectTitle:""})},projectTitle:n.projectTitle}),(0,xt.jsx)(Mt,{})]}):(0,xt.jsxs)("div",{children:[(0,xt.jsx)(kt,{}),(0,xt.jsxs)("div",{style:{padding:"100px 20px",textAlign:"center",color:"white"},children:[(0,xt.jsx)("h1",{children:"Job Not Found"}),(0,xt.jsx)(ht,{to:"/",style:{color:"#4B0082"},children:"\u2190 Back to Home"})]}),(0,xt.jsx)(Mt,{})]})};const xr=function(){return(0,xt.jsx)(ft,{children:(0,xt.jsx)("div",{className:"App",children:(0,xt.jsxs)(Oe,{children:[(0,xt.jsx)(je,{path:"/",element:(0,xt.jsx)(zt,{})}),(0,xt.jsx)(je,{path:"/job/:slug",element:(0,xt.jsx)(Sr,{})})]})})})},kr=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then(t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:l,getTTFB:i}=t;n(e),r(e),a(e),l(e),i(e)})};a.createRoot(document.getElementById("root")).render((0,xt.jsx)(r.StrictMode,{children:(0,xt.jsx)(xr,{})})),kr()})();
//# sourceMappingURL=main.c61ad48c.js.map