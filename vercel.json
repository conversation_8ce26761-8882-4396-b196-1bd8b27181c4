{"version": 2, "name": "med-amine-chouchane-portfolio", "buildCommand": "cd portfolio-react && npm ci && npm run build", "outputDirectory": "portfolio-react/build", "installCommand": "cd portfolio-react && npm ci", "framework": "create-react-app", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}